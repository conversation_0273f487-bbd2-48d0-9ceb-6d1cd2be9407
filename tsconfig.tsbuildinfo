{"root": ["./src/App.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/Components/Common/AccessibleImage.tsx", "./src/Components/Common/index.ts", "./src/Components/Common/DocDetailCard/index.tsx", "./src/Components/Common/HistoryCard/index.tsx", "./src/Components/Common/MainLoader/index.tsx", "./src/Components/Common/Popover/index.tsx", "./src/Components/Layout/index.ts", "./src/Components/Layout/ContentLayout/index.tsx", "./src/Components/Layout/MainLayout/MenuItem.tsx", "./src/Components/Layout/MainLayout/Sidebar.tsx", "./src/Components/Layout/MainLayout/index.tsx", "./src/Components/Layout/ProfileLayout/index.tsx", "./src/Components/Layout/RegisterLayout/index.tsx", "./src/Components/Recorder/index.tsx", "./src/Components/UI/index.ts", "./src/Components/UI/AccessibleImage/index.tsx", "./src/Components/UI/Accordion/index.tsx", "./src/Components/UI/Breadcrumb/index.tsx", "./src/Components/UI/Button/index.tsx", "./src/Components/UI/Checkbox/index.tsx", "./src/Components/UI/InputField/index.tsx", "./src/Components/UI/InputSelect/index.tsx", "./src/Components/UI/Modal/index.tsx", "./src/Components/UI/Switch/index.tsx", "./src/Components/UI/Toast/ToastProvider.tsx", "./src/Components/UI/Toast/index.tsx", "./src/Config/Path.Config.ts", "./src/Config/Routes.Config.ts", "./src/Config/firebaseConfig.ts", "./src/Helpers/Api.ts", "./src/Helpers/CacheKeys.ts", "./src/Helpers/Constants.ts", "./src/Helpers/Paths.ts", "./src/Helpers/PhoneCountryCodes.ts", "./src/Helpers/QueryClient.ts", "./src/Helpers/Roleguard.tsx", "./src/Helpers/StatusCodes.ts", "./src/Helpers/Utils.ts", "./src/Hooks/useAppDispatch.ts", "./src/Hooks/useDebouce.ts", "./src/Hooks/useScreenScale.ts", "./src/Icons/index.ts", "./src/Icons/AdminIcon/index.tsx", "./src/Icons/AttachmentIcon/index.tsx", "./src/Icons/BackArrowIcon/index.tsx", "./src/Icons/BellIcon/index.tsx", "./src/Icons/BellRingingIcon/index.tsx", "./src/Icons/CancelIcon/index.tsx", "./src/Icons/CaretRightIcon/index.tsx", "./src/Icons/ChartLineUpIcon/index.tsx", "./src/Icons/CircleTickIcon/index.tsx", "./src/Icons/ClockCounterwiseIcon/index.tsx", "./src/Icons/ClockIcon/index.tsx", "./src/Icons/CloseIcon/index.tsx", "./src/Icons/DatabaseIcon/index.tsx", "./src/Icons/DeleteAccountIcon/index.tsx", "./src/Icons/DeleteIcon/index.tsx", "./src/Icons/DownloadIcon/index.tsx", "./src/Icons/ErrorIcon/index.tsx", "./src/Icons/FileIcon/index.tsx", "./src/Icons/HouseIcon/index.tsx", "./src/Icons/InfoIcon/index.tsx", "./src/Icons/KeyIcon/index.tsx", "./src/Icons/Loader/index.tsx", "./src/Icons/LogoutIcon/index.tsx", "./src/Icons/MicIcon/index.tsx", "./src/Icons/MicOffIcon/index.tsx", "./src/Icons/NewChatIcon/index.tsx", "./src/Icons/NotepadIcon/index.tsx", "./src/Icons/PdfIcon/index.tsx", "./src/Icons/PhoneCallIcon/index.tsx", "./src/Icons/PurchaseHistoryIcon/index.tsx", "./src/Icons/QuestionsIcon/index.tsx", "./src/Icons/RoboIcon/index.tsx", "./src/Icons/SearchIcon/index.tsx", "./src/Icons/SimpleTickIcon/index.tsx", "./src/Icons/ThreeDotIcon/index.tsx", "./src/Icons/UploadIcon/index.tsx", "./src/Icons/UserCircleIcon/index.tsx", "./src/Icons/UserCircleMinusIcon/index.tsx", "./src/Icons/UserCirclePlusIcon/index.tsx", "./src/Icons/VideoIcon/index.tsx", "./src/LanguageProvider/i18n.ts", "./src/Pages/index.ts", "./src/Pages/AboutUs/index.tsx", "./src/Pages/AboutUsPublic/index.tsx", "./src/Pages/ContactUs/index.tsx", "./src/Pages/Forms&Documents/DetailsOfDocument.tsx", "./src/Pages/Forms&Documents/index.tsx", "./src/Pages/History/index.tsx", "./src/Pages/Home/index.tsx", "./src/Pages/LicenseCheck/index.tsx", "./src/Pages/Login/index.tsx", "./src/Pages/NotificationSettings/index.tsx", "./src/Pages/PageNotFound/index.tsx", "./src/Pages/PerformanceSolution/index.tsx", "./src/Pages/PrivacyPolicy/index.tsx", "./src/Pages/PrivacyPolicyPublic/index.tsx", "./src/Pages/Profile/index.tsx", "./src/Pages/Register/index.tsx", "./src/Pages/Subscribe/SalesContactForm.tsx", "./src/Pages/Subscribe/index.tsx", "./src/Pages/TermsConditions/index.tsx", "./src/Pages/TermsConditionsPublic/index.tsx", "./src/Pages/VerifyOtp/index.tsx", "./src/Query/Hooks/useAdmin.ts", "./src/Query/Hooks/useAuth.ts", "./src/Query/Hooks/usePosts.ts", "./src/Query/Services/admin.service.ts", "./src/Query/Services/auth.service.ts", "./src/Query/Services/posts.ts", "./src/Redux/Reducers.ts", "./src/Redux/store.ts", "./src/Redux/General/Toast.ts", "./src/Redux/SystemControl/PostControl.ts", "./src/Redux/SystemControl/index.ts"], "version": "5.6.3"}