{
  "parser": "@typescript-eslint/parser",
  "extends": [
    "airbnb/base",
    "plugin:@typescript-eslint/recommended",
    "plugin:import/errors",
    "plugin:import/warnings",
    "plugin:import/typescript",
    "prettier",
  ],
  "parserOptions": {
    "ecmaVersion": 2018,
    "project": "./tsconfig.json",
  },
  "plugins": ["prettier"],
  "rules": {
    "prettier/prettier": ["error"],
    "semi": ["error", "always"],
    "object-curly-spacing": ["error", "always"],
    "camelcase": "off",
    "@typescript-eslint/explicit-function-return-type": "off",
    "@typescript-eslint/no-explicit-any": 1,
    "@typescript-eslint/no-inferrable-types": [
      "warn",
      {
        "ignoreParameters": true,
      },
    ],
    "no-underscore-dangle": "off",
    "no-shadow": "off",
    "no-new": 0,
    "@typescript-eslint/no-shadow": ["error"],
    "@typescript-eslint/no-unused-vars": "warn",
    "quotes": [2, "single", { "avoidEscape": true }],
    "class-methods-use-this": "off",
    "import/extensions": [
      "error",
      "ignorePackages",
      {
        "js": "never",
        "jsx": "never",
        "ts": "never",
        "tsx": "never",
      },
    ],
  },
}
