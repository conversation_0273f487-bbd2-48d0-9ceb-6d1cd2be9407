{
  "compilerOptions": {
    "target": "ES2020",
    "module": "ES2020", // Added for import.meta support
    "lib": ["ES2020", "DOM", "DOM.Iterable"], // Added for modern features
    "moduleResolution": "bundler", // Added for Vite

    // Your existing settings
    "baseUrl": ".",
    "jsx": "react-jsx",
    "resolveJsonModule": true,
    "esModuleInterop": true,

    // Path aliases (preserved from your config)
    "paths": {
      "@Components/*": ["src/Components/*"],
      "@LanguageProvider/*": ["src/LanguageProvider/*"],
      "@Assets/*": ["src/Assets/*"],
      "@Helpers/*": ["src/Helpers/*"],
      "@Hooks/*": ["src/Hooks/*"],
      "@Query/*": ["src/Query/*"],
      "@Redux/*": ["src/Redux/*"],
      "@Routes/*": ["src/Routes/*"],
      "@Config/*": ["src/Config/*"],
      "@Pages": ["src/Pages"],
      "@Icons": ["src/Icons"]
    },

    // Additional recommended settings for Vite + TypeScript
    "skipLibCheck": true,
    "noEmit": true,
    "isolatedModules": true,
    "strict": true,

    // Types (including both node and vite)
    "types": ["node", "vite/client"]
  },
  "include": ["src/**/*", "postcss.config.js"]
}
