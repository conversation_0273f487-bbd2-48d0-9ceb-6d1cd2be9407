@import url('https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100..900;1,100..900&display=swap');
@import 'tailwindcss';

@theme {
  --color-primary-100: #FF8800;
  --color-primary-0: #FFA033;
  --shadow-primary: 0 2px 6px rgba(0, 0, 0, 0.1), 0 0 12px rgba(255, 136, 0, 0.1);
  --color-b-primary: #e0def7;
  --color-secondary: #666666;
  --color-primary-light: lighten($color-primary-0, 30%);
}

@keyframes pulse-left-to-right {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

.animate-left-to-right-pulse {
  background-image: linear-gradient(
    to right,
    transparent,
    rgba(12, 0, 0, 0.6),
    transparent
  );
  background-size: 200% 100%;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  animation: pulse-left-to-right 2s ease-in-out infinite;
}

body {
  font-family: 'Roboto', sans-serif;
  font-optical-sizing: auto;
  font-weight: 400;
  font-style: normal;
}

@layer utilities {
  /* Chrome, Safari and Opera */
  .scrollbar-hidden::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-hidden {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE and Edge */
  }
}
@layer base {
  .reset-styles h1,
  .reset-styles h2,
  .reset-styles h3,
  .reset-styles h4,
  .reset-styles h5,
  .reset-styles h6,
  .reset-styles ul,
  .reset-styles ol,
  .reset-styles li,
  .reset-styles p,
  .reset-styles blockquote,
  .reset-styles pre,
  .reset-styles code,
  .reset-styles table,
  .reset-styles th,
  .reset-styles td,
  .reset-styles button,
  .reset-styles input,
  .reset-styles textarea,
  .reset-styles select,
  .reset-styles label,
  .reset-styles a {
    all: revert !important;
  }
}

.custom-scrollbar::-webkit-scrollbar {
  height: 2px;
  width: 2px;
}
.custom-scrollbar::-webkit-scrollbar-thumb {
  background-color: #FF8800;
  border-radius: 10px;
}
.custom-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
}

.custom-clamp {
  display: -webkit-box;
  -webkit-line-clamp: 3; /* Limits text to 3 lines */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  max-height: 5.3em; /* Adjust based on font size */
  line-height: 1.5em; /* Controls line spacing */

  /* Standard Property (Fallback) */
  display: box;
  line-clamp: 3;
  box-orient: vertical;
}

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  -webkit-box-shadow: 0 0 0px 1000px white inset !important;
  box-shadow: 0 0 0px 1000px white inset !important;
  transition: background-color 5000s ease-in-out 0s;
}

.prose ul {
  list-style-type: disc;
  padding-left: 1.5rem;
}

.prose a {
  color: blue;
  text-decoration: underline;
}

.prose ol {
  list-style-type: decimal;
  padding-left: 1.5rem;
}
.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}
