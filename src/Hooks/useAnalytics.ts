import {
  getUserAnalytics,
  getCategoryWiseAnalytics,
  getCategoryWiseIssueAnalytics,
  getRectificationAnalytics,
} from '@Query/Services/analytics.service';
import { useQuery } from 'react-query';

export const useGetUserAnalytics = (data: string) =>
  useQuery(['graph1'], () => getUserAnalytics(data));

export const useGetCategoryWiseAnalytics = (data: string) =>
  useQuery(['graph2'], () => getCategoryWiseAnalytics(data));

export const useGetCategoryWiseIssueAnalytics = (data: string) =>
  useQuery(['graph3'], () => getCategoryWiseIssueAnalytics(data));

export const useRectificationAnalytics = (data: string) =>
  useQuery(['graph4'], () => getRectificationAnalytics(data));
