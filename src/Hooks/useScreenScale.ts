import { useState, useEffect } from 'react';

const useScreenScale = (): number => {
  const [scale, setScale] = useState<number>(100); // Default: 100% scale

  useEffect(() => {
    const updateZoom = () => {
      const zoomLevel = Math.round(window.devicePixelRatio * 100);
      setScale(zoomLevel);
    };

    updateZoom();
    window.addEventListener('resize', updateZoom);

    return () => window.removeEventListener('resize', updateZoom);
  }, []);

  return scale;
};

export default useScreenScale;
