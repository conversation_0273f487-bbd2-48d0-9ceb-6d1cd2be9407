import { AccessibleImage } from '@Components/UI';
import ROBOLOGO from '@Assets/Images/ROBOLOGO.png';

import { motion } from 'framer-motion';

const InternetConnectionMonitor = () => {
  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.8 }}
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#0d1117',
        color: '#ffffff',
        textAlign: 'center',
      }}
    >
      <motion.div
        initial={{ y: -10 }}
        animate={{ y: [0, -10, 0] }}
        transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
      >
        <AccessibleImage src={ROBOLOGO} alt="robologo" className="w-20 h-20" />
      </motion.div>

      <motion.h1
        initial={{ y: -10 }}
        animate={{ y: [0, -10, 0] }}
        transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
        style={{
          fontSize: '2rem',
          fontWeight: 'bold',
          textShadow: '0px 0px 20px rgba(0,255,255,0.6)',
        }}
      >
        🔌 Internet connection lost. Please check your network.
      </motion.h1>
    </motion.div>
  );
};

export default InternetConnectionMonitor;
