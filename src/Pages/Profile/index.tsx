/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useEffect, useState, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import { X } from 'lucide-react';

// Import custom components and hooks
import { Button, InputField, InputSelect, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useStateList, useTradeList } from '@Query/Hooks/useAuth';
import { useGetUserProfile } from '@Query/Hooks/useAdmin';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import { Loader, UserCirclePlusIcon } from '@Icons';
// Import constants
import {
  COUNTRY_CODES,
  DEFAULT_COUNTRY,
  DEFAULT_COUNTRY_CODE,
} from '@Helpers/PhoneCountryCodes';
import { useUpdateProfile } from '@Query/Hooks/useProfile';
import queryClient from '@Helpers/QueryClient';
import CacheKeys from '@Helpers/CacheKeys';
import VerifyOtp from '../VerifyOtp';
import { useDispatch, useSelector } from 'react-redux';
import { setUserData } from '@Redux/SystemControl/UserControle';

// Type Definitions
interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

interface StateData {
  id: number;
  name: string;
}

interface RegisterFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryCode: SelectOption;
  country: SelectOption;
  state: SelectOption;
  trade: SelectOption;
}

const Profile: React.FC = () => {
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const userData = useSelector((state: any) => state?.UserControle?.user);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editToken, setEditToken] = useState<string>('');
  const [currentImage, setCurrentImage] = useState<string>('');
  const [profilePic, setProfilePic] = useState<string>('');
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isImageRemoved, setIsImageRemoved] = useState(false);
  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const { data: stateListData, isLoading: stateListLoading } = useStateList();
  const { data: tradeListData, isLoading: tradeListLoading } = useTradeList();

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    refetch: refetchProfileData,
    isLoading: isViewProfileLoading,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
  } = useGetUserProfile();

  const { mutate: updateUser, isLoading: isUpdateUserLoading } =
    useUpdateProfile({
      onSuccess: (data) => {
        setIsEdit(false);
        setIsImageRemoved(false);
        setSelectedFile(null);
        setIsImageUpdated(false);
        setPreviewUrl(null);
        if (isImageRemoved) {
          setCurrentImage('');
        }
        queryClient.invalidateQueries(CacheKeys.profileData);
        addToast('success', data?.data?.message);
      },
      onError: (error) => {
        addToast('error', error);
      },
    });

  // Validation Schema
  const schema = yup.object().shape({
    firstName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('First name is required')
      .min(3, 'Name must have at least 3 characters')
      .max(50, 'Name must have at most 50 characters'),
    lastName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Last name is required')
      .min(1, 'Name must have at least 1 character')
      .max(50, 'Name must have at most 50 characters'),
    email: yup
      .string()
      .required('Email is required')
      .matches(
        /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
        'Please enter a valid email'
      ),
    phoneNumber: yup
      .string()
      .matches(/^\d{8,12}$/, 'Enter a valid mobile number')
      .required('Mobile number is required'),
    countryCode: yup.mixed().required('Required'),
    country: yup.mixed().required('Country is required'),
    state: yup.mixed().required('State is required'),
    trade: yup.mixed().required('Trade is required'),
  });

  const {
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
    setValue,
    watch,
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      firstName: '',
      lastName: '',
      email: '',
      phoneNumber: '',
      countryCode: DEFAULT_COUNTRY_CODE as unknown as SelectOption,
      country: DEFAULT_COUNTRY as unknown as SelectOption,
      state: null as unknown as RegisterFormValues['state'],
      trade: null as unknown as RegisterFormValues['trade'],
    },
    mode: 'onChange',
  });

  const populateData = (isCancel = false) => {
    if (isSuccessViewProfile && viewProfileData?.data?.data) {
      if (viewProfileData?.data?.data?.profile_picture) {
        setCurrentImage(viewProfileData?.data?.data?.profile_picture);
        setProfilePic(viewProfileData?.data?.data?.profile_picture);
      } else {
        setCurrentImage('');
        setProfilePic('');
      }
      const profileData = viewProfileData.data.data;
      if (!isCancel) {
        dispatch(setUserData({ ...userData, ...profileData }));
      }
      // Set basic profile information
      setValue('firstName', profileData.first_name);
      setValue('lastName', profileData.last_name);
      setValue('email', profileData.email);
      setValue('countryCode', {
        value: profileData.country_code,
        label: profileData.country_code,
      });
      setValue('phoneNumber', profileData.mobile_no);
      setValue('country', {
        value: profileData?.country?.toString(),
        label: profileData.country_name,
      });

      // Set state if state list data is available
      if (stateListData?.data?.data) {
        const stateId = parseInt(profileData?.state?.toString(), 10);
        const state = stateListData.data.data.find(
          (s: StateData) => s.id === stateId
        );

        if (state) {
          setValue('state', {
            value: state.id.toString(),
            label: state.name,
          });
        }
      }

      if (tradeListData?.data?.data && profileData?.trade) {
        const tradeId = parseInt(profileData?.trade?.toString(), 10);
        const trade = tradeListData.data.data.find(
          (t: StateData) => t.id === tradeId
        );
  
        if (trade) {
          setValue('trade', {
            value: trade.id.toString(),
            label: trade.name,
          });
        }
      }

    }
  };

  // Populate form with user profile data
  useEffect(() => {
    populateData();
    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [
    viewProfileData,
    isSuccessViewProfile,
    isErrorInViewProfile,
    errorOfViewProfile,
    stateListData,
    tradeListData,
    setValue,
    addToast,
  ]);

  const onSubmit = (data: RegisterFormValues) => {
    const formData = new FormData();

    const country = parseInt(data.country.value, 10);
    const state = parseInt(data.state.value, 10);
    const trade = parseInt(data.trade.value, 10);

    formData.append('first_name', data.firstName);
    formData.append('last_name', data.lastName);
    formData.append('email', data.email);
    formData.append('mobile_no', data.phoneNumber);
    formData.append('country_code', data.countryCode.value);
    formData.append('country', country.toString());
    formData.append('state', state.toString());
    formData.append('trade', trade.toString());
    formData.append('is_profile_removed', String(isImageRemoved));
    formData.append('is_profile_updated', String(isImageUpdated));

    if (selectedFile) {
      formData.append('profile_picture', selectedFile);
    }
    updateUser(formData);
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);

      if (currentImage) {
        setIsImageUpdated(true);
      } else if (isImageRemoved) {
        setIsImageUpdated(true);
        setIsImageRemoved(false);
      }
      setCurrentImage('');
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
    setCurrentImage(profilePic);
    setSelectedFile(null);
    setIsImageRemoved(false);
    setIsImageUpdated(false);
    setPreviewUrl(null);
    setProfilePic('');
    populateData(true);
    clearErrors();
  };


  const clearSelection = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setSelectedFile(null);
    setPreviewUrl(null);
    if (currentImage) {
      setCurrentImage('');
      setIsImageRemoved(true);
    } else if (isImageUpdated) {
      setIsImageUpdated(false);
      setIsImageRemoved(true);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  useEffect(() => {
    const handleRefresh = () => {
      refetchProfileData();
    };
    window.addEventListener('refreshProfileData', handleRefresh);
    return () => {
      window.removeEventListener('refreshProfileData', handleRefresh);
    };
  }, []);

  const tradeOptions = tradeListData?.data?.data?.map((trade: StateData) => ({
    label: trade.name,
    value: trade.id.toString(),
  })) || [];

  return (
    <ProfileLayout>
      <div>
        <div className="flex flex-col justify-between sticky top-0 bg-white z-20">
          <div
            className={clsx(
              'flex justify-between gap-1.5 p-6 px-10 pb-0 h-[40px]'
            )}
          >
            <div className="flex justify-between w-full">
              <h3 className="text-xl font-semibold">My Profile</h3>
              {!isEdit && (
                <Button
                  text="Edit"
                  type="button"
                  className="!w-[65px] h-[40px] !p-2"
                  onClick={() => setIsEdit(true)}
                  disabled={isViewProfileLoading}
                />
              )}
            </div>
          </div>
        </div>
        <div className="p-10 flex flex-col gap-4 ">
          <div className="max-w-[900px]">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col items-center">
                {/* Circle container with profile image or icon */}
                <div
                  className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center relative cursor-pointer overflow-hidden"
                  onClick={handleButtonClick}
                >
                  {previewUrl || currentImage ? (
                    <img
                      src={previewUrl || currentImage}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserCirclePlusIcon />
                  )}

                  <input
                    type="file"
                    ref={fileInputRef}
                    disabled={!isEdit}
                    className="hidden"
                    accept=".jpg,.jpeg,.png"
                    onChange={handleFileSelect}
                  />
                </div>

                {(selectedFile || currentImage) && (
                  <div className="mt-3 flex items-center bg-gray-100  rounded-md gap-4">
                    {selectedFile && isEdit ? (
                      <>
                        <span className="text-gray-700 text-sm flex-grow truncate px-3 py-2">
                          {selectedFile.name}
                        </span>
                        <button
                          onClick={clearSelection}
                          className="text-gray-500 hover:text-red-500 px-3 py-2"
                        >
                          <X size={16} />
                        </button>
                      </>
                    ) : (
                      currentImage &&
                      isEdit && (
                        <button
                          onClick={clearSelection}
                          className="text-gray-500 hover:text-red-500 h-[30px] px-3 text-[13px] hover:cursor-pointer"
                        >
                          Remove
                        </button>
                      )
                    )}
                  </div>
                )}
              </div>

              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="firstName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="First Name"
                      field={field}
                      placeholder="First Name"
                      errorMessage={errors.firstName?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="lastName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Last Name"
                      field={field}
                      placeholder="Last Name"
                      errorMessage={errors.lastName?.message}
                      disabled={!isEdit}
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="email"
                    control={control}
                    render={({ field }) => (
                      <InputField
                        label="Email"
                        field={field}
                        placeholder="Enter your email"
                        errorMessage={errors.email?.message}
                        disabled={!isEdit}
                      />
                    )}
                  />
                </div>

                <div className="w-1/2">
                  <div className="relative mt-6">
                    <div className="flex gap-x-2">
                      <label
                        className={clsx(
                          'text-sm font-medium text-left absolute -top-5 text-gray-700'
                        )}
                      >
                        Mobile Number
                      </label>

                      <div className="w-[108px]">
                        <Controller
                          name="countryCode"
                          control={control}
                          render={({ field }) => (
                            <InputSelect
                              label={true}
                              field={field}
                              disabled={!isEdit}
                              placeholder="+61"
                              errorMessage={
                                errors.countryCode?.message
                                  ? String(errors.countryCode.message)
                                  : undefined
                              }
                              options={COUNTRY_CODES}
                            />
                          )}
                        />
                      </div>

                      <div className="w-full">
                        <Controller
                          name="phoneNumber"
                          control={control}
                          render={({ field }) => (
                            <InputField
                              label={true}
                              field={field}
                              placeholder="Enter your mobile number"
                              errorMessage={errors.phoneNumber?.message}
                              disabled={!isEdit}
                            />
                          )}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div className="flex gap-x-4 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="country"
                    control={control}
                    render={({ field }) => (
                      <InputSelect
                        label={'Country'}
                        field={field}
                        placeholder="Select country"
                        disabled={!isEdit}
                        errorMessage={
                          errors.country?.message
                            ? String(errors.country.message)
                            : undefined
                        }
                        options={[{ value: '+61', label: 'Australia' }]}
                      />
                    )}
                  />
                </div>

                <div className="w-1/2">
                  <Controller
                    name="state"
                    control={control}
                    render={({ field }) => (
                      <InputSelect
                        label={'State'}
                        field={field}
                        placeholder="Select your state"
                        disabled={!isEdit}
                        errorMessage={
                          errors.state?.message
                            ? String(errors.state.message)
                            : undefined
                        }
                        options={
                          stateListData?.data?.data?.map(
                            (state: StateData) => ({
                              label: state.name,
                              value: state.id.toString(),
                            })
                          ) || []
                        }
                        isLoading={stateListLoading}
                      />
                    )}
                  />
                </div>
              </div>
              <div className="flex gap-x-4 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="trade"
                    control={control}
                    render={({ field }) => (
                      <InputSelect
                        label={'Trade'}
                        field={field}
                        placeholder="Select your trade"
                        disabled={!isEdit}
                        errorMessage={
                          errors.trade?.message
                            ? String(errors.trade.message)
                            : undefined
                        }
                        options={tradeOptions}
                        isLoading={tradeListLoading}
                      />
                    )}
                  />
                </div>
              </div>
              {isEdit && (
                <div className="mt-8 flex justify-center gap-10">
                  <div className="min-w-[150px]">
                    <Button
                      text="Cancel"
                      type="button"
                      className="w-full"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isUpdateUserLoading}
                    />
                  </div>
                  <div className="min-w-[150px]">
                    <Button
                      text="Save"
                      type="submit"
                      className="w-full"
                      loading={isUpdateUserLoading}
                    />
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
    </ProfileLayout>
  );
};

export default Profile;