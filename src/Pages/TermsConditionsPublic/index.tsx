import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useEffect, useState } from 'react';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import parse from 'html-react-parser';
import { RoboIcon } from '@Icons';

const TermsConditionsPublic = (): JSX.Element => {
  const { addToast } = useToast();
  const api = new Api();

  const [contentData, setContentData] = useState<{
    title?: string;
    content?: string;
  } | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchTermsConditions = async () => {
      try {
        const { data } = await api.get(API_PATHS.TERMS_AND_CONDITION);
        const termsAndConditionContent = data?.data;

        if (termsAndConditionContent) {
          setContentData(termsAndConditionContent);
        }
      } catch (error: any) {
        addToast('error', error as string);
      } finally {
        setLoading(false);
      }
    };

    fetchTermsConditions();
  }, []);

  return (
    <div className="flex justify-center items-center min-h-screen bg-gray-100 px-4">
      <div className="max-w-[1200px] w-full bg-white p-6 shadow-lg rounded-xl overflow-hidden">
        <div className="flex justify-center">
          <RoboIcon className="w-16 h-16 text-blue-500" />
        </div>

        <h1 className="text-3xl font-bold text-center text-gray-800 mt-4">
          {contentData?.title || 'Terms & Conditions'}
        </h1>

        <div
          className="reset-styles mt-6 max-h-[600px] overflow-y-auto p-4 text-gray-700 leading-relaxed border border-gray-200 rounded-md"
          style={{ scrollBehavior: 'smooth' }}
        >
          {loading ? (
            <p className="text-center animate-pulse">Loading...</p>
          ) : contentData?.content ? (
            parse(contentData.content)
          ) : (
            <p className="text-center">No content available</p>
          )}
        </div>
      </div>
    </div>
  );
};

export default TermsConditionsPublic;
