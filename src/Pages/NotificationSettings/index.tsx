import { useState, useEffect } from 'react';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import ContentLayout from '@Components/Layout/ContentLayout';
import { Switch } from '@Components/UI';
import { BellRingingIcon } from '@Icons';

import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';

const NotificationSettings = (): JSX.Element => {
  const { addToast } = useToast();
  const api = new Api();

  const [is_email_notification, setEmailEnabled] = useState(false);
  const [is_push_notification, setPushEnabled] = useState(false);

  const getUserProfile = async () => {
    try {
      const { data } = await api.get(API_PATHS.USER_PROFILE);
      if (data?.data) {
        setEmailEnabled(data.data.is_email_notification);
        setPushEnabled(data.data.is_push_notification);
      }
    } catch (error: any) {
      addToast('error', error);
    }
  };

  const updateNotificationSettings = async (newSettings: {
    is_email_notification: boolean;
    is_push_notification: boolean;
  }) => {
    try {
      const { data } = await api.put(API_PATHS.NOTIFICATION_SETTINGS, {
        data: newSettings,
      });
      const responseData = data;
      console.log(responseData);
      if (responseData) {
        addToast('success', responseData.message);
      }
    } catch (error: any) {
      addToast('error', error);
    }
  };

  useEffect(() => {
    getUserProfile();
  }, []);

  return (
    <ProfileLayout>
      <ContentLayout
        title="Notification Settings"
        subtitle="Manage your notification preferences."
      >
        <div className="flex items-center bg-white p-4 rounded-lg shadow-md w-full">
          <div className="bg-[#F1F1F1] h-12 w-12 flex justify-center items-center rounded-full shrink-0">
            <BellRingingIcon />
          </div>

          <div className="flex-1 ml-4">
            <h2 className="text-lg font-semibold text-gray-800 leading-snug">
              Push Notification
            </h2>
            <p className="text-sm text-gray-600 leading-relaxed">
              Get real-time alerts and never miss important updates.
            </p>
          </div>

          <div className="shrink-0">
            <Switch
              enabled={is_push_notification}
              onChange={(value) => {
                setPushEnabled(value);
                updateNotificationSettings({
                  is_email_notification,
                  is_push_notification: value,
                });
              }}
            />
          </div>
        </div>

        <div className="flex items-center bg-white p-4 rounded-lg shadow-md w-full">
          <div className="bg-[#F1F1F1] h-12 w-12 flex justify-center items-center rounded-full shrink-0">
            <BellRingingIcon />
          </div>

          <div className="flex-1 ml-4">
            <h2 className="text-lg font-semibold text-gray-800 leading-snug">
              Email Notification
            </h2>
            <p className="text-sm text-gray-600 leading-relaxed">
              Stay updated with important updates in your inbox.
            </p>
          </div>

          <div className="shrink-0">
            <Switch
              enabled={is_email_notification}
              onChange={(value) => {
                setEmailEnabled(value);
                updateNotificationSettings({
                  is_email_notification: value,
                  is_push_notification,
                });
              }}
            />
          </div>
        </div>
      </ContentLayout>
    </ProfileLayout>
  );
};

export default NotificationSettings;
