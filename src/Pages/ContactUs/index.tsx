import React, { useCallback, useRef, useState } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import { Button, InputField, AccessibleImage } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { API_PATHS } from '@Helpers/Constants';
import Api from '@Helpers/Api';
import ContactUsImage from '@Assets/Images/ContactUs.png';
import TextAreaField from '@Components/UI/InputField/TextAreaField';

type ContactUsFormValues = {
  subject: string;
  comment: string;
};

const ContactUs = (): JSX.Element => {
  const { addToast } = useToast();
  const api = useRef(new Api()).current;

  const [loading, setLoading] = useState<boolean>(false);

  const schema = yup.object().shape({
    subject: yup
      .string()
      .required('Subject is required')
      .max(50, 'Subject must be at most 50 characters'),
    comment: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Comment is required')
      .max(1000, 'Comment must have at most 1000 characters'),
  });

  const {
    control,
    handleSubmit,
    formState: { errors },
    reset,
  } = useForm<ContactUsFormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      subject: '',
      comment: '',
    },
    mode: 'onChange',
  });

  const onSubmit = useCallback(
    async ({ subject, comment }: ContactUsFormValues) => {
      setLoading(true);
      try {
        const { data } = await api.post(API_PATHS.CONTACT_US, {
          data: {
            subject,
            comment,
          },
        });
        const responseData = data;
        if (responseData) {
          addToast('success', responseData?.message);
          reset();
        }
      } catch (error) {
        addToast('error', error as string);
      } finally {
        setLoading(false);
      }
    },
    []
  );

  return (
    <ProfileLayout>
      <div className="flex p-8">
        <div className="w-1/2 max-w-[470px]">
          <h1 className="text-3xl font-bold text-left text-gray-800 pb-4">
            Contact Us
          </h1>
          <p className="text-sm text-gray-500 pb-10">
            Contact our support team for assistance.
          </p>

          <form onSubmit={handleSubmit(onSubmit)}>
            <div>
              <Controller
                control={control}
                name="subject"
                render={({ field }) => (
                  <InputField
                    label="Subject"
                    placeholder="Subject"
                    field={field}
                    type="text"
                    errorMessage={errors?.subject?.message}
                  />
                )}
              />
            </div>
            <div className="mt-6">
              <Controller
                control={control}
                name="comment"
                render={({ field }) => (
                  <TextAreaField
                    label={'Comment'}
                    field={field}
                    placeholder="Share your feedback here"
                    rows={6}
                    errorMessage={errors.comment?.message}
                  />
                )}
              />
            </div>
            <div className="mt-6">
              <Button text="Submit" type="submit" loading={loading} />
            </div>
          </form>
        </div>

        <div className="w-1/2 flex justify-center">
          <AccessibleImage
            src={ContactUsImage}
            alt="Performance solution image"
            className="h-full w-full object-cover"
          />
        </div>
      </div>
    </ProfileLayout>
  );
};
export default ContactUs;
