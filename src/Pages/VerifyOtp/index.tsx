import React, { useEffect, useRef, useState, useCallback } from 'react';
import { Button } from '@Components/UI';
import { useForm, Controller } from 'react-hook-form';
import { useDispatch } from 'react-redux';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import clsx from 'clsx';
import { useResendOtp } from '@Query/Hooks/useAuth';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { useSelector } from 'react-redux';

import { API_PATHS } from '@Helpers/Constants';
import { setUserData } from '@Redux/SystemControl/UserControle';

const otpSchema = yup.object().shape({
  otp: yup
    .array()
    .of(
      yup
        .string()
        .matches(/^\d$/, 'Each field must be a single digit')
        .required('Each digit is required')
    )
    .min(4, 'OTP must be exactly 4 digits')
    .max(4, 'OTP must be exactly 4 digits')
    .required(), // ✅ Ensure otp array is always required
});

interface OTPFormValues {
  otp: string[];
}

const VerifyOtp = ({
  isEdit = false,
  onEditSuccess = () => {},
  editToken = '',
}): JSX.Element => {
  const length = 4;
  const api = useRef(new Api()).current;
  const { addToast } = useToast();
  const dispatch = useDispatch();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);

  const [timer, setTimer] = useState(30);
  const [loading, setLoading] = useState<boolean>(false);
  const {
    mutate: resendOtp,
    isError: isResendError,
    error: resendError,
    isSuccess: isResendSuccess,
    data: resendData,
  } = useResendOtp();

  // Add effect to handle resend API responses
  useEffect(() => {
    if (isResendError) {
      addToast('error', resendError as string);
    }
    if (isResendSuccess) {
      addToast('success', resendData?.data?.message);
    }
  }, [isResendError, isResendSuccess]);

  const {
    control,
    handleSubmit,
    setValue,
    getValues,
    watch,
    formState: { errors },
  } = useForm<OTPFormValues>({
    resolver: yupResolver(otpSchema),
    defaultValues: { otp: ['', '', '', ''] },
    mode: 'onChange',
  });

  const otpWatch = watch('otp');

  const inputRefs = useRef<Array<HTMLInputElement | null>>(
    Array(length).fill(null)
  );

  // Start countdown when component mounts
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer((prev) => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [timer]);

  const handleChange = (index: number, value: string) => {
    // Only allow numeric input
    if (!/^\d*$/.test(value)) {
      return;
    }

    const otpValues = getValues('otp');
    otpValues[index] = value.slice(-1); // Ensure only 1 digit is stored
    setValue('otp', otpValues);

    // Move focus to next field
    if (value && index < length - 1) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (
    index: number,
    event: React.KeyboardEvent<HTMLInputElement>
  ) => {
    if (
      event.key === 'e' ||
      event.key === 'E' ||
      event.key === '+' ||
      event.key === '-'
    ) {
      event.preventDefault();
      return;
    }

    if (event.key === 'Backspace') {
      const otpValues = getValues('otp');
      if (!otpValues[index] && index > 0) {
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  const handlePaste = (event: React.ClipboardEvent<HTMLInputElement>) => {
    const pastedData = event.clipboardData.getData('text');

    // Allow Ctrl+V or Cmd+V but prevent non-numeric characters
    if (!/^\d+$/.test(pastedData)) {
      event.preventDefault();
      return;
    }

    event.preventDefault(); // Prevent default paste behavior

    const otpValues = pastedData.slice(0, length).split(''); // Take only the first 'length' characters

    setValue('otp', otpValues);

    otpValues.forEach((digit, i) => {
      if (inputRefs.current[i]) {
        inputRefs.current[i]!.value = digit;
      }
    });
    inputRefs.current[otpValues.length - 1]?.focus();
  };

  const onSubmit = useCallback(async (otp: string) => {
    setLoading(true);
    try {
      if (!isEdit) {
        const { data } = await api.post(API_PATHS.VERIFY_OTP, {
          data: {
            verify_token: userData?.verify_token,
            otp,
          },
        });
        const responseData = data;
        localStorage.setItem('SESSION_ID', 'null');
        dispatch(setUserData({ ...userData, ...responseData?.data }));

        addToast('success', responseData.message);
      } else {
        await api.post(API_PATHS.VERIFY_OTP, {
          data: {
            verify_token: editToken,
            otp,
          },
        });
        onEditSuccess();
      }

      // }
    } catch (error) {
      // Clear all OTP inputs
      setValue('otp', Array(length).fill(''));
      // Focus on first input
      setTimeout(() => {
        inputRefs.current[0]?.focus();
      }, 0);
      addToast('error', error as string);
    } finally {
      setLoading(false);
    }
  }, []);

  const handleResendOtp = () => {
    if (!timer && userData?.id) {
      resendOtp({
        verify_token: userData?.verify_token,
        user_id: userData.id,
      });
      setTimer(30);
    }
  };

  return (
    <>
      <form
        onSubmit={handleSubmit((data) => onSubmit(data.otp.join('')))}
        className="flex flex-col items-center pt-6"
      >
        <div className="flex justify-center">
          {Array.from({ length }, (_, i) => (
            <div className="flex items-center">
              <Controller
                key={i}
                name={`otp.${i}`}
                control={control}
                render={({ field }) => (
                  <input
                    {...field}
                    ref={(el) => (inputRefs.current[i] = el)}
                    type="text"
                    inputMode="numeric"
                    pattern="\d*"
                    maxLength={1}
                    className={clsx(
                      'w-16 h-16 border border-b-primary rounded-[10px] text-center text-lg outline-none focus:border-primary-100 focus:shadow-primary [appearance:textfield] [&::-webkit-outer-spin-button]:appearance-none [&::-webkit-inner-spin-button]:appearance-none',
                      errors.otp &&
                        otpWatch.some((el) => el === '') &&
                        'border-red-500 focus:border-red-500 focus:shadow-red-200'
                    )}
                    onChange={(e) => handleChange(i, e.target.value)}
                    onKeyDown={(e) => handleKeyDown(i, e)}
                    onPaste={handlePaste}
                    autoFocus={i === 0}
                  />
                )}
              />
              {i !== length - 1 && <span className="px-4">-</span>}
            </div>
          ))}
        </div>

        {errors.otp && otpWatch.some((el) => el === '') && (
          <p className="text-red-500 text-sm mt-2">Enter valid otp</p>
        )}

        <Button
          type="submit"
          text={'Next'}
          className="mt-8"
          loading={loading}
        />

        <div className="pt-12 flex items-center gap-2">
          <button
            className={`text-red-500 text-base font-semibold underline underline-offset-2 cursor-pointer ${
              timer ? 'opacity-50 cursor-not-allowed' : ''
            }`}
            onClick={handleResendOtp}
            disabled={!!timer}
          >
            Resend OTP
          </button>
          {!!timer && <span className="text-gray-500">in {timer} seconds</span>}
        </div>
      </form>
    </>
  );
};

export default VerifyOtp;
