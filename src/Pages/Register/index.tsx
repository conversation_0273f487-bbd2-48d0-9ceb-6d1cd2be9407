import { useEffect, useRef, useState, useCallback } from 'react';
import { Button, InputField, InputSelect } from '@Components/UI';
import { useForm, Controller } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import clsx from 'clsx';
import { useDispatch } from 'react-redux';
import { Link, useNavigate } from 'react-router';
import { PATHS } from '@Config/Path.Config';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { setUserData } from '@Redux/SystemControl/UserControle';
import { useStateList, useTradeList } from '@Query/Hooks/useAuth';
import { requestForToken } from '@Config/firebaseConfig';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import {
  COUNTRY_CODES,
  DEFAULT_COUNTRY,
  DEFAULT_COUNTRY_CODE,
} from '@Helpers/PhoneCountryCodes';

// Types
interface LicenseFormat {
  prefix: string | null;
  postfix: string | null;
  min_number: number;
  max_number: number;
  format_id: number;
}

interface StateData {
  id: number;
  name: string;
  license_formats: LicenseFormat[];
}

interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

interface RegisterFormValues {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber: string;
  countryCode: SelectOption;
  country: SelectOption;
  state: SelectOption;
  trade: SelectOption;
}

// Validation Schema
const validationSchema = yup.object().shape({
  firstName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('First name is required')
    .min(3, 'Name must have at least 3 characters')
    .max(50, 'Name must have at most 50 characters')
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed'),
  lastName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Last name is required')
    .min(1, 'Name must have at least 1 character')
    .max(50, 'Name must have at most 50 characters')
    .matches(/^[A-Za-z ]+$/, 'Only alphabets and spaces are allowed'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email'
    ),
  phoneNumber: yup
    .string()
    .matches(/^\d{8,12}$/, 'Enter a valid mobile number')
    .required('Mobile number is required'),
  countryCode: yup.mixed().required('Country code is required'),
  country: yup.mixed().required('Country is required'),
  state: yup.mixed().required('State is required'),
  trade: yup.mixed().required('Trade is required'),
});

// Constants
const FORM_STORAGE_KEY = 'registerFormValues';
const DEVICE_ID_KEY = 'deviceId';

const Register = (): JSX.Element => {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const api = useRef(new Api()).current;

  const [loading, setLoading] = useState(false);
  const [deviceState, setDeviceState] = useState({
    deviceId: '',
    firebaseToken: '',
    isInitialized: false,
  });

  // Custom hooks for data fetching
  const { data: stateListData, isLoading: stateListLoading } = useStateList();
  const { data: tradeListData, isLoading: tradeListLoading } = useTradeList();

  // Utility functions
  const getStoredFormValues = useCallback((): Partial<RegisterFormValues> => {
    try {
      const storedValues = sessionStorage.getItem(FORM_STORAGE_KEY);
      return storedValues ? JSON.parse(storedValues) : {};
    } catch (error) {
      console.error('Error parsing stored form values:', error);
      return {};
    }
  }, []);

  const getOrCreateDeviceId = useCallback((): string => {
    let deviceId = localStorage.getItem(DEVICE_ID_KEY);
    if (!deviceId) {
      deviceId = uuidv4();
      localStorage.setItem(DEVICE_ID_KEY, deviceId);
    }
    return deviceId;
  }, []);

  const requestNotificationPermission = useCallback(async (): Promise<boolean> => {
    if (!('Notification' in window)) {
      addToast('error', 'This browser does not support notifications');
      return false;
    }

    if (Notification.permission === 'granted') return true;
    if (Notification.permission === 'denied') return false;

    try {
      const permission = await Notification.requestPermission();
      return permission === 'granted';
    } catch (error) {
      addToast('error', 'Failed to request notification permissions');
      return false;
    }
  }, [addToast]);

  const initializeDevice = useCallback(async () => {
    const deviceId = getOrCreateDeviceId();
    const firebaseToken = (await requestForToken()) || '';
    const isInitialized = await requestNotificationPermission();

    setDeviceState({
      deviceId,
      firebaseToken,
      isInitialized,
    });
  }, [getOrCreateDeviceId, requestNotificationPermission]);

  // Form setup
  const defaultValues: Partial<RegisterFormValues> = {
    firstName: '',
    lastName: '',
    email: '',
    phoneNumber: '',
    countryCode: DEFAULT_COUNTRY_CODE as unknown as SelectOption,
    country: DEFAULT_COUNTRY as unknown as SelectOption,
    state: null as unknown as RegisterFormValues['state'],
    trade: null as unknown as RegisterFormValues['trade'],
    ...getStoredFormValues(),
  };

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(validationSchema) as any,
    defaultValues,
    mode: 'onChange',
  });

  const formValues = watch();

  // Effects
  useEffect(() => {
    initializeDevice();
  }, [initializeDevice]);

  useEffect(() => {
    const hasFormData = Object.values(formValues).some(value => 
      value && (typeof value === 'string' ? value.trim() : true)
    );

    if (hasFormData) {
      try {
        sessionStorage.setItem(FORM_STORAGE_KEY, JSON.stringify(formValues));
      } catch (error) {
        console.error('Error saving form values to session storage:', error);
      }
    }
  }, [formValues]);

  // Form submission
  const onSubmit = useCallback(
    async (formData: RegisterFormValues): Promise<void> => {
      setLoading(true);
      
      try {
        const { data } = await api.post(API_PATHS.REGISTER, {
          data: {
            first_name: formData.firstName,
            last_name: formData.lastName,
            email: formData.email,
            mobile: formData.phoneNumber,
            device_id: deviceState.deviceId,
            device_token: deviceState.firebaseToken,
            country_code: formData.countryCode.value,
            country: parseInt(formData.country.value, 10),
            state: parseInt(formData.state.value, 10),
            trade: parseInt(formData.trade.value, 10),
          },
        });

        if (data) {
          addToast('success', data.message);
          dispatch(setUserData(data?.data));
          navigate(PATHS.VERIFY_OTP);
        }
      } catch (error) {
        addToast('error', error as string);
      } finally {
        setLoading(false);
      }
    },
    [deviceState, addToast, api, navigate, dispatch]
  );

  // Transform data for select options
  const stateOptions = stateListData?.data?.data?.map((state: StateData) => ({
    label: state.name,
    value: state.id.toString(),
    license_formats: state.license_formats,
  })) || [];

  const tradeOptions = tradeListData?.data?.data?.map((trade: StateData) => ({
    label: trade.name,
    value: trade.id.toString(),
  })) || [];

  return (
    <div>
      <form
        className="flex flex-col space-y-4 pt-2"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        {/* Name Fields */}
        <div className="flex gap-x-4">
          <Controller
            name="firstName"
            control={control}
            render={({ field }) => (
              <InputField
                label="First Name"
                field={field}
                placeholder="First Name"
                errorMessage={errors.firstName?.message}
                autoFocus
              />
            )}
          />
          <Controller
            name="lastName"
            control={control}
            render={({ field }) => (
              <InputField
                label="Last Name"
                field={field}
                placeholder="Last Name"
                errorMessage={errors.lastName?.message}
              />
            )}
          />
        </div>

        {/* Email Field */}
        <Controller
          name="email"
          control={control}
          render={({ field }) => (
            <InputField
              label="Email"
              field={field}
              placeholder="Enter your email"
              errorMessage={errors.email?.message}
            />
          )}
        />

        {/* Mobile Number Fields */}
        <div className="relative mt-6">
          <label className={clsx(
            'text-sm font-medium text-left absolute -top-5 text-gray-700'
          )}>
            Mobile Number
          </label>
          <div className="flex gap-x-2">
            <div className="w-[108px]">
              <Controller
                name="countryCode"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    label={true}
                    field={field}
                    placeholder="+61"
                    errorMessage={errors.countryCode?.message ? String(errors.countryCode.message) : undefined}
                    options={COUNTRY_CODES}
                  />
                )}
              />
            </div>
            <div className="w-full">
              <Controller
                name="phoneNumber"
                control={control}
                render={({ field }) => (
                  <InputField
                    label={true}
                    field={field}
                    placeholder="Enter your mobile number"
                    errorMessage={errors.phoneNumber?.message}
                  />
                )}
              />
            </div>
          </div>
        </div>

        {/* Country Field */}
        <Controller
          name="country"
          control={control}
          render={({ field }) => (
            <InputSelect
              label="Country"
              field={field}
              placeholder="Select country"
              errorMessage={errors.country?.message ? String(errors.country.message) : undefined}
              options={[{ value: '+61', label: 'Australia' }]}
              disabled
            />
          )}
        />

        {/* State Field */}
        <Controller
          name="state"
          control={control}
          render={({ field }) => (
            <InputSelect
              label="State"
              field={field}
              placeholder="Select your state"
              errorMessage={errors.state?.message ? String(errors.state.message) : undefined}
              options={stateOptions}
              isLoading={stateListLoading}
            />
          )}
        />

        {/* Trade Field */}
        <Controller
          name="trade"
          control={control}
          render={({ field }) => (
            <InputSelect
              label="Trade"
              field={field}
              placeholder="Select your trade"
              errorMessage={errors.trade?.message ? String(errors.trade.message) : undefined}
              options={tradeOptions}
              isLoading={tradeListLoading}
            />
          )}
        />

        {/* Terms and Conditions */}
        <span className="text-sm text-[#97959E]">
          By signing up, you agree to our{' '}
          <Link
            to={PATHS.TERMS_CONDITIONS_PUBLIC}
            target="_blank"
            className="font-medium text-primary-100 underline underline-offset-2"
          >
            Terms and Conditions
          </Link>{' '}
          and{' '}
          <Link
            to={PATHS.PRIVACY_POLICY_PUBLIC}
            target="_blank"
            className="font-medium text-primary-100 underline underline-offset-2"
          >
            Privacy Policy
          </Link>
          .
        </span>

        {/* Submit Button */}
        <Button
          text="Sign Up"
          className="mt-2"
          type="submit"
          loading={loading}
        />

        {/* Login Link */}
        <Button
          text={
            <span className="font-normal">
              Already have an account? <span className="font-bold">Login</span>
            </span>
          }
          className="mt-2"
          variant="outline"
          type="button"
          onClick={() => navigate('/login')}
        />
      </form>
    </div>
  );
};

export default Register;