
import React, { useEffect, useRef, useState } from 'react';
import {
  ClockCounterwiseIcon,
  MicIcon,
  UserCircleIcon,
  RoboIcon,
  Loader,
  NewChatIcon,
} from '@Icons';
import clsx from 'clsx';
import Recorder from '@Components/Recorder';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { useLocation, useNavigate } from 'react-router';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css'; // Import KaTeX styles
import { useSelector } from 'react-redux';
import { ArrowDown } from 'lucide-react';
import { AccessibleImage, Button } from '@Components/UI';
import ContactUsImage from '@Assets/Images/Frame.png';

interface ChatObjectType {
  text: string | undefined;
  type: 'user' | 'bot';
  id: string;
  calculation_input?: string | undefined;
  calculation_result?: string | undefined;
  isNew?: boolean;
}

interface Session {
  id: number;
  session_title: string;
  session_id: string;
  started_at: string;
  ended_at: string | null;
}

const Home = (): JSX.Element => {
  const scrollPositionRef = useRef(0);
  const scrollHeightRef = useRef(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const firstMessageRef = useRef<HTMLDivElement | null>(null);
  const stopFetchingRef = useRef<boolean>(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { addToast } = useToast();
  const api = new Api();

  const { state } = location;

  const [loading, setLoading] = useState<boolean>(true);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [startX, setStartX] = useState<number>(0);
  const [scrollLeft, setScrollLeft] = useState<number>(0);
  const [text, setText] = useState<string>('');
  const [chats, setChats] = useState<ChatObjectType[]>([]);
  const [isRecordOn, setIsRecordOn] = useState<boolean>(false);
  const [status, setStatus] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [recentSearches, setRecentSearches] = useState<Session[]>([]);

  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isFetchingStopped, setIsFetchingStopped] = useState<boolean>(true);
  const [isNewChat, setIsNewChat] = useState<boolean>(
    state?.from !== 'HISTORY'
  );
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [lastMessageMinHeight, setLastMessageMinHeight] = useState('69vh');

  const fetchSession = async (id: string | null) => {
    setLoading(true);
    try {
      const response = await api.get(
        `${API_PATHS.QUICK_CALC_CHAT_SESSION_DETAILS}/${id}/history`,
        {
          params: { page_size: 10, page: currentPage },
        }
      );

      let newChats: ChatObjectType[] = [];

      if (response?.data?.data?.list?.length) {
        response?.data?.data?.list?.forEach((el: ChatObjectType) => {
          newChats.push({ id: el?.id, type: 'user', text: el?.calculation_input });
          newChats.push({ id: el?.id, type: 'bot', text: el?.calculation_result });
        });
      }
      if (chats.length) {
        newChats = [...newChats, ...chats];
      }
      setChats([...newChats]);

      setTotalPages(response?.data?.data?.total_pages);
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isNewChat && chats?.length === 1) setIsNewChat(false);
  }, [isNewChat, chats, state?.from]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const paymentStatus = searchParams.get('payment_status');

    if (paymentStatus === 'success') {
      addToast('success', 'Payment successful!');
      navigate('.', { replace: true }); // Clean URL
    }
  }, [location, navigate, addToast]);

  useEffect(() => {
    (async () => {
      if (!chats?.length)
        try {
          const searchResponse = await api.get(API_PATHS.QUICK_CALC_SEARCH);
          if (searchResponse?.data?.data?.list?.length)
            setRecentSearches([...searchResponse.data.data.list]);
        } catch (err) {
          console.log('err', err);
        }
    })();
    stopFetchingRef.current = false;
  }, [chats]);

  useEffect(() => {
    if (state?.from === 'HISTORY' && state?.sessionId) {
      setSessionId(state?.sessionId);
    } else {
      setLoading(false);
    }
  }, [state?.sessionId, state?.from]);

  useEffect(() => {
    if (isNewChat && sessionId) setSessionId(null);
  }, [isNewChat, sessionId]);

  useEffect(() => {
    if (sessionId && state?.from === 'HISTORY') fetchSession(sessionId);
    localStorage.setItem('SESSION_ID_QUICK_CALC', sessionId ? sessionId : 'null');
  }, [sessionId, currentPage, state]);

  useEffect(() => {
    if (
      (isFetching &&
        chats[chats?.length - 1]?.type === 'bot' &&
        !status &&
        chats[chats?.length - 1]?.text !== '') ||
      (isFetching &&
        chats[chats?.length - 1]?.type === 'bot' &&
        status &&
        chats[chats?.length - 1]?.text !== '' &&
        chats[chats?.length - 1]?.isNew) ||
      (!isFetching &&
        chats[chats?.length - 1]?.type === 'bot' &&
        !status &&
        chats[chats?.length - 1]?.text !== '' &&
        chats[chats?.length - 1]?.isNew)
    ) {
      return;
    }
    if (
      chatContainerRef.current &&
      (status ||
        chats[chats?.length - 1]?.type === 'user' ||
        (state?.from === 'HISTORY' && currentPage === 1))
    ) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [chats, status, currentPage, isFetching]);

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [text]);

  // Restore scroll position after loading more messages
  useEffect(() => {
    if (loading === false && currentPage > 1 && chatContainerRef.current) {
      const newScrollHeight = chatContainerRef.current.scrollHeight;
      const addedHeight = newScrollHeight - scrollHeightRef.current;
      chatContainerRef.current.scrollTop =
        scrollPositionRef.current + addedHeight;
    }
  }, [loading, currentPage]);

  useEffect(() => {
    if (!chats.length) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          currentPage <= totalPages &&
          !loading
        ) {
          if (chatContainerRef.current) {
            scrollPositionRef.current = chatContainerRef.current.scrollTop;
            scrollHeightRef.current = chatContainerRef.current.scrollHeight;
          }
          setCurrentPage((p) => Math.min(p + 1, totalPages));
        }
      },
      { threshold: 1.0 }
    );

    if (firstMessageRef.current) observer.observe(firstMessageRef.current);

    return () => observer.disconnect();
  }, [chats, currentPage, totalPages, loading]);

  // Check if scrolled away from bottom
  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        chatContainerRef.current;
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      const shouldShow = distanceFromBottom > 150;

      if (shouldShow !== showScrollButton) {
        setShowScrollButton(shouldShow);
      }
    }
  };

  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (chatContainer) {
      chatContainer.addEventListener('scroll', handleScroll);
      handleScroll();

      return () => {
        chatContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]);

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!scrollRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollRef.current.offsetLeft);
    setScrollLeft(scrollRef.current.scrollLeft);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!isDragging || !scrollRef.current) return;
    const x = e.pageX - scrollRef.current.offsetLeft;
    const walk = (x - startX) * 2;
    scrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const onRecordClose = (voiceText: string) => {
    if (voiceText) {
      setText(voiceText);
      textareaRef.current?.focus();
    }
  };

  const { mutateAsync } = useStreamedResponse();
  const handleFetch = async (userText: string) => {
    setIsFetching(true);
    setIsFetchingStopped(false);
    stopFetchingRef.current = false;
    if (!userText.trim()) return;
    const tempChats: ChatObjectType[] = [...chats];

    tempChats.push({
      type: 'user',
      text: userText,
      id: String(chats.length),
      isNew: true,
    });
    setChats([...tempChats]);
    setStatus(null);
    setText('');

    try {
      const requestBody = { calculation_query: userText, session_id: sessionId ?? '' };

      const reader = await mutateAsync('rag/quick-calc/', requestBody, false);

      const decoder = new TextDecoder();
      let textBuffer = '';
      let accumulatedText = '';

      const botMessage: ChatObjectType = {
        type: 'bot',
        text: '',
        id: String(chats.length + 1),
        isNew: true,
      };
      tempChats.push(botMessage);
      setChats([...tempChats]);

      while (true) {
        if (stopFetchingRef.current) {
          console.log('Fetching stopped by user');
          break;
        }

        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        textBuffer += chunk;

        let newlineIndex;
        while ((newlineIndex = textBuffer.indexOf('\n')) >= 0) {
          const rawLine = textBuffer.slice(0, newlineIndex).trim();
          textBuffer = textBuffer.slice(newlineIndex + 1);

          if (!rawLine.startsWith('data: ')) continue;

          const jsonStr = rawLine.replace(/^data:\s*/, '');
          if (!jsonStr || jsonStr === '[DONE]') continue;

          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          let parsedData: any;
          try {
            parsedData = JSON.parse(jsonStr);
          } catch (e) {
            console.warn('JSON parse failed, skipping:', jsonStr);
            continue;
          }

          if (parsedData?.type === 'calculation_chunk') {
            accumulatedText += parsedData.content + ' ';
            botMessage.text = accumulatedText;
            setStatus(null);
            setChats([...tempChats]);
          } else if (
            parsedData?.type === 'complete' ||
            parsedData?.type === 'error'
          ) {
            if (parsedData?.session_id) setSessionId(parsedData.session_id);
            botMessage.text =
              accumulatedText +
              (parsedData?.error || '') +
              (parsedData?.relevant_content
                ? ' ✔'
                : parsedData?.content
                  ? parsedData.content
                  : '');
            setStatus(null);
            setChats([...tempChats]);
          } else if (
            parsedData?.type === 'status' ||
            parsedData?.type === 'metadata'
          ) {
            if (parsedData?.session_id) setSessionId(parsedData.session_id);
            setStatus(parsedData.message || parsedData.content || status || '');
          }
        }
      }

      setIsFetching(false);
      setIsFetchingStopped(true);
    } catch (error) {
      if ((error as DOMException).name === 'AbortError') {
        console.log('Request aborted');
      } else {
        console.error('Streaming failed:', error);
      }
      setIsFetching(false);
      setIsFetchingStopped(true);
    }
  };

  const cleanMath = (text: string | undefined) =>
    text ? text.replace(/\\\[/g, '$$').replace(/\\\]/g, '$$') : '';

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Calculate last message min height
  useEffect(() => {
    if (chats.length > 2 && status && chats[chats?.length - 1]?.isNew) {
      const lastMessageId = chats[chats.length - 1]?.id;
      const secondLastMessageId = chats[chats.length - 2]?.id;

      setTimeout(() => {
        const secondLastElement = document.getElementById(secondLastMessageId);

        if (secondLastElement && chats[chats.length - 1]?.isNew) {
          const secondLastHeight =
            secondLastElement.getBoundingClientRect().height;
          const viewportHeight = window.innerHeight * 0.76;
          const calculatedHeight = Math.max(
            0,
            viewportHeight - secondLastHeight
          );
          setLastMessageMinHeight(`${calculatedHeight}px`);
        }
      }, 0);
    }
  }, [chats, status]);

  const handleSubmit = () => {
    if (!text.trim()) return;
    const tempChats: ChatObjectType[] = [];
    tempChats.push({
      type: 'user',
      text: text,
      id: String(chats?.length),
      isNew: true,
    });
    setChats([...tempChats]);
    handleFetch(text);
    setText('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
      e.preventDefault();
      if (!text.trim() || !isFetchingStopped) return;
      handleSubmit();
    } else if (e.key === 'Enter' && e.ctrlKey) {
      setText(text + '\n');
    }
  };

  return (
    <div className="flex flex-col h-full w-full relative overflow-hidden">
      {sessionId && loading && !chats?.length && (
        <div className="flex flex-1 h-full w-full justify-center items-center">
          <Loader height={46} width={46} fill="#fff" />
        </div>
      )}

      {isRecordOn && (
        <Recorder setIsRecordOn={setIsRecordOn} onClose={onRecordClose} />
      )}

      {isNewChat && !sessionId && !loading ? (
        <div className="flex flex-1 items-center justify-center min-h-screen px-4 py-8">
          <div className="w-full max-w-7xl mx-auto">
            <div className="flex flex-col lg:flex-row items-center justify-center gap-8 lg:gap-12">
              {/* Left Side - Content */}
              <div className="w-full lg:w-1/2 max-w-lg lg:max-w-none">
                <div className="text-center lg:text-left">
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold text-gray-800 mb-4 lg:mb-6 leading-tight">
                    <span className="text-[#FF8800]">Quick Calc -</span> Fast, Accurate Trade Calculations
                  </h1>
                  <p className="text-sm sm:text-base text-gray-500 mb-6 lg:mb-4">
                    Type or speak your question and get instant, step-by-step <br /> answers for concrete, pipe sizing, wire specs, load requirements, and more.
                  </p>
                </div>

                <h1 className="text-xl font-bold text-gray-800 mb-4 lg:mb-6 leading-tight">
                  Built for tradies, powered by REX
                </h1>

                {/* Search Input */}
                <div className="space-y-4">
                  <div className="flex items-center p-3 sm:p-4 border w-full min-h-12 max-h-32 group rounded-lg border-gray-300 focus-within:border-[#FF8800] focus-within:shadow-lg transition-all duration-200 gap-x-3">
                    <textarea
                      ref={textareaRef}
                      className="flex-1 text-base sm:text-lg border-none outline-none resize-none bg-transparent min-h-8 max-h-28 custom-scrollbar placeholder-gray-400"
                      rows={1}
                      placeholder="Ask any calculations related queries"
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      onKeyDown={handleKeyDown}
                    />
                    <MicIcon
                      height={20}
                      width={20}
                      className="cursor-pointer hover:scale-110 flex-shrink-0 text-gray-500 hover:text-[#FF8800] transition-colors"
                      onClick={() => setIsRecordOn(true)}
                    />
                  </div>

                  <Button
                    onClick={handleSubmit}
                    text="Submit"
                    type="submit"
                    loading={loading}
                    disabled={!text.trim()}
                  />
                </div>
              </div>

              {/* Right Side - Image */}
              <div className="w-full lg:w-1/2 max-w-md lg:max-w-none flex justify-center">
                <div className="relative">
                  <AccessibleImage
                    src={ContactUsImage}
                    alt="Performance solution image"
                    className="w-full h-auto max-w-md lg:max-w-lg xl:max-w-xl object-cover rounded-lg shadow-lg"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      ) : (
        chats?.length > 0 &&
        !isNewChat && (
          <div className="flex overflow-hidden h-screen max-h-screen flex-col p-4 sm:p-6">
            <div className="flex flex-1 flex-col">
              <div className="flex pb-4 w-full justify-between items-center">
                <div className="font-semibold text-xl sm:text-2xl">Search Result</div>
                <div className="flex items-center gap-x-4">
                  <div
                    className="cursor-pointer hover:bg-gray-100 p-2 rounded-full transition-colors"
                    onClick={() => {
                      stopFetchingRef.current = true;
                      setIsFetchingStopped(true);
                      setSessionId(null);
                      setChats([]);
                      setIsNewChat(true);
                      if (state?.from === 'HISTORY')
                        navigate('.', { replace: true, state: null });
                    }}
                  >
                    <NewChatIcon height={24} width={24} />
                  </div>
                </div>
              </div>

              {loading && chats?.length > 0 && sessionId && currentPage > 1 && (
                <div className="py-2 flex justify-center items-center w-full">
                  <Loader fill="#fff" height={36} width={36} />
                </div>
              )}

              <div
                ref={chatContainerRef}
                className={clsx(
                  'flex flex-col h-full max-h-[76vh] px-2 sm:px-4 overflow-auto custom-scrollbar w-full flex-1',
                  chats?.length &&
                  chats[chats?.length - 1]?.isNew &&
                  'scroll-smooth'
                )}
              >
                {chats.map((el, index) => (
                  <div
                    className={clsx(
                      'flex w-full py-3 sm:py-4 items-start',
                      el?.type === 'user' ? 'justify-end' : 'justify-start',
                      index === chats?.length - 1 &&
                      chats?.length > 2 &&
                      el?.isNew &&
                      'min-h-[69vh]'
                    )}
                    style={
                      index === chats?.length - 1 &&
                        chats?.length > 2 &&
                        el?.isNew &&
                        lastMessageMinHeight
                        ? { minHeight: lastMessageMinHeight ?? 'auto' }
                        : {}
                    }
                    id={el?.id}
                    key={`${el?.id}_${el?.type}`}
                    ref={index === 0 ? firstMessageRef : null}
                  >
                    {el?.type === 'bot' && (
                      <div className="flex h-8 w-8 flex-shrink-0">
                        <RoboIcon />
                      </div>
                    )}
                    <div
                      className={clsx(
                        'flex flex-wrap px-3 sm:px-4 py-2.5 sm:py-3 rounded-lg max-w-full sm:max-w-4xl',
                        el?.type === 'user'
                          ? 'bg-primary-light border border-primary-100'
                          : 'bg-white ml-2 shadow-sm border border-gray-100'
                      )}
                    >
                      {el?.type === 'user' ? (
                        el?.text &&
                        el?.text.split('\n').map((line, index) => (
                          <React.Fragment key={index}>
                            {line}
                            <br />
                          </React.Fragment>
                        ))
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5 }}
                          className="prose max-w-none leading-relaxed max-w-full flex flex-1 flex-col"
                        >
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeRaw, rehypeKatex]}
                            components={{
                              strong: ({ node, ...props }) => (
                                <strong {...props} />
                              ),
                              em: ({ node, ...props }) => <em {...props} />,
                              table: ({ children }) => (
                                <div className="overflow-x-auto">
                                  <table className="table-auto border-collapse border border-gray-300 w-full text-left min-w-full">
                                    {children}
                                  </table>
                                </div>
                              ),
                              th: ({ children }) => (
                                <th className="border border-gray-300 bg-gray-100 px-3 sm:px-4 py-2 text-sm sm:text-base">
                                  {children}
                                </th>
                              ),
                              td: ({ children }) => (
                                <td className="border border-gray-300 px-3 sm:px-4 py-2 text-sm sm:text-base">
                                  {children}
                                </td>
                              ),
                              h3: ({ node, ...props }) => (
                                <h3
                                  {...props}
                                  className="text-lg sm:text-xl font-bold py-2 sm:py-3"
                                />
                              ),
                              h2: ({ node, ...props }) => (
                                <h2
                                  {...props}
                                  className="text-xl sm:text-2xl font-bold py-2 sm:py-3"
                                />
                              ),
                              h1: ({ node, ...props }) => (
                                <h1
                                  {...props}
                                  className="text-2xl sm:text-3xl font-bold py-2 sm:py-3"
                                />
                              ),
                              h4: ({ node, ...props }) => (
                                <h4
                                  {...props}
                                  className="text-base sm:text-lg font-bold py-2 sm:py-3"
                                />
                              ),
                              p: ({ children }) => (
                                <p className="mb-2 text-sm sm:text-base">{children}</p>
                              ),
                              br: () => <div className="my-2 hidden" />,
                              hr: () => <div className="py-2 hidden" />,
                            }}
                          >
                            {cleanMath(el?.text)}
                          </ReactMarkdown>
                          {status && index === chats?.length - 1 && (
                            <div className="mt-2 text-sm sm:text-base font-medium text-gray-900">
                              <span className="animate-left-to-right-pulse">
                                {status}
                              </span>
                            </div>
                          )}
                        </motion.div>
                      )}
                    </div>

                    {el?.type === 'user' && (
                      <div className="pl-2 flex flex-shrink-0">
                        <UserCircleIcon height={32} width={32} />
                      </div>
                    )}
                  </div>
                ))}

                {showScrollButton &&
                  chats?.length &&
                  chats[chats.length - 1].type !== 'user' && (
                    <button
                      onClick={scrollToBottom}
                      className="fixed bottom-24 sm:bottom-28 right-4 sm:right-6 shadow-lg h-10 w-10 bg-[#FF8800] hover:bg-[#e67700] hover:cursor-pointer text-white rounded-full p-3 flex items-center justify-center transition-all duration-200 z-10"
                      aria-label="Scroll to bottom"
                    >
                      <ArrowDown size={20} />
                    </button>
                  )}
              </div>
            </div>

            {/* Chat Input */}
            <div className="flex flex-row p-3 sm:p-4 border w-full max-h-32 sm:max-h-36 group items-center rounded-lg border-gray-300 focus-within:border-[#FF8800] focus-within:shadow-lg transition-all duration-200 mt-4">
              <textarea
                ref={textareaRef}
                className="w-full text-base sm:text-lg border-none outline-none resize-none bg-transparent max-h-24 sm:max-h-28 custom-scrollbar disabled:cursor-not-allowed placeholder-gray-400"
                rows={1}
                placeholder="Ask REX anything..."
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                    e.preventDefault();
                    if (!text.trim() || !isFetchingStopped) return;
                    const tempChats = [...chats];
                    tempChats.push({
                      type: 'user',
                      text: text,
                      id: String(chats?.length),
                      isNew: true,
                    });
                    setChats([...tempChats]);
                    handleFetch(text);
                    setText('');
                  } else if (e.key === 'Enter' && e.ctrlKey) {
                    setText(text + '\n');
                  }
                }}
                autoFocus
              />
              {isFetchingStopped ? (
                <MicIcon
                  height={20}
                  width={20}
                  className="cursor-pointer hover:scale-110"
                  onClick={() => setIsRecordOn(true)}
                />
              ) : (
                <div
                  className="flex justify-center items-center h-5 w-5 rounded-full bg-primary-100 cursor-pointer"
                  onClick={() => {
                    stopFetchingRef.current = true;
                    setIsFetchingStopped(true);
                  }}
                >
                  <div className="h-2 w-2 rounded-xs bg-white" />
                </div>
              )}
            </div>
          </div>
        )
      )}
    </div>
  );
};

export const useStreamedResponse = () => {
  const BASEURL = import.meta.env.VITE_APP_API_URL;
  const abortControllerRef = useRef<AbortController | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);

  return {
    mutateAsync: async (
      url: string,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      requestBody: Record<string, any> | FormData,
      isFormData = false
    ) => {
      // Abort previous request
      abortControllerRef.current?.abort();
      abortControllerRef.current = new AbortController();

      const headers: HeadersInit = {
        'device-type': 'web',
        Authorization: `Bearer ${userData?.access_token || localStorage.getItem('access_token')}`,
      };

      let body: BodyInit | null;

      if (isFormData) {
        body = requestBody as FormData; // TypeScript ensures it's FormData
      } else {
        headers['Content-Type'] = 'application/json';
        body = JSON.stringify(requestBody); // Convert object to JSON string
      }

      const response = await fetch(BASEURL + url, {
        method: 'POST',
        headers,
        body,
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP Error: ${response.status} - ${errorText}`);
      }

      if (!response.body) throw new Error('ReadableStream not supported');

      return response.body.getReader();
    },
    abort: () => abortControllerRef.current?.abort(),
  };
};

export default Home;
