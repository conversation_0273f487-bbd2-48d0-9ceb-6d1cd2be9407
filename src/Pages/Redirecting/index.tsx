import { useEffect, useCallback } from 'react';
import { useParams } from 'react-router';
import { API_PATHS } from '@Helpers/Constants';
import { useDispatch } from 'react-redux';
import { useNavigate } from 'react-router-dom';
import { setUserData } from '@Redux/SystemControl/UserControle';
import { PATHS } from '@Config/Path.Config';
import { Loader } from '@Icons';
import axios from 'axios';

const BASEURL = import.meta.env.VITE_APP_API_URL;

const Redirecting = () => {
  const navigate = useNavigate();
  const dispatch = useDispatch();
  const { id, token } = useParams();

  const fetchProfileData = useCallback(async () => {
    try {
      const response = await axios.get(BASEURL + API_PATHS.USER_PROFILE, {
        headers: {
          Authorization: `Bearer ${token}`,
          'Accept-Language': 'en',
          'Content-Type': 'application/json',
          'device-type': 'web',
        },
      });
      const responseData = response?.data?.data;
      if (responseData) {
        dispatch(setUserData({ ...responseData, access_token: token }));
        navigate('/video/' + id);
      }
    } catch (error) {
      console.error('Error fetching profile:', error);
      navigate(PATHS.LOGIN);
    }
  }, []);

  useEffect(() => {
    if (token) {
      fetchProfileData();
    }
  }, [fetchProfileData, token]);

  return (
    <div className="flex items-center justify-center h-screen w-full">
      <Loader fill="#FF8800" height={36} width={36} />
    </div>
  );
};

export default Redirecting;
