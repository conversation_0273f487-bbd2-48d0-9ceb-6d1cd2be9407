/* eslint-disable @typescript-eslint/no-unused-vars */
import React, { useCallback, useEffect, useRef, useState } from 'react';
import {
  ClockCounterwiseIcon,
  MicIcon,
  UploadIcon,
  UserCircleIcon,
  AttachmentIcon,
  BackArrowIcon,
  PdfIcon,
  CloseIcon,
  RoboIcon,
  Loader,
  NewChatIcon,
  DownloadIcon,
} from '@Icons';
import { Button, Modal } from '@Components/UI';
import clsx from 'clsx';
import Recorder from '@Components/Recorder';
// import { useMutation } from 'react-query';
import { motion } from 'framer-motion';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import rehypeRaw from 'rehype-raw';
import { useLocation, useNavigate } from 'react-router';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';
import 'katex/dist/katex.min.css'; // Import KaTeX styles
import { useDispatch, useSelector } from 'react-redux';
import { ArrowDown } from 'lucide-react';
// import ChatComponent from './ChatComponent';

interface ChatObjectType {
  text: string | undefined;
  type: 'user' | 'bot';
  id: string;
  question?: string | undefined;
  answer?: string | undefined;
  file?: boolean;
  isNew?: boolean;
}

interface Session {
  id: number;
  session_title: string;
  session_id: string;
  started_at: string;
  ended_at: string | null;
}

const Home = (): JSX.Element => {
  const scrollPositionRef = useRef(0);
  const scrollHeightRef = useRef(0);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const scrollRef = useRef<HTMLDivElement | null>(null);
  const chatContainerRef = useRef<HTMLDivElement | null>(null);
  const uploadDocumentsInputRef = useRef<HTMLInputElement | null>(null);
  const firstMessageRef = useRef<HTMLDivElement | null>(null);
  const stopFetchingRef = useRef<boolean>(false);
  // const mutation = useStreamedResponse();
  const location = useLocation();
  const navigate = useNavigate();
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const api = new Api();

  const { state } = location;

  const [loading, setLoading] = useState<boolean>(true);
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [isFileDragging, setIsFileDragging] = useState<boolean>(false);
  const [startX, setStartX] = useState<number>(0);
  const [scrollLeft, setScrollLeft] = useState<number>(0);
  const [text, setText] = useState<string>('');
  const [chats, setChats] = useState<ChatObjectType[]>([]);
  const [isUploadReport, setIsUploadReport] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  // const [fileUrl, setFileURL] = useState<string | null>(null);
  const [isRecordOn, setIsRecordOn] = useState<boolean>(false);
  const [status, setStatus] = useState<string | null>(null);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [recentSearches, setRecentSearches] = useState<Session[]>([]);

  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [isFetchingStopped, setIsFetchingStopped] = useState<boolean>(true);
  const [link, setLink] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isSessionWarningOpen, setIsSessionWarningOpen] =
    useState<boolean>(false);
  const [isDonloadLoading, setIsDownloadLoading] = useState<boolean>(false);
  const [isNewChat, setIsNewChat] = useState<boolean>(
    state?.from !== 'HISTORY'
  );
  const [showScrollButton, setShowScrollButton] = useState(false);
  const [lastMessageMinHeight, setLastMessageMinHeight] = useState('69vh');

  const fetchSession = async (id: string | null) => {
    setLoading(true);
    try {
      const response = await api.get(
        `${state?.tab === 2 ? API_PATHS.DOC_SESSION_DETAILS : API_PATHS.CHAT_SESSION_DETAILS}/${id}/history`,
        {
          params: { page_size: 10, page: currentPage },
        }
      );

      let newChats: ChatObjectType[] = [];

      if (response?.data?.data?.list?.length) {
        response?.data?.data?.list?.forEach((el: ChatObjectType) => {
          newChats.push({ id: el?.id, type: 'user', text: el?.question });
          newChats.push({ id: el?.id, type: 'bot', text: el?.answer });
        });
      }
      if (chats.length) {
        newChats = [...newChats, ...chats];
      }
      setChats([...newChats]);

      // Prepend new messages
      setTotalPages(response?.data?.data?.total_pages);
      // setCurrentPage((prev) => prev + 1);
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isNewChat && chats?.length === 1) setIsNewChat(false);
  }, [isNewChat, chats, state?.from]);

  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const paymentStatus = searchParams.get('payment_status');

    if (paymentStatus === 'success') {
      addToast('success', 'Payment successful!');
      navigate('.', { replace: true }); // Clean URL
    }
  }, [location, navigate, addToast]);

  useEffect(() => {
    (async () => {
      if (!chats?.length)
        try {
          const searchResponse = await api.get(API_PATHS.RECENT_SEARCH);
          if (searchResponse?.data?.data?.list?.length)
            setRecentSearches([...searchResponse.data.data.list]);
        } catch (err) {
          console.log('err', err);
        }
    })();
    stopFetchingRef.current = false;
  }, [chats]);

  useEffect(() => {
    if (state?.from === 'HISTORY' && state?.sessionId) {
      setSessionId(state?.sessionId);
    } else {
      setLoading(false);
    }
  }, [state?.sessionId, state?.from]);

  useEffect(() => {
    if (isNewChat && sessionId) setSessionId(null);
  }, [isNewChat, sessionId]);

  useEffect(() => {
    if (sessionId && state?.from === 'HISTORY') fetchSession(sessionId);
    localStorage.setItem('SESSION_ID', sessionId ? sessionId : 'null');
  }, [sessionId, currentPage, state]);

  useEffect(() => {
    if (
      (isFetching &&
        chats[chats?.length - 1]?.type === 'bot' &&
        !status &&
        chats[chats?.length - 1]?.text !== '') ||
      (isFetching &&
        chats[chats?.length - 1]?.type === 'bot' &&
        status &&
        chats[chats?.length - 1]?.text !== '' &&
        chats[chats?.length - 1]?.isNew) ||
      (!isFetching &&
        chats[chats?.length - 1]?.type === 'bot' &&
        !status &&
        chats[chats?.length - 1]?.text !== '' &&
        chats[chats?.length - 1]?.isNew)
    ) {
      return;
    }
    if (
      chatContainerRef.current &&
      (status ||
        chats[chats?.length - 1]?.type === 'user' ||
        (state?.from === 'HISTORY' && currentPage === 1))
    ) {
      chatContainerRef.current.scrollTop =
        chatContainerRef.current.scrollHeight;
    }
  }, [chats, status, currentPage, isFetching]); // Runs when chats update

  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [text]);

  // Restore scroll position after loading more messages
  useEffect(() => {
    if (loading === false && currentPage > 1 && chatContainerRef.current) {
      // Calculate how much new content was added
      const newScrollHeight = chatContainerRef.current.scrollHeight;
      const addedHeight = newScrollHeight - scrollHeightRef.current;

      // Adjust scroll position to maintain the same relative position
      chatContainerRef.current.scrollTop =
        scrollPositionRef.current + addedHeight;
    }
  }, [loading, currentPage]);

  useEffect(() => {
    if (!chats.length) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (
          entries[0].isIntersecting &&
          currentPage <= totalPages &&
          !loading
        ) {
          // Store the current scroll position and height before loading more
          if (chatContainerRef.current) {
            scrollPositionRef.current = chatContainerRef.current.scrollTop;
            scrollHeightRef.current = chatContainerRef.current.scrollHeight;
          }
          setCurrentPage((p) => Math.min(p + 1, totalPages));
        }
      },
      { threshold: 1.0 }
    );

    if (firstMessageRef.current) observer.observe(firstMessageRef.current);

    return () => observer.disconnect();
  }, [chats, currentPage, totalPages, loading]);

  // Check if scrolled away from bottom
  const handleScroll = () => {
    if (chatContainerRef.current) {
      const { scrollTop, scrollHeight, clientHeight } =
        chatContainerRef.current;
      // Calculate distance from bottom
      const distanceFromBottom = scrollHeight - scrollTop - clientHeight;
      // Show button if not at bottom (with a small threshold)
      const shouldShow = distanceFromBottom > 150;

      // Only update state if the value changes to prevent unnecessary re-renders
      if (shouldShow !== showScrollButton) {
        setShowScrollButton(shouldShow);
      }
    }
  };

  // Make sure this useEffect runs after component mounts and adds the event listener
  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (chatContainer) {
      // Add the event listener
      chatContainer.addEventListener('scroll', handleScroll);

      // Initial check for button visibility
      handleScroll();

      // Clean up event listener on unmount
      return () => {
        chatContainer.removeEventListener('scroll', handleScroll);
      };
    }
  }, [handleScroll]); // Add showScrollButton to dependencies so handleScroll closure has latest value

  const handleMouseDown = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!scrollRef.current) return;
    setIsDragging(true);
    setStartX(e.pageX - scrollRef.current.offsetLeft);
    setScrollLeft(scrollRef.current.scrollLeft);
  };

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    if (!isDragging || !scrollRef.current) return;
    const x = e.pageX - scrollRef.current.offsetLeft;
    const walk = (x - startX) * 2; // Adjust scrolling speed
    scrollRef.current.scrollLeft = scrollLeft - walk;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
  };

  const handleUploadClick = () => {
    uploadDocumentsInputRef.current?.click();
  };

  const validateFile = (file: File): boolean => {
    return file.type === 'application/pdf';
  };

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsFileDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsFileDragging(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsFileDragging(false);

    const file = event.dataTransfer.files[0];

    if (file && validateFile(file)) {
      handleFileUpload({
        target: { files: [file] },
      } as unknown as React.ChangeEvent<HTMLInputElement>);
    }
  }, []);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && validateFile(file)) {
      setSelectedFile(file);
      // setFileURL(URL.createObjectURL(file));
    } else {
      alert('Only PDF files are allowed.');
    }
  };

  const onRecordClose = (voiceText: string) => {
    if (voiceText) {
      // const tempChats = [...chats];
      // tempChats.push({
      //   type: 'user',
      //   text: voiceText,
      //   id: String(chats?.length),
      // });
      // setChats([...tempChats]);
      setText(voiceText);
      textareaRef.current?.focus();
    }
  };

  const { mutateAsync } = useStreamedResponse();
  const handleFetch = async (userText: string, isFormData = false) => {
    setIsFetching(true);
    setIsFetchingStopped(false);
    stopFetchingRef.current = false; // Reset stop flag before fetching
    if (!userText.trim()) return;
    const tempChats: ChatObjectType[] = [...chats];

    tempChats.push({
      type: 'user',
      text: userText,
      id: String(chats.length),
      file: !!selectedFile || !!link,
      isNew: true,
    });
    setChats([...tempChats]);
    setStatus(null);
    setText('');

    try {
      const requestBody = isFormData
        ? new FormData()
        : { issue: userText, session_id: sessionId ?? '' };

      if (isFormData && requestBody instanceof FormData) {
        if (selectedFile) requestBody.append('document', selectedFile);
        if (link) requestBody.append('pdf_url ', link);
        // requestBody.append('session_id', sessionId ?? '');
      }

      const reader = await mutateAsync(
        isFormData
          ? 'rag/analyse-document'
          : chats[0]?.file || state?.tab === 2
            ? 'rag/ask-question-document-upload'
            : 'rag/ask-rex', // rag/ask-question-document-upload
        requestBody,
        isFormData
      );

      // const decoder = new TextDecoder();
      // let accumulatedText = '';
      // const botMessage: ChatObjectType = {
      //   type: 'bot',
      //   text: '',
      //   id: String(chats.length + 1),
      // };
      // tempChats.push(botMessage);
      // setChats([...tempChats]);

      // let textBuffer = ''; // Holds incomplete line fragments

      // while (true) {
      //   if (stopFetchingRef.current) {
      //     console.log('Fetching stopped by user');
      //     break;
      //   }

      //   const { value, done } = await reader.read();
      //   if (done) break;

      //   const chunk = decoder.decode(value, { stream: true });
      //   textBuffer += chunk; // Accumulate stream content

      //   // Process only complete lines
      //   const lines = textBuffer.split('\n');
      //   textBuffer = lines.pop() || ''; // Keep the last (possibly incomplete) line for next round

      //   const jsonChunks = lines
      //     .filter((line) => line.startsWith('data: '))
      //     .map((line) => {
      //       try {
      //         return JSON.parse(line.replace(/^data:\s*/, ''));
      //       } catch (e) {
      //         console.warn('Skipping malformed JSON:', line);
      //         return null;
      //       }
      //     })
      //     .filter(Boolean);

      //   jsonChunks.forEach((data) => {
      //     if (data?.type === 'solution_chunk') {
      //       accumulatedText += data.content + ' ';
      //       botMessage.text = accumulatedText;
      //       setStatus(null);
      //       setChats([...tempChats]);
      //     } else if (data?.type === 'complete' || data?.type === 'error') {
      //       if (data?.session_id) setSessionId(data?.session_id);
      //       botMessage.text =
      //         accumulatedText +
      //         (data?.error || '') +
      //         (data?.relevant_content
      //           ? ' ✔'
      //           : data?.content
      //             ? data?.content
      //             : '');
      //       setStatus(null);
      //       setChats([...tempChats]);
      //     } else if (data?.type === 'status' || data?.type === 'metadata') {
      //       if (data?.session_id) setSessionId(data?.session_id);
      //       setStatus(data.message || data.content || status || '');
      //     }
      //   });
      // }

      const decoder = new TextDecoder();
      let textBuffer = '';
      let accumulatedText = '';

      const botMessage: ChatObjectType = {
        type: 'bot',
        text: '',
        id: String(chats.length + 1),
        isNew: true,
      };
      tempChats.push(botMessage);
      setChats([...tempChats]);

      while (true) {
        if (stopFetchingRef.current) {
          console.log('Fetching stopped by user');
          break;
        }

        const { value, done } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        textBuffer += chunk;

        // Ensure all lines are split properly (including \r\n or \n)
        let newlineIndex;
        while ((newlineIndex = textBuffer.indexOf('\n')) >= 0) {
          const rawLine = textBuffer.slice(0, newlineIndex).trim();
          textBuffer = textBuffer.slice(newlineIndex + 1);

          if (!rawLine.startsWith('data: ')) continue;

          const jsonStr = rawLine.replace(/^data:\s*/, '');
          if (!jsonStr || jsonStr === '[DONE]') continue;

          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          let parsedData: any;
          try {
            parsedData = JSON.parse(jsonStr);
          } catch (e) {
            console.warn('JSON parse failed, skipping:', jsonStr);
            continue;
          }

          // Now process the parsed chunk
          if (parsedData?.type === 'solution_chunk') {
            accumulatedText += parsedData.content + ' ';
            botMessage.text = accumulatedText;
            setStatus(null);
            setChats([...tempChats]);
          } else if (
            parsedData?.type === 'complete' ||
            parsedData?.type === 'error'
          ) {
            if (parsedData?.session_id) setSessionId(parsedData.session_id);
            botMessage.text =
              accumulatedText +
              (parsedData?.error || '') +
              (parsedData?.relevant_content
                ? ' ✔'
                : parsedData?.content
                  ? parsedData.content
                  : '');
            setStatus(null);
            setChats([...tempChats]);
          } else if (
            parsedData?.type === 'status' ||
            parsedData?.type === 'metadata'
          ) {
            if (parsedData?.session_id) setSessionId(parsedData.session_id);
            setStatus(parsedData.message || parsedData.content || status || '');
          }
        }
      }

      setIsFetching(false);
      setIsFetchingStopped(true);
    } catch (error) {
      if ((error as DOMException).name === 'AbortError') {
        console.log('Request aborted');
      } else {
        console.error('Streaming failed:', error);
      }
      setIsFetching(false);
      setIsFetchingStopped(true);
    }
  };

  // Function to validate if the link is a PDF
  const validatePdfUrl = (url: string): boolean => {
    const pdfRegex = /^(https?:\/\/.*\.pdf)$/i; // Checks if the URL ends with .pdf
    return pdfRegex.test(url);
  };

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLink(e.target.value);
    if (error) setError(''); // Clear error when user types
  };

  // Handle button click (Validation)
  const handleSubmitLink = () => {
    if (!validatePdfUrl(link)) {
      setError('Please enter a valid PDF URL.');
      return;
    }
    setChats([
      {
        type: 'user',
        file: true,
        text: link.split('/').pop(),
        id: '1',
        isNew: true,
      },
    ]);
    setIsUploadReport(false); // Close modal or perform any action
    handleFetch(link.split('/').pop() as string, true);
  };

  const cleanMath = (text: string | undefined) =>
    text ? text.replace(/\\\[/g, '$$').replace(/\\\]/g, '$$') : '';

  const downloadFile = async (id: number) => {
    try {
      setIsDownloadLoading(true);
      const response = await api.get(`rag/documents/${id}/download/`); // Replace with your actual API endpoint
      const data = response?.data;

      if (data.status !== 1 || !data.data.document_url) {
        throw new Error('Invalid document URL');
      }

      const fileResponse = await fetch(data.data.document_url);
      const blob = await fileResponse.blob();
      const url = window.URL.createObjectURL(blob);

      const a = document.createElement('a');
      a.href = url;
      a.download = 'downloaded-file'; // Replace with actual filename if available
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error('Download failed:', error);
    } finally {
      setIsDownloadLoading(false);
    }
  };

  const scrollToBottom = () => {
    if (chatContainerRef.current) {
      chatContainerRef.current.scrollTo({
        top: chatContainerRef.current.scrollHeight,
        behavior: 'smooth',
      });
    }
  };

  // Add this useEffect to handle the calculation using IDs
  useEffect(() => {
    if (chats.length > 2 && status && chats[chats?.length - 1]?.isNew) {
      // Get the last and second-to-last message IDs
      const lastMessageId = chats[chats.length - 1]?.id;
      const secondLastMessageId = chats[chats.length - 2]?.id;

      // Use setTimeout to ensure DOM is updated
      setTimeout(() => {
        const secondLastElement = document.getElementById(secondLastMessageId);

        if (secondLastElement && chats[chats.length - 1]?.isNew) {
          const secondLastHeight =
            secondLastElement.getBoundingClientRect().height;
          const viewportHeight = window.innerHeight * 0.76; // 76vh in pixels
          const calculatedHeight = Math.max(
            0,
            viewportHeight - secondLastHeight
          );
          setLastMessageMinHeight(`${calculatedHeight}px`);
        }
      }, 0);
    }
  }, [chats, status]);

  return (
    <div
      className={clsx(
        'flex flex-col h-full w-full relative overflow-hidden',
        isNewChat && 'items-center justify-center'
      )}
    >
      {sessionId && loading && !chats?.length && (
        <div className="flex flex-1 h-ful -w-full justify-center items-center">
          <Loader height={46} width={46} fill="#fff" />
        </div>
      )}
      {isUploadReport && (
        <div
          className="absolute top-8 left-8 cursor-pointer hover:scale-105"
          onClick={() => setIsUploadReport(false)}
        >
          <BackArrowIcon height={28} width={28} />
        </div>
      )}
      {isRecordOn && (
        <Recorder setIsRecordOn={setIsRecordOn} onClose={onRecordClose} />
      )}
      {isNewChat && !sessionId && !loading ? (
        <>
          <div className={clsx('flex h-22 w-22 mb-15', isUploadReport && '-mt-16')}>
            <RoboIcon height={120} width={120} />
          </div>
          {!isUploadReport ? (
            <div className="flex items-center p-4 border w-1/2 min-h-10 max-h-45 group rounded-[20px] border-b-primary focus-within:border-primary-100 focus-within:shadow-primary gap-x-2">
              <textarea
                ref={textareaRef}
                className="flex-1 p-2 text-lg border-none outline-none resize-none bg-transparent min-h-10 max-h-45 custom-scrollbar"
                rows={1}
                placeholder="Ask REX anything..."
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                    e.preventDefault();
                    if (!text.trim()) return;
                    const tempChats: ChatObjectType[] = [];
                    tempChats.push({
                      type: 'user',
                      text: text,
                      id: String(chats?.length),
                      isNew: true,
                    });
                    setChats([...tempChats]);
                    handleFetch(text);
                    setText('');
                  } else if (e.key === 'Enter' && e.ctrlKey) {
                    setText(text + '\n');
                  }
                }}
              />
              <MicIcon
                height={20}
                width={20}
                className="cursor-pointer hover:scale-110 flex-shrink-0"
                onClick={() => setIsRecordOn(true)}
              />
            </div>
          ) : (
            <div
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onDragLeave={handleDragLeave}
              className={clsx(
                'flex text-[14px] text-secondary text-center items-center flex-col py-8 px-18 border border-dashed w-1/2 group  bg-primary-light rounded-[16px] border-primary-100 focus-within:shadow-primary',
                isFileDragging && 'scale-110'
              )}
            >
              <input
                ref={uploadDocumentsInputRef}
                type="file"
                className="sr-only"
                accept="application/pdf"
                onChange={handleFileUpload}
                multiple={false}
              />
              <div className="py-4">
                <UploadIcon height={64} width={64} />
              </div>
              <div className="pt-4 text-lg">
                Drag & Drop or{' '}
                <span
                  className={clsx(
                    'text-primary-100 hover:underline hover:underline-offset-2',
                    link ? 'cursor-not-allowed' : 'cursor-pointer'
                  )}
                  onClick={() => {
                    if (!link) handleUploadClick();
                  }}
                  title={link && 'Link is already present'}
                >
                  Choose File
                </span>{' '}
                to upload PDF
              </div>

              {/* <div className="py-4">- or -</div>
              <div className="pb-4">
                <span className="text-primary-100">Paste link</span> to get
                result
              </div>
              <div
                className={clsx(
                  'flex items-center  hover:border-primary-100 focus-within:border-primary-100 flex-1 py-1.5 px-4 border rounded-full border-b-primary focus-within:ring-1 focus-within:ring-teal-100 focus-within:shadow-primary w-full',
                  selectedFile ? 'bg-[#e5e7eb]' : 'bg-white'
                )}
              >
                <div className="flex pr-2">
                  <AttachmentIcon height={20} width={20} />
                </div>
                <input
                  type="text"
                  value={link}
                  onChange={handleInputChange}
                  disabled={!!selectedFile}
                  className={clsx(
                    'w-full  flex-1 p-1.5 border-none outline-none appearance-none disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-base'
                  )}
                  placeholder="Paste link"
                  title={selectedFile ? 'File is already selected' : ''}
                />
              </div> */}
              {error && <p className="text-red-500 text-sm mt-1">{error}</p>}

              {selectedFile && (
                <div className="py-4 group flex flex-row gap-x-2 w-full ">
                  <PdfIcon height={20} width={20} />
                  <div>{selectedFile?.name}</div>
                  <div
                    className="hidden group-hover:flex cursor-pointer"
                    onClick={() => {
                      setSelectedFile(null);
                      // setFileURL(null);
                    }}
                  >
                    <CloseIcon height={10} width={10} />
                  </div>
                </div>
              )}
            </div>
          )}
          {!isUploadReport ? (
            <>
              {recentSearches?.length > 0 && (
                <>
                  <div className="flex pt-12 pb-6 gap-x-2 justify-start items-center w-1/2">
                    <ClockCounterwiseIcon height={24} width={24} />
                    <span className="text-secondary text-[18px]">
                      Recent Search
                    </span>
                  </div>
                  <div className="w-1/2">
                    <div
                      ref={scrollRef}
                      className="max-w-full w-auto select-none flex overflow-y-hidden flex-row items-start justify-start  overflow-x-scroll whitespace-nowrap gap-x-4 scrollbar-hidden pb-2 cursor-grab active:cursor-grabbing px-1"
                      onMouseDown={handleMouseDown}
                      onMouseLeave={handleMouseUp}
                      onMouseUp={handleMouseUp}
                      onMouseMove={handleMouseMove}
                    >
                      {recentSearches.map((el: Session) => (
                        <div
                          key={el?.id}
                          className="py-2 px-4 border border-primary-100 rounded-[10px] max-w-1/2 w-fit cursor-pointer shadow-primary h-22"
                          onClick={() => {
                            setChats([]);
                            navigate('.', {
                              replace: true,
                              state: { from: 'HISTORY' },
                            });
                            setIsNewChat(false);
                            localStorage.setItem('NAVIGATED_FROM', 'HISTORY');
                            setSessionId(el?.session_id);
                            fetchSession(el?.session_id);
                          }}
                        >
                          <div className="flex text-secondary flex-wrap overflow-hidden break-words min-w-[164px] whitespace-normal text-ellipsis custom-clamp">
                            {el?.session_title}
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </>
          ) : (
            (selectedFile || link) && (
              <div>
                <Button
                  text={'Done'}
                  width="w-42"
                  className="mt-6 bg-primary-100 text-white group-hover:!bg-white group-hover:!text-primary-100"
                  variant="other"
                  type="button"
                  onClick={() => {
                    if (link) {
                      handleSubmitLink();
                    } else {
                      setIsUploadReport(false);
                      if (selectedFile)
                        handleFetch(selectedFile?.name as string, true);
                    }
                  }}
                />
              </div>
            )
          )}
        </>
      ) : (
        chats?.length > 0 &&
        !isNewChat && (
          <div className="flex overflow-hidden h-screen max-h-screen flex-col m-6">
            <div className="flex flex-1 flex-col">
              <div className="flex pb-4 w-full justify-between items-center">
                <div className="font-semibold text-2xl">Search Result</div>
                <div className="flex items-center gap-x-4">
                  {state?.isDownloadAvailable && state?.id && (
                    <div
                      className={
                        isDonloadLoading
                          ? 'cursor-not-allowed'
                          : 'cursor-pointer'
                      }
                    >
                      {isDonloadLoading ? (
                        <Loader height={20} width={20} />
                      ) : (
                        <DownloadIcon
                          height={22}
                          width={22}
                          onClick={() => downloadFile(state?.id)}
                          className="mr-2"
                        />
                      )}
                    </div>
                  )}
                  <div
                    className="cursor-pointer"
                    onClick={() => {
                      stopFetchingRef.current = true;
                      setIsFetchingStopped(true);
                      setSessionId(null);
                      setChats([]);
                      setIsNewChat(true);
                      setLink('');
                      setSelectedFile(null);
                      if (state?.from === 'HISTORY')
                        navigate('.', { replace: true, state: null }); // Clears the state
                    }}
                  >
                    <NewChatIcon height={24} width={24} />
                  </div>
                </div>
              </div>
              {loading && chats?.length > 0 && sessionId && currentPage > 1 && (
                <div className="py-2 flex justify-center items-center w-full ">
                  <Loader fill="#fff" height={36} width={36} />
                </div>
              )}
              <div
                ref={chatContainerRef}
                className={clsx(
                  'flex flex-col h-full max-h-[76vh] px-4 overflow-auto custom-scrollbar w-full flex-1',
                  chats?.length &&
                  chats[chats?.length - 1]?.isNew &&
                  'scroll-smooth'
                )}
              >
                {chats.map((el, index) => (
                  <div
                    className={clsx(
                      'flex w-full py-4 items-start',
                      el?.type === 'user' ? 'justify-end' : 'justify-start',
                      index === chats?.length - 1 &&
                      chats?.length > 2 &&
                      el?.isNew &&
                      `min-h-[69vh]`
                    )}
                    style={
                      index === chats?.length - 1 &&
                        chats?.length > 2 &&
                        el?.isNew &&
                        lastMessageMinHeight
                        ? { minHeight: lastMessageMinHeight ?? 'auto' }
                        : {}
                    }
                    id={el?.id}
                    key={`${el?.id}_${el?.type}`}
                    ref={index === 0 ? firstMessageRef : null}
                  >
                    {el?.type === 'bot' && (
                      <div className="flex h-8 w-8">
                        <RoboIcon />
                      </div>
                    )}
                    <div
                      className={clsx(
                        'flex flex-wrap bg-primary-light px-4  rounded-[10px] max-w-full',
                        el?.type === 'user'
                          ? 'bg-primary-light border border-primary-100 py-2.5'
                          : 'bg-white ml-2'
                      )}
                    >
                      {el?.type === 'user' ? (
                        el?.text &&
                        el?.text.split('\n').map((line, index) => (
                          <React.Fragment key={index}>
                            {el?.text?.split('.').pop() === 'pdf' && (
                              <PdfIcon
                                height={20}
                                width={20}
                                className="mr-2"
                              />
                            )}
                            {line}
                            <br />
                          </React.Fragment>
                        ))
                      ) : (
                        <motion.div
                          initial={{ opacity: 0 }}
                          animate={{ opacity: 1 }}
                          transition={{ duration: 0.5 }}
                          className="prose max-w-none leading-10 max-w-full flex flex-1 flex-col"
                        >
                          <ReactMarkdown
                            remarkPlugins={[remarkGfm, remarkMath]}
                            rehypePlugins={[rehypeRaw, rehypeKatex]}
                            components={{
                              strong: ({ node, ...props }) => (
                                <strong {...props} />
                              ),
                              em: ({ node, ...props }) => <em {...props} />,
                              table: ({ children }) => (
                                <table className="table-auto border-collapse border border-gray-300 w-full text-left">
                                  {children}
                                </table>
                              ),
                              th: ({ children }) => (
                                <th className="border border-gray-300 bg-gray-100 px-4 py-2">
                                  {children}
                                </th>
                              ),
                              td: ({ children }) => (
                                <td className="border border-gray-300 px-4 py-2">
                                  {children}
                                </td>
                              ),
                              h3: ({ node, ...props }) => (
                                <h3
                                  {...props}
                                  className="text-xl font-bold py-3"
                                />
                              ),
                              h2: ({ node, ...props }) => (
                                <h2
                                  {...props}
                                  className="text-2xl font-bold py-3"
                                />
                              ),
                              h1: ({ node, ...props }) => (
                                <h1
                                  {...props}
                                  className="text-3xl font-bold py-3"
                                />
                              ),
                              h4: ({ node, ...props }) => (
                                <h4
                                  {...props}
                                  className="text-lg font-bold py-3"
                                />
                              ),
                              p: ({ children }) => (
                                <p className="mb-2">{children}</p>
                              ),
                              br: () => <div className="my-2 hidden" />,
                              hr: () => <div className="py-2 hidden" />,
                            }}
                          >
                            {cleanMath(el?.text)}
                          </ReactMarkdown>
                          {status && index === chats?.length - 1 && (
                            <div className="mt-2 text-md font-medium text-gray-900">
                              <span className="animate-left-to-right-pulse">
                                {status}
                              </span>
                            </div>
                          )}
                        </motion.div>
                      )}
                    </div>

                    {el?.type === 'user' && (
                      <div className="pl-2 flex">
                        <UserCircleIcon height={32} width={32} />
                      </div>
                    )}
                  </div>
                ))}
                {showScrollButton &&
                  chats?.length &&
                  chats[chats.length - 1].type !== 'user' && (
                    <button
                      onClick={scrollToBottom}
                      className="absolute shadow-primary  m-auto h-10 w-10 bottom-21 right-0 left-0 bg-primary-100 hover:cursor-pointer text-white rounded-full p-3 flex items-center justify-center transition-opacity"
                      aria-label="Scroll to bottom"
                    >
                      <ArrowDown size={20} />
                    </button>
                  )}
              </div>
            </div>
            <div className="flex flex-row p-1 border w-full max-h-36 group items-center px-2 rounded-[10px] border-b-primary focus-within:border-primary-100 focus-within:shadow-primary">
              <textarea
                ref={textareaRef}
                className="w-full p-2 text-lg border-none outline-none resize-none bg-transparent max-h-30 custom-scrollbar disabled:cursor-not-allowed"
                rows={1}
                placeholder="Ask REX anything..."
                value={text}
                onChange={(e) => setText(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.ctrlKey && !e.shiftKey) {
                    e.preventDefault(); // Prevents newline
                    if (!text.trim() || !isFetchingStopped) return; // Do nothing if input is empty
                    const tempChats = [...chats];
                    tempChats.push({
                      type: chats.length % 2 === 0 ? 'user' : 'bot',
                      text: text,
                      id: String(chats?.length),
                      isNew: true,
                    });
                    setChats([...tempChats]);
                    handleFetch(text);
                    setText(''); // Clear input after submission
                  } else if (e.key === 'Enter' && e.ctrlKey) {
                    setText(text + '\n'); // Allows newline with Ctrl + Enter
                  }
                }}
                autoFocus
              />
              {isFetchingStopped ? (
                <>
                  <MicIcon
                    height={20}
                    width={20}
                    className="cursor-pointer hover:scale-110"
                    onClick={() => setIsRecordOn(true)}
                  />

                  <UploadIcon
                    height={18}
                    width={18}
                    className="cursor-pointer hover:scale-110 mx-3"
                    onClick={() => {
                      if (
                        localStorage.getItem('NAVIGATED_FROM') === 'HISTORY'
                      ) {
                        setIsSessionWarningOpen(false);
                        localStorage.removeItem('NAVIGATED_FROM');
                        setIsUploadReport(true);
                        setChats([]);
                        setSessionId(null);
                        setLink('');
                        setSelectedFile(null);
                        setIsNewChat(true);
                      } else setIsSessionWarningOpen(true);
                    }}
                  />
                </>
              ) : (
                <div
                  className="flex justify-center items-center h-5 w-5 rounded-full bg-primary-100 cursor-pointer"
                  onClick={() => {
                    stopFetchingRef.current = true;
                    setIsFetchingStopped(true);
                  }}
                >
                  <div className="h-2 w-2 rounded-xs bg-white" />
                </div>
              )}
            </div>
          </div>
        )
      )}
      <Modal isOpen={isSessionWarningOpen} hideCloseButton>
        <div className="flex flex-col gap-y-8 p-3">
          <div className="text-center font-semibold text-lg text-black">
            Your chat session will end. Are you sure you want to continue?
          </div>
          <div className="flex w-full gap-6">
            <Button
              text="Yes"
              onClick={() => {
                setIsSessionWarningOpen(false);
                localStorage.removeItem('NAVIGATED_FROM');
                setIsUploadReport(true);
                setChats([]);
                setSessionId(null);
                setLink('');
                setSelectedFile(null);
                setIsNewChat(true);
              }}
            />
            <Button
              text="No"
              variant="outline"
              onClick={() => setIsSessionWarningOpen(false)}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

// interface SolutionChunk {
//   type: 'solution_chunk';
//   content: string;
// }

// interface CompleteMessage {
//   type: 'complete';
//   relevant_content: boolean;
//   state_id: string;
//   full_solution: string;
//   error?: string;
// }

// interface MetadataMessage {
//   type: 'metadata' | 'status' | 'error';
//   message?: string;
//   session_id?: string;
// }

// type StreamResponse = SolutionChunk | CompleteMessage | MetadataMessage;

export const useStreamedResponse = () => {
  const BASEURL = import.meta.env.VITE_APP_API_URL;
  const abortControllerRef = useRef<AbortController | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);

  return {
    mutateAsync: async (
      url: string,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      requestBody: Record<string, any> | FormData,
      isFormData = false
    ) => {
      // Abort previous request
      abortControllerRef.current?.abort();
      abortControllerRef.current = new AbortController();

      const headers: HeadersInit = {
        'device-type': 'web',
        Authorization: `Bearer ${userData?.access_token || localStorage.getItem('access_token')}`,
      };

      let body: BodyInit | null;

      if (isFormData) {
        body = requestBody as FormData; // TypeScript ensures it's FormData
      } else {
        headers['Content-Type'] = 'application/json';
        body = JSON.stringify(requestBody); // Convert object to JSON string
      }

      const response = await fetch(BASEURL + url, {
        method: 'POST',
        headers,
        body,
        signal: abortControllerRef.current.signal,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP Error: ${response.status} - ${errorText}`);
      }

      if (!response.body) throw new Error('ReadableStream not supported');

      return response.body.getReader();
    },
    abort: () => abortControllerRef.current?.abort(),
  };
};

export default Home;
