import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, InputField, Modal } from '@Components/UI';
import clsx from 'clsx';
import { useCallback, useEffect, useRef, useState } from 'react';
import {
  CirclePlusIcon,
  CircleTickIcon,
  ClockIcon,
  CloseIcon,
  CommentIcon,
  DeleteIcon,
  EditIcon,
  Loader,
  MenuIcon,
  ReportIcon,
  SearchIcon,
} from '@Icons';
import { AnimatePresence, motion } from 'framer-motion';
import { ExpandableText } from '@Components/Common';
import { useDebounce } from '@Hooks/useDebouce';
import AddQuestionModal from './AddQuestionModal';
import Api from '@Helpers/Api';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { API_PATHS } from '@Helpers/Constants';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import relativeTime from 'dayjs/plugin/relativeTime';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import CommentSection from './CommentSection';
import { useSelector } from 'react-redux';
import { trim } from '@Helpers/Utils';
import ReportModal from './ReportModal';

dayjs.extend(utc);
dayjs.extend(relativeTime);
dayjs.extend(localizedFormat);
dayjs.extend(isToday);
dayjs.extend(isYesterday);

export const formatPostDate = (utcDateString: string): string => {
  const localDate = dayjs.utc(utcDateString).local();

  if (localDate.isToday()) {
    return localDate.format('hh:mm A'); // Example: 04:30 PM
  }

  if (localDate.isYesterday()) {
    return 'Yesterday';
  }

  const now = dayjs();
  const diffInDays = now.diff(localDate, 'day');
  const diffInWeeks = now.diff(localDate, 'week');
  const diffInMonths = now.diff(localDate, 'month');

  if (diffInDays < 7) {
    return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
  }

  if (diffInWeeks < 4) {
    return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
  }

  return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
};

const breadcrumbItems = [
  { label: 'Home', link: '/' },
  { label: `Tradie's Hub` },
];

const tabDataList = [
  {
    id: 1,
    name: 'All Questions',
  },
  {
    id: 2,
    name: 'My Questions',
  },
];

interface Author {
  id: number;
  email: string;
  profile_picture: string;
  first_name: string;
  last_name: string;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
}

export interface Post {
  id: number;
  title: string;
  content: string;
  author: Author;
  category_ids: Category[];
  created_at: string;
  updated_at: string;
  comment_count: number;
}

interface ReportType {
  id: number;
  type: 'commentreplay' | 'comment' | 'post';
}

const BuilderHub = (): JSX.Element => {
  const api = new Api();
  const { addToast } = useToast();
  const observerRef = useRef<HTMLDivElement | null>(null);
  const popoverRef = useRef<HTMLDivElement | null>(null);
  const abortControllerRef = useRef<AbortController | null>(null);
  const scrollContainerRef = useRef<HTMLDivElement | null>(null);
  const loadingRef = useRef<boolean>(false);

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);

  const [activeCategory, setActiveCategory] = useState<number[]>([]);
  const [search, setSearch] = useState<string>('');
  const [selectedPost, setPost] = useState<Post | null>(null);
  const [isAddOpen, setIsAddOpen] = useState<boolean>(false);
  const [activeTab, setActiveTab] = useState<number>(1);
  const debouncedSearchTerm = useDebounce(search, 500);

  const [postList, setPostList] = useState<Post[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [isPostLoading, setIsPostLoading] = useState<boolean>(false);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [categoryList, setCategotyList] = useState<Category[]>([]);
  const [isPopoverOpen, setIsPopoverOpen] = useState<number | null>(null);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [isReportOpen, setIsReportOpen] = useState<null | ReportType>(null);
  const [isSubmitLoading, setIsSubmitLoading] = useState<boolean>(false);
  const [isInitialLoad, setIsInitialLoad] = useState<boolean>(true);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const clickedInside = popoverRef.current?.contains(event.target as Node);

      if (!clickedInside) {
        setIsPopoverOpen(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const catRes = await api.get(API_PATHS.GET_BH_CATEGORY_LIST);
        if (catRes?.data?.status) {
          setCategotyList([...catRes.data.data] as Category[]);
        } else {
          addToast('error', catRes?.data?.message);
        }
      } catch (err) {
        addToast('error', err as string);
      }
    })();
  }, []);

  // Handle post updates on tab/category change
  useEffect(() => {
    setPost(null);
    // If debouncedSearchTerm is active, the search effect is already handling the fetch.
    if (debouncedSearchTerm.trim() === '') {
      setCurrentPage(1);
      setPostList([]);
      fetchPostList();
    }
  }, [activeTab, activeCategory]);

  // Handle search term changes
  useEffect(() => {
    if (debouncedSearchTerm.trim() !== '') {
      setCurrentPage(1);
      setPostList([]);
      fetchPostList(true);
    }
    setIsSuccess(false);
  }, [debouncedSearchTerm]);

  // Handle successful operations
  useEffect(() => {
    if (isSuccess) {
      setCurrentPage(1);
      setPostList([]);
      fetchPostList(true);
      setIsSuccess(false);
    }
  }, [isSuccess]);

  // Optimized Infinite Scroll Observer with useCallback
  const initializeObserver = useCallback(() => {
    if (!observerRef.current || loadingRef.current) return;

    const observer = new IntersectionObserver(
      (entries) => {
        const target = entries[0];
        if (target.isIntersecting && !loadingRef.current && currentPage < totalPages) {
          loadingRef.current = true;
          setCurrentPage((prevPage) => prevPage + 1);
        }
      },
      { threshold: 0.5, rootMargin: '100px' }
    );

    if (observerRef.current) {
      observer.observe(observerRef.current);
    }

    return () => {
      if (observerRef.current) {
        observer.unobserve(observerRef.current);
      }
      observer.disconnect();
    };
  }, [currentPage, totalPages]);

  useEffect(() => {
    const cleanup = initializeObserver();
    return cleanup;
  }, [initializeObserver, postList.length]);

  // Effect to handle page changes
  useEffect(() => {
    if (!isInitialLoad && currentPage > 1) {
      fetchPostList(false);
    }
  }, [currentPage]);

  const fetchPostList = useCallback(async (reset: boolean = false) => {
    try {
      // If there's an ongoing request, abort it
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }

      // Create a new abort controller for this request
      abortControllerRef.current = new AbortController();

      if (reset) {
        setCurrentPage(1);
      }

      setIsPostLoading(true);
      loadingRef.current = true;

      const postRes = await api.get(API_PATHS.GET_POST_LIST, {
        params: {
          page: reset ? 1 : currentPage,
          page_size: 10,
          is_my_post: activeTab === 2,
          category_ids: activeCategory,
          search: debouncedSearchTerm,
        },
        signal: abortControllerRef.current.signal, // Pass the signal to the request
      });

      if (postRes?.data?.status) {
        if (reset) {
          setPostList([...postRes.data.data.list] as Post[]);
        } else {
          setPostList((prev) => [...prev, ...postRes.data.data.list] as Post[]);
        }
        setTotalPages(postRes?.data?.data?.total_pages);
      } else {
        addToast('error', postRes?.data?.message);
      }

      setIsInitialLoad(false);
      setIsPostLoading(false);
      loadingRef.current = false;
    } catch (err) {
      // Don't show error toast if request was aborted
      if (err !== 'CANCELLED') {
        addToast('error', err as string);
        setIsPostLoading(false);
        loadingRef.current = false;
      }
    } finally {
      // Clear the reference if this request completed
      if (abortControllerRef.current?.signal.aborted === false) {
        abortControllerRef.current = null;
      }
    }
  }, [currentPage, activeTab, activeCategory, debouncedSearchTerm]);

  const searchHandler = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;
    setSearch(searchTerm);
  };

  const openAddmodal = () => {
    setIsAddOpen(true);
  };

  const closeAddmodal = () => {
    if (selectedPost) setPost(null);
    setIsAddOpen(false);
  };

  const handleCommentModalClose = (commentsUpdated?: boolean) => {
    setPost(null);
    if (commentsUpdated) setIsSuccess(true);
  };

  const handleDeletePost = async () => {
    try {
      setIsSuccess(false);
      setIsDeleteLoading(true);
      const deleteRes = await api.delete(
        `${API_PATHS.DELETE_POST}${selectedPost?.id}/`
      );
      if (deleteRes?.data?.status) {
        addToast('success', deleteRes?.data?.message);
        setIsDeleteOpen(false);
        setIsSuccess(true);
      } else {
        addToast('error', deleteRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setPost(null);
      setIsDeleteLoading(false);
      if (isDeleteOpen) {
        setIsDeleteOpen(false);
      }
    }
  };

  const toggleCategory = (el: number) => {
    setActiveCategory((prev) => {
      const exists = prev.includes(el);
      let updated = exists
        ? prev.filter((item) => item !== el) // remove
        : [...prev, el]; // add

      if (updated.length === categoryList?.length) {
        updated = [];
      }
      setCurrentPage(1);
      return updated;
    });
  };

  const handleCloseReportModal = () => {
    setIsReportOpen(null);
  };

  // Memoized post list rendering
  const renderPostList = useCallback(() => {
    if (isPostLoading && !postList?.length) {
      return (
        <div className="flex py-6 w-full justify-center items-center">
          <Loader fill="#fff" height={36} width={36} />
        </div>
      );
    }

    if (!isPostLoading && !postList?.length && currentPage === 1) {
      return (
        <div className="flex py-6 text-secondary flex-1 h-full w-full justify-center items-center">
          No Post Available
        </div>
      );
    }

    return postList.map((post, index) => (
      <div
        key={post?.id}
        className="flex flex-col border border-gray-200 rounded-[8px] p-4 gap-y-2 shadow-primary"
      >
        <div className="flex flex-1 justify-between items-start">
          <div className="flex items-center gap-x-3">
            <div className="h-12 w-12 flex rounded-full">
              {post?.author?.profile_picture ? (
                <img
                  src={post?.author?.profile_picture}
                  className="h-full w-full rounded-full object-cover"
                  alt={`${post?.author?.first_name} ${post?.author?.last_name}`}
                />
              ) : (
                <div className="h-full w-full rounded-full object-cover flex justify-center items-center bg-[#EDD6C2] text-primary-100 font-medium">
                  {post?.author?.first_name?.charAt(0)}
                  {post?.author?.last_name?.charAt(0)}
                </div>
              )}
            </div>
            <div className="flex flex-col ">
              <div className="font-bold">
                {trim(
                  `${post?.author?.first_name} ${post?.author?.last_name}`,
                  75
                )}
              </div>
              <div className="text-[#666666] flex items-center text-xs gap-x-1">
                <ClockIcon height={12} width={12} />
                {formatPostDate(post?.updated_at)}
              </div>
            </div>
          </div>
          {String(post?.author?.id) === String(userData?.id) &&
            selectedPost?.id !== post?.id && (
              <div className="flex justify-start items-center pt-1 relative">
                {/* Trigger */}
                <div
                  onClick={(e) => {
                    e.stopPropagation();
                    setIsPopoverOpen((prev) =>
                      prev === post?.id ? null : post?.id
                    );
                  }}
                  className="cursor-pointer"
                >
                  <MenuIcon />
                </div>

                {/* Popover */}
                {isPopoverOpen === post?.id && (
                  <div
                    className="absolute top-4 right-2 z-10"
                    ref={popoverRef}
                  >
                    <div className="flex rounded-lg flex-col shadow-primary bg-white min-w-[120px]">
                      {post?.comment_count <= 0 && (
                        <div
                          className="text-secondary hover:bg-primary-light py-2 px-3 flex gap-x-2 justify-start items-center cursor-pointer"
                          onClick={() => {
                            setPost(post);
                            setIsPopoverOpen(null);
                            if (search) setSearch('');
                            setIsAddOpen(true);
                          }}
                        >
                          <EditIcon height={14} width={14} /> Edit
                        </div>
                      )}
                      <div
                        className="hover:bg-primary-light py-2 px-3 flex justify-start items-center gap-x-2 text-red-400 cursor-pointer"
                        onClick={() => {
                          setPost(post);
                          setIsPopoverOpen(null);
                          setIsDeleteOpen(true);
                        }}
                      >
                        <DeleteIcon height={14} width={14} /> Delete
                      </div>
                    </div>
                  </div>
                )}
              </div>
            )}
        </div>

        <ExpandableText maxLength={450} text={post?.content} />
        <div className="py-2 flex flex-wrap gap-2">
          {post?.category_ids?.map((category) => (
            <div
              key={category?.id}
              className="bg-primary-light text-primary-100 font-semibold px-2 py-1 rounded-md text-sm"
            >
              {category?.name}
            </div>
          ))}
        </div>

        <div
          className={clsx(
            'flex flex-1 items-center pt-1',
            selectedPost?.id !== post?.id
              ? 'justify-between'
              : 'justify-end'
          )}
        >
          {selectedPost?.id !== post?.id && (
            <div
              className="bg-[#E9F1FF] flex p-1.5 gap-x-1 hover:scale-105 rounded-md text-sm items-center cursor-pointer"
              onClick={() => setPost(post)}
            >
              <CommentIcon height={16} width={16} />
              <span>{post?.comment_count ?? 0} Comments</span>
            </div>
          )}

          {String(post?.author?.id) !== String(userData?.id) && (
            <div
              className=" flex p-1.5 gap-x-1 hover:scale-105 rounded-md text-sm items-center cursor-pointer"
              onClick={() =>
                setIsReportOpen({ id: post?.id, type: 'post' })
              }
            >
              <ReportIcon height={16} width={16} />
              <span>Report</span>
            </div>
          )}
        </div>
      </div>
    ));
  }, [postList, isPostLoading, selectedPost, isPopoverOpen, userData]);

  return (
    <div className="flex flex-1 w-full h-full flex-col overflow-hidden max-h-screen">
      <Modal
        header="Report an issue"
        isOpen={!!isReportOpen}
        onClose={() => setIsReportOpen(null)}
        size="xl"
        hideCloseButton={isSubmitLoading}
        outsideClickClose={false}
      >
        <ReportModal
          setIsSubmitLoading={setIsSubmitLoading}
          isSubmitLoading={isSubmitLoading}
          details={isReportOpen}
          onClose={handleCloseReportModal}
        />
      </Modal>
      <Modal
        isOpen={isDeleteOpen}
        onClose={() => {
          setIsDeleteOpen(false);
        }}
        hideCloseButton
        outsideClickClose={false}
        children={
          <div className="flex flex-col items-center gap-[36px] pb-4 px-4">
            <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
              <CloseIcon width={18} height={18} fill="#FF3B30" />
            </div>
            <div className="text-black font-bold text-center w-full">
              Are you sure want to delete this post?
            </div>
            <div className=" w-full flex justify-center gap-6">
              <Button
                onClick={handleDeletePost}
                text="Yes"
                variant="other"
                className="bg-white shadow-primary border text-red-500 border-red-500"
                loading={isDeleteLoading}
                fillLoader="#fb2c36"
              />
              <Button
                text="No"
                variant="outline"
                disabled={isDeleteLoading}
                onClick={() => {
                  setIsDeleteOpen(false);
                  setPost(null);
                }}
              />
            </div>
          </div>
        }
      />
      <Modal
        size="xxl"
        isOpen={isAddOpen}
        header={selectedPost ? 'Update Question' : 'Add New Question'}
        onClose={closeAddmodal}
      >
        <AddQuestionModal
          post={selectedPost}
          categories={categoryList}
          onClose={closeAddmodal}
          setIsSuccess={setIsSuccess}
        />
      </Modal>
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Tradie's Hub</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="flex items-center gap-x-2">
          <Button
            text={
              <div className="flex items-center gap-x-2 justify-center">
                <CirclePlusIcon height={20} width={20} />
                <span>Add Question</span>
              </div>
            }
            variant="outline"
            width="w-[164px]"
            className="text-primary-100"
            onClick={openAddmodal}
            type="button"
          />
          <div className="w-[332px]">
            <InputField
              containerClassName="pt-0"
              placeholder="Search..."
              rightIcon={<SearchIcon height={25} width={25} />}
              field={null}
              onChange={searchHandler}
              value={search}
            />
          </div>
        </div>
      </div>
      <div className="px-10 flex flex-row overflow-hidden min-h-[calc(100vh-200px)]">
        {!selectedPost && (
          <div className="flex w-[240px] pt-2">
            <div className=" w-full rounded-[6px] flex flex-col">
              <div className="shadow-xs rounded-[6px]">
                <div
                  className={clsx(
                    'py-2.5 px-3 cursor-pointer font-medium rounded-t-[6px]',
                    activeCategory.length === categoryList?.length ||
                      !activeCategory?.length
                      ? 'bg-primary-light text-primary-100 '
                      : 'hover:bg-primary-light hover:text-primary-0'
                  )}
                  onClick={() => {
                    setActiveCategory([]);
                  }}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex flex-1">All Categories</div>
                    {(activeCategory.length === categoryList?.length ||
                      !activeCategory?.length) && (
                        <CircleTickIcon fill="#05df72" height={15} width={15} />
                      )}
                  </div>
                </div>
                {categoryList?.map((el, i) => (
                  <div
                    key={el.id}
                    className={clsx(
                      'py-2.5 px-3 cursor-pointer font-medium relative group',
                      i == categoryList?.length - 1 && 'rounded-b-[6px]',
                      activeCategory.includes(el.id) &&
                        activeCategory?.length !== categoryList?.length
                        ? 'bg-primary-light text-primary-100 '
                        : 'hover:bg-primary-light hover:text-primary-0'
                    )}
                    onClick={() => {
                      toggleCategory(el?.id);
                    }}
                  >
                    {el?.description && (
                      <div className="hidden group-hover:flex absolute -top-6 left-56 w-full h-full z-50">
                        <div className="flex p-2 items-center text-secondary shadow-primary rounded-[8px] text-xs min-h-max bg-white font-medium">
                          <ExpandableText
                            text={el?.description}
                            maxLength={200}
                          />
                        </div>
                      </div>
                    )}
                    <div className="flex items-center justify-between">
                      <div className="flex flex-1 text-[15px]">
                        {trim(el?.name, 30)}
                      </div>
                      {activeCategory.includes(el.id) &&
                        activeCategory?.length !== categoryList?.length && (
                          <CircleTickIcon
                            fill="#05df72"
                            height={15}
                            width={15}
                          />
                        )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
        <AnimatePresence>
          <motion.div
            initial={{ x: 100, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            exit={{ x: 100, opacity: 0 }}
            transition={{ duration: 0.3, ease: 'easeInOut' }}
            className={clsx(
              'flex flex-1 flex-col gap-y-2',
              !selectedPost && 'pl-6'
            )}
          >
            <div className="border border-l-0 border-r-0 border-t-0 border-b-[#F1F1F1] flex items-center justify-between">
              <div className=" flex gap-x-4">
                {tabDataList.map((tab) => (
                  <button
                    key={tab.id}
                    className={clsx(
                      'cursor-pointer text-[16px] font-bold flex items-center justify-center border-2 border-l-0 border-r-0 border-t-0 transition-colors',
                      activeTab === tab.id
                        ? 'border-b-primary-100 text-primary-100'
                        : 'border-b-transparent text-secondary'
                    )}
                    onClick={() => {
                      if (activeTab !== tab.id) {
                        if (search) setSearch('');
                        setActiveTab(tab.id);
                        setCurrentPage(1);
                      }
                    }}
                  >
                    {tab.name}
                  </button>
                ))}
              </div>
            </div>
            <motion.div
              className="flex flex-1 flex-col gap-y-4 overflow-auto h-full max-h-[78vh] custom-scrollbar pt-1 px-2"
              ref={scrollContainerRef}
              style={{
                willChange: 'transform',
                overscrollBehavior: 'contain'
              }}
            >
              {renderPostList()}

              {/* Infinite Scroll Trigger - only visible when needed */}
              {currentPage < totalPages && postList.length > 0 && (
                <div
                  ref={observerRef}
                  className="text-center p-4 h-20"
                >
                  {isPostLoading && (
                    <Loader height={36} width={36} fill="#fff" />
                  )}
                </div>
              )}
            </motion.div>
          </motion.div>
        </AnimatePresence>
        <AnimatePresence>
          {selectedPost && !isAddOpen && !isDeleteOpen && (
            <motion.div
              className="flex flex-1 max-w-[450px] max-h-[82vh] min-h-[82vh] pl-2 pt-10"
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: 100, opacity: 0 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
            >
              <CommentSection
                title={selectedPost?.title}
                postId={selectedPost?.id}
                onClose={handleCommentModalClose}
                setIsReportOpen={setIsReportOpen}
                commentCount={selectedPost?.comment_count}
              />
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default BuilderHub;