import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Modal } from '@Components/UI';
import clsx from 'clsx';
import { useEffect, useRef, useState } from 'react';
import {
  CirclePlusIcon,
  ClockIcon,
  CloseIcon,
  CommentIcon,
  DeleteIcon,
  EditIcon,
  Loader,
  MenuIcon,
  ReportIcon,
} from '@Icons';
import { AnimatePresence, motion } from 'framer-motion';
import { ExpandableText } from '@Components/Common';
import AddQuestionModal from './AddQuestionModal';
import Api from '@Helpers/Api';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { API_PATHS } from '@Helpers/Constants';
import CommentSection from './CommentSection';
import { useSelector } from 'react-redux';
import { trim } from '@Helpers/Utils';
import ReportModal from './ReportModal';
import { formatPostDate } from '.';
import { PATHS } from '@Config/Path.Config';
import { useNavigate, useParams } from 'react-router';

const breadcrumbItems = [
  { label: 'Home', link: '/' },
  { label: `<PERSON><PERSON><PERSON>'s Hub`, link: PATHS.TRADIE_HUB },
  { label: `Post` },
];

interface Author {
  id: number;
  email: string;
  profile_picture: string;
  first_name: string;
  last_name: string;
}

export interface Category {
  id: number;
  name: string;
  description?: string;
}

export interface Post {
  id: number;
  title: string;
  content: string;
  author: Author;
  category_ids: Category[];
  created_at: string;
  updated_at: string;
  comment_count: number;
}

interface ReportType {
  id: number;
  type: 'commentreplay' | 'comment' | 'post';
}

const SinglePost = (): JSX.Element => {
  const api = new Api();
  const navigate = useNavigate();
  const { addToast } = useToast();
  const { id } = useParams();
  const popoverRef = useRef<HTMLDivElement | null>(null);
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);

  const [selectedPost, setPost] = useState<Post | null>(null);
  const [isAddOpen, setIsAddOpen] = useState<boolean>(false);
  const [isAddNew, setIsAddNew] = useState<boolean>(false);
  const [isPostLoading, setIsPostLoading] = useState<boolean>(true);
  const [categoryList, setCategotyList] = useState<Category[]>([]);
  const [isPopoverOpen, setIsPopoverOpen] = useState<number | null>(null);
  const [isSuccess, setIsSuccess] = useState<boolean>(false);
  const [isDeleteOpen, setIsDeleteOpen] = useState<boolean>(false);
  const [isDeleteLoading, setIsDeleteLoading] = useState<boolean>(false);
  const [isReportOpen, setIsReportOpen] = useState<null | ReportType>(null);
  const [isSubmitLoading, setIsSubmitLoading] = useState<boolean>(false);
  const [postComments, setPostComments] = useState<number>(0);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const clickedInside = popoverRef.current?.contains(event.target as Node);

      if (!clickedInside) {
        setIsPopoverOpen(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    (async () => {
      try {
        const catRes = await api.get(API_PATHS.GET_BH_CATEGORY_LIST);
        if (catRes?.data?.status) {
          setCategotyList([...catRes.data.data] as Category[]);
        } else {
          addToast('error', catRes?.data?.message);
        }
      } catch (err) {
        addToast('error', err as string);
      }
    })();
  }, []);

  useEffect(() => {
    fetchPostDetails();
  }, [id]);

  useEffect(() => {
    if (isSuccess) {
      setIsSuccess(false);
      fetchPostDetails();
    }
  }, [isSuccess]);

  const fetchPostDetails = async () => {
    try {
      setIsPostLoading(true);
      const postRes = await api.get(`${API_PATHS.GET_POST_DETAILS}${id}`);

      if (postRes?.data?.status) {
        setPost(postRes?.data?.data);
        setPostComments(postRes?.data?.data?.comment_count);
      } else {
        addToast('error', postRes?.data?.message);
      }
    } catch (err) {
      // Don't show error toast if request was aborted
      addToast('error', err as string);
    } finally {
      setIsPostLoading(false);
    }
  };

  const openAddmodal = () => {
    setIsAddNew(true);
    setIsAddOpen(true);
  };

  const closeAddmodal = () => {
    setIsAddNew(false);
    setIsAddOpen(false);
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Deletes a post.
   *
   * @remarks
   * 1. Sends a DELETE request to the API to delete the post.
   * 2. If the request is successful, shows a success toast with the response message.
   * 3. If the request fails, shows an error toast with the error message.
   * 4. Closes the delete confirmation modal.
   */
  /*******  a6554887-5708-4c21-bd03-3695ac2b746f  *******/
  const handleDeletePost = async () => {
    try {
      setIsDeleteLoading(true);
      const deleteRes = await api.delete(
        `${API_PATHS.DELETE_POST}${selectedPost?.id}/`
      );
      if (deleteRes?.data?.status) {
        addToast('success', deleteRes?.data?.message);
        setIsDeleteOpen(false);
        navigate(PATHS.TRADIE_HUB);
      } else {
        addToast('error', deleteRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsDeleteLoading(false);
      if (isDeleteOpen) {
        setIsDeleteOpen(false);
      }
    }
  };

  const handleCloseReportModal = () => {
    setIsReportOpen(null);
  };

  return (
    <div className="flex flex-1 w-full h-full flex-col !overflow-hidden max-h-screen">
      <Modal
        header="Report an issue"
        isOpen={!!isReportOpen}
        onClose={() => setIsReportOpen(null)}
        size="xl"
        hideCloseButton={isSubmitLoading}
        outsideClickClose={false}
      >
        <ReportModal
          setIsSubmitLoading={setIsSubmitLoading}
          isSubmitLoading={isSubmitLoading}
          details={isReportOpen}
          onClose={handleCloseReportModal}
        />
      </Modal>
      <Modal
        isOpen={isDeleteOpen}
        onClose={() => {
          setIsDeleteOpen(false);
        }}
        hideCloseButton
        outsideClickClose={false}
        children={
          <div className="flex flex-col items-center gap-[36px] pb-4 px-4">
            <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
              <CloseIcon width={18} height={18} fill="#FF3B30" />
            </div>
            <div className="text-black font-bold text-center w-full">
              Are you sure want to delete this post?
            </div>
            <div className=" w-full flex justify-center gap-6">
              <Button
                onClick={handleDeletePost}
                text="Yes"
                variant="other"
                className="bg-white shadow-primary border text-red-500 border-red-500"
                loading={isDeleteLoading}
                fillLoader="#fb2c36"
              />
              <Button
                text="No"
                variant="outline"
                disabled={isDeleteLoading}
                onClick={() => {
                  setIsDeleteOpen(false);
                }}
              />
            </div>
          </div>
        }
      />
      <Modal
        size="xxl"
        isOpen={isAddOpen}
        header={selectedPost ? 'Update Question' : 'Add New Question'}
        onClose={closeAddmodal}
      >
        <AddQuestionModal
          post={isAddNew ? null : selectedPost}
          categories={categoryList}
          onClose={closeAddmodal}
          setIsSuccess={setIsSuccess}
          isSinglePost
        />
      </Modal>
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Tradie's Hub</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="flex items-center gap-x-2">
          <Button
            text={
              <div className="flex items-center gap-x-2 justify-center">
                <CirclePlusIcon height={20} width={20} />
                <span>Add Question</span>
              </div>
            }
            variant="outline"
            width="w-[164px]"
            className="text-primary-100"
            onClick={openAddmodal}
            type="button"
          />
        </div>
      </div>
      {isPostLoading && (
        <div className="flex py-6  w-full justify-center items-center">
          <Loader fill="#fff" height={36} width={36} />
        </div>
      )}
      {!isPostLoading && !selectedPost && (
        <div className="flex py-6 text-secondary flex-1 h-full w-full justify-center items-center">
          Post Not Available
        </div>
      )}
      {!isPostLoading && selectedPost && (
        <div className="px-10 flex flex-col gap-y-4 overflow-hidden h-[100vh] max-h-[100vh]">
          <AnimatePresence>
            <motion.div
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: 100, opacity: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className={clsx(
                'flex  flex-col gap-y-2',
                !selectedPost && 'pl-6'
              )}
            >
              <motion.div className="flex flex-col gap-y-4 h-full  max-h-[50vh] custom-scrollbar pt-1 px-2">
                <div
                  key={selectedPost?.id}
                  // ref={index === postList.length - 1 ? lastPostRef : null}
                  className="flex flex-col border border-gray-200 rounded-[8px] p-4 gap-y-2 shadow-primary"
                >
                  <div className="flex flex-1 justify-between items-start">
                    <div className="flex items-center gap-x-3">
                      <div className="h-12 w-12 flex rounded-full">
                        {selectedPost?.author?.profile_picture ? (
                          <img
                            src={selectedPost?.author?.profile_picture}
                            className="h-full w-full rounded-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full rounded-full object-cover flex justify-center items-center bg-[#EDD6C2] text-primary-100 font-medium">
                            {selectedPost?.author?.first_name?.charAt(0)}
                            {selectedPost?.author?.last_name?.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="flex flex-col ">
                        <div className="font-bold">
                          {trim(
                            `${selectedPost?.author?.first_name} ${selectedPost?.author?.last_name}`,
                            75
                          )}
                        </div>
                        <div className="text-[#666666] flex items-center text-xs gap-x-1">
                          <ClockIcon height={12} width={12} />
                          {formatPostDate(selectedPost?.updated_at as string)}
                        </div>
                      </div>
                    </div>
                    {String(selectedPost?.author?.id) ===
                      String(userData?.id) && (
                      <div className="flex justify-start items-center pt-1 relative">
                        {/* Trigger */}
                        <div
                          onClick={(e) => {
                            e.stopPropagation();
                            setIsPopoverOpen((prev) =>
                              prev === selectedPost?.id
                                ? null
                                : selectedPost?.id
                            );
                          }}
                          className="cursor-pointer"
                        >
                          <MenuIcon />
                        </div>

                        {/* Popover */}
                        {isPopoverOpen === selectedPost?.id && (
                          <div
                            className="absolute top-4 right-2 z-10"
                            ref={popoverRef} // <-- ref applied to popover box
                          >
                            <div className="flex rounded-lg flex-col shadow-primary bg-white min-w-[120px]">
                              {selectedPost?.comment_count <= 0 && (
                                <div
                                  className="text-secondary hover:bg-primary-light py-2 px-3 flex gap-x-2 justify-start items-center cursor-pointer"
                                  onClick={() => {
                                    setIsPopoverOpen(null);
                                    setIsAddOpen(true);
                                  }}
                                >
                                  <EditIcon height={14} width={14} /> Edit
                                </div>
                              )}
                              <div
                                className="hover:bg-primary-light py-2 px-3 flex justify-start items-center gap-x-2 text-red-400 cursor-pointer"
                                onClick={() => {
                                  setIsPopoverOpen(null);
                                  setIsDeleteOpen(true);
                                }}
                              >
                                <DeleteIcon height={14} width={14} /> Delete
                              </div>
                            </div>
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  {/* <div className="pt-2 font-semibold">{post?.title}</div> */}
                  <ExpandableText
                    maxLength={700}
                    text={selectedPost?.content as string}
                  />
                  <div className="py-2 flex flex-wrap gap-2">
                    {selectedPost?.category_ids?.map((category) => (
                      <div
                        key={category?.id}
                        className="bg-primary-light text-primary-100 font-semibold px-2 py-1 rounded-md text-sm"
                      >
                        {category?.name}
                      </div>
                    ))}
                  </div>

                  <div
                    className={clsx(
                      'flex flex-1 items-center pt-1 justify-between'
                    )}
                  >
                    <div className="bg-[#E9F1FF] flex p-1.5 gap-x-1 hover:scale-105 rounded-md text-sm items-center ">
                      <CommentIcon height={16} width={16} />
                      <span>
                        {(postComments || selectedPost?.comment_count) ?? 0}{' '}
                        Comments
                      </span>
                    </div>

                    {String(selectedPost?.author?.id) !==
                      String(userData?.id) && (
                      <div
                        className=" flex p-1.5 gap-x-1 hover:scale-105 rounded-md text-sm items-center cursor-pointer"
                        onClick={() =>
                          setIsReportOpen({
                            id: selectedPost?.id as number,
                            type: 'post',
                          })
                        }
                      >
                        <ReportIcon height={16} width={16} />
                        <span>Report</span>
                      </div>
                    )}
                  </div>
                </div>
              </motion.div>
            </motion.div>
          </AnimatePresence>
          <AnimatePresence>
            <motion.div
              className="flex  max-w-full  min-h-[100px] px-2 pb-4"
              initial={{ x: 100, opacity: 0 }}
              animate={{ x: 0, opacity: 1 }}
              exit={{ x: 100, opacity: 0 }}
              transition={{ duration: 0.2, ease: 'easeInOut' }}
            >
              <CommentSection
                title={selectedPost?.title}
                postId={selectedPost?.id}
                setIsReportOpen={setIsReportOpen}
                commentCount={selectedPost?.comment_count || 0}
                isSinglePost
                setPostComments={setPostComments}
              />
            </motion.div>
          </AnimatePresence>
        </div>
      )}
    </div>
  );
};

export default SinglePost;
