import { Button } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { ReportIcon } from '@Icons';
import { useEffect, useState } from 'react';

interface Reasons {
  id: number;
  name: string;
}

interface ReportType {
  id: number;
  type: 'commentreplay' | 'comment' | 'post';
}

const DEFAULT_REASONS: Reasons[] = [
  {
    id: 1,
    name: 'Spam',
  },
  {
    id: 2,
    name: 'Hate Speech',
  },
  {
    id: 3,
    name: 'Harassment and bullying',
  },
  {
    id: 4,
    name: 'Harmful activities',
  },
  {
    id: 5,
    name: 'Adult content (Consensual)',
  },
  {
    id: 6,
    name: 'Sexual exploittion and abuse (child safety)',
  },
  {
    id: 7,
    name: 'Sexual exploitation and abuse (adults and animals)',
  },
  {
    id: 8,
    name: 'Plagiarism',
  },
  {
    id: 9,
    name: 'Poorly written',
  },
  {
    id: 10,
    name: 'Inappropriate credential',
  },
  {
    id: 11,
    name: 'Other',
  },
];

const reportTypes = {
  comment: 'comment',
  commentreplay: 'reply',
  post: 'post',
};

const ReportModal = ({
  details,
  isSubmitLoading,
  setIsSubmitLoading,
  onClose,
}: {
  details: ReportType | null;
  isSubmitLoading: boolean;
  setIsSubmitLoading: React.Dispatch<React.SetStateAction<boolean>>;
  onClose: () => void;
}): JSX.Element => {
  const api = new Api();
  const { addToast } = useToast();
  const [selectedeReason, setSelectedReason] = useState<Reasons | null>(null);
  const [reasons, setReasons] = useState<Reasons[]>(DEFAULT_REASONS);
  const [error, setError] = useState<string>('');

  useEffect(() => {
    (async () => {
      try {
        const res = await api.get(API_PATHS.GET_REPORT_REASON_LIST);
        if (res?.data?.status) {
          setReasons([...res.data.data] as Reasons[]);
        } else {
          addToast('error', res?.data?.message);
        }
      } catch (err) {
        console.log(err);
        // addToast('error', err as string);
      }
    })();
  }, []);

  const handleReportIssue = async () => {
    try {
      if (!selectedeReason) {
        setError('Select a reason to report');
        return;
      }
      setIsSubmitLoading(true);
      console.log('details', details);
      console.log('selectedeReason', selectedeReason);
      const res = await api.post(API_PATHS.CREATE_REPORT, {
        data: {
          type: details?.type,
          object_id: details?.id,
          reason_id: selectedeReason?.id,
        },
      });
      if (res?.data?.status) {
        addToast('success', res?.data?.message);
        onClose();
      } else {
        addToast('error', res?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsSubmitLoading(false);
    }
  };

  return (
    <div className="flex flex-col px-6">
      <div className="flex flex-col items-center">
        <div className="bg-red-100 h-12 w-12 flex justify-center items-center rounded-full">
          <ReportIcon width={18} height={18} />
        </div>
        <div className="text-black font-medium text-center w-full py-4 text-xl">
          Why are you reporting this {details ? reportTypes[details?.type] : ''}
          ?
        </div>
      </div>
      <div className="flex flex-col  gap-y-3 pt-2 justify-items-start">
        {reasons?.length > 0 &&
          reasons.map((el) => (
            <div
              className="flex items-center gap-x-2 cursor-pointer"
              key={el?.id}
              onClick={() => {
                setSelectedReason(el);
                setError('');
              }}
            >
              <input
                type="radio"
                name="report"
                value={el?.id}
                className="w-4 h-4 cursor-pointer"
                checked={selectedeReason?.id === el?.id}
              />
              <div className="text-gray-700 font-normal text-md">
                {el?.name}
              </div>
            </div>
          ))}
        {error && <p className="text-red-500 text-sm break-words">{error}</p>}
        <div className="flex justify-end w-full py-4">
          <Button
            onClick={handleReportIssue}
            disabled={isSubmitLoading}
            loading={isSubmitLoading}
            text="Submit"
          />{' '}
        </div>
      </div>
    </div>
  );
};

export default ReportModal;
