import { ExpandableText } from '@Components/Common';
import { <PERSON>ton, InputField } from '@Components/UI';
import {
  ClockIcon,
  CloseIcon,
  DeleteIcon,
  EditIcon,
  Loader,
  MenuIcon,
  ReportIcon,
} from '@Icons';
import { Dispatch, SetStateAction, useEffect, useRef, useState } from 'react';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { API_PATHS } from '@Helpers/Constants';
import Api from '@Helpers/Api';
import { formatPostDate } from '.';
import { useSelector } from 'react-redux';
import { trim } from '@Helpers/Utils';
import clsx from 'clsx';

interface ReportType {
  id: number;
  type: 'commentreplay' | 'comment' | 'post';
}

interface Author {
  id: number;
  email: string;
  profile_picture: string | null;
  first_name: string;
  last_name: string;
}

interface Reply {
  id: number;
  content: string;
  author: Author;
  created_at: string;
  updated_at: string;
}

interface Comment {
  id: number;
  content: string;
  post_id: number;
  author: Author;
  created_at: string;
  updated_at: string;
  replies: Reply[];
  replay_count: number;
  nextReply?: number;
}

const CommentSection = ({
  onClose,
  postId = null,
  title = '',
  setIsReportOpen,
  commentCount = 0,
  isSinglePost = false,
  setPostComments,
}: {
  title?: string;
  postId?: number | null;
  onClose?: (val: boolean) => void;
  setIsReportOpen: React.Dispatch<React.SetStateAction<null | ReportType>>;
  commentCount: number;
  isSinglePost?: boolean;
  setPostComments?: Dispatch<SetStateAction<number>>;
}): JSX.Element => {
  const api = new Api();
  const { addToast } = useToast();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state?.UserControle?.user);
  const observerRef = useRef<HTMLDivElement | null>(null);
  const popoverRef = useRef<HTMLDivElement | null>(null);
  const firstCommentRef = useRef<HTMLDivElement | null>(null);

  const [isAddReply, setIsAddReply] = useState<null | number>(null);
  const [commentsList, setCommentsList] = useState<Comment[]>([]);
  const [isMoreCommentLoading, setIsMoreCommentLoading] = useState<
    number | null
  >(null);
  const [isEditingReply, setIsEditingReply] = useState<number | null>(null);
  const [isEditingComment, setIsEditingComment] = useState<number | null>(null);
  const [isCommentsLoading, setIsCommentsLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [comment, setComment] = useState<string>('');
  const [reply, setReply] = useState<string>('');
  const [isCommenting, setIsCommenting] = useState<boolean>(false);
  const [isReplying, setIsReplying] = useState<number | null>(null);
  const [editComment, setEditComment] = useState<string>('');
  const [editReply, setEditReply] = useState<string>('');
  const [isEditLoading, setIsEditLoading] = useState<boolean>(false);
  const [isPopoverOpen, setIsPopoverOpen] = useState<number | null>(null);
  const [totalComments, setTotalComments] = useState<number>(0);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const clickedInside = popoverRef.current?.contains(event.target as Node);

      if (!clickedInside) {
        setIsPopoverOpen(null);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  useEffect(() => {
    if (isAddReply) setIsAddReply(null);
    if (isEditingComment) setIsEditingComment(null);
    if (isEditingReply) setIsEditingReply(null);
    setCommentsList([]);
    setCurrentPage(1);
    fetchCommentList(true);
  }, [postId]);

  useEffect(() => {
    fetchCommentList();
  }, [currentPage]);

  // Infinite Scroll Observer
  useEffect(() => {
    if (isCommentsLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setCurrentPage((p) => Math.min(p + 1, totalPages));
        }
      },
      { threshold: 1.0 }
    );

    if (observerRef.current) observer.observe(observerRef.current);

    return () => observer.disconnect();
  }, [isCommentsLoading]);

  useEffect(() => {
    if (firstCommentRef.current && !isCommentsLoading && currentPage === 1) {
      firstCommentRef.current.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
      });
    }
  }, [commentsList.length, isCommentsLoading, currentPage]);

  useEffect(() => {
    if (!isEditingComment) return;
    setTimeout(() => {
      scrollToCommentBox();
    }, 200);
  }, [isEditingComment]);

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Fetches the list of comments for a specific post and updates the state.
   *
   * @param {boolean} [reset=false] - Indicates whether to reset the pagination and start fetching from the first page.
   *
   * This function performs an API request to retrieve comments associated with a given post ID.
   * It updates the comments list and pagination details in the state. If the API request is
   * successful, the comments are aggregated and the total number of pages is updated. If the
   * request fails, an error message is displayed via a toast notification.
   */

  /*******  871fcc4f-419a-4fa0-a663-73dccf286736  *******/
  const fetchCommentList = async (reset: boolean = false) => {
    try {
      setIsCommentsLoading(true);
      const postRes = await api.get(API_PATHS.GET_COMMENT_LIST, {
        params: {
          page: reset ? 1 : currentPage,
          page_size: 10,
          post_id: postId,
        },
      });
      if (postRes?.data?.status) {
        let tempPosts: Comment[] = [];
        if (commentsList?.length && !reset) {
          tempPosts = [...commentsList];
        }
        tempPosts = [...tempPosts, ...postRes.data.data.list];
        setCommentsList([...tempPosts] as Comment[]);
        setTotalPages(postRes?.data?.data?.total_pages);
        setTotalComments(postRes?.data?.data?.total_record);
        if (setPostComments) setPostComments(postRes?.data?.data?.total_record);
      } else {
        addToast('error', postRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsCommentsLoading(false);
    }
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Loads more replies for a given comment, given its ID and the next page number. It makes
   * a GET request to the API to retrieve the replies, and if the request is successful,
   * it updates the comment with the new replies by concatenating the new replies with the
   * existing replies, and updates the nextReply and replay_count fields of the comment.
   * If the request fails, it logs an error to the console. Finally, it sets
   * isMoreCommentLoading to false.
   * @param {number} id - The ID of the comment for which to load more replies.
   * @param {number} [nextReply] - The next page number of replies to load. If not provided,
   * the default value is 2.
   */
  /*******  270aab3a-eb04-46af-8370-40e4e424c8fd  *******/
  const handleLoadMoreReplies = async (id: number, nextReply?: number) => {
    try {
      setIsMoreCommentLoading(id);
      const newReplies = await api.get(`${API_PATHS.GET_REPLIES}`, {
        params: {
          comment_id: id,
          page_size: 2,
          page: nextReply ?? 2,
        },
      });
      if (newReplies?.data?.status) {
        const tempComments = [...commentsList];
        const findIndex = tempComments.findIndex((cm) => cm?.id == id);
        // tempComments[findIndex].replies = newReplies.data.data.list;
        tempComments[findIndex].replies = [
          ...tempComments[findIndex].replies,
          ...newReplies.data.data.list,
        ];
        tempComments[findIndex].nextReply = newReplies.data.data.next;
        tempComments[findIndex].replay_count =
          newReplies.data.data.total_record;
        setCommentsList([...tempComments]);
      }
    } catch (err) {
      console.log('err', err);
    } finally {
      setIsMoreCommentLoading(null);
    }
  };

  const onCommentChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setComment(e.target.value);
  };

  const onReplyChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    setReply(e.target.value);
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Posts a new comment to the given post.
   *
   * @remarks
   * 1. Sends a POST request to the API to post the comment.
   * 2. If the request is successful, resets the comments list and fetches the comment list again with the first page.
   * 3. If the request fails, shows an error toast with the error message.
   * 4. Finally, resets the comment input and sets isCommenting to false.
   */
  /*******  0b9823e7-0da5-4985-8aba-5c2325c093d9  *******/
  const postComment = async () => {
    try {
      setIsCommenting(true);
      const postRes = await api.post(API_PATHS.POST_COMMENT, {
        data: {
          content: comment,
          post_id: postId,
        },
      });
      if (postRes?.data?.status) {
        setCurrentPage(1);
        setCommentsList([]);
        fetchCommentList(true);
      } else {
        addToast('error', postRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setComment('');
      setIsCommenting(false);
    }
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Posts a new reply to the given comment.
   *
   * @remarks
   * 1. Sends a POST request to the API to post the reply.
   * 2. If the request is successful, resets the reply input and sets isCommenting to false.
   * 3. If the request fails, shows an error toast with the error message.
   * @param {number} commentId - The ID of the comment to which the reply is being posted.
   */
  /*******  0caab55c-14a0-45e2-862b-f1e10c1d777d  *******/
  const postReply = async (commentId: number) => {
    try {
      setIsReplying(commentId);
      const replyRes = await api.post(API_PATHS.POST_REPLY, {
        data: {
          content: reply,
          comment_id: commentId,
        },
      });
      if (replyRes?.data?.status) {
        /*************  ✨ Windsurf Command ⭐  *************/
        const newReplies = await api.get(`${API_PATHS.GET_REPLIES}`, {
          params: {
            comment_id: commentId,
            page_size: 2,
            page: 1,
          },
        });
        if (newReplies?.data?.status) {
          const tempComments = [...commentsList];
          const findIndex = tempComments.findIndex((cm) => cm?.id == commentId);
          tempComments[findIndex].replies = newReplies.data.data.list;
          tempComments[findIndex].nextReply = newReplies.data.data.next;
          tempComments[findIndex].replay_count =
            newReplies.data.data.total_record;
          setCommentsList([...tempComments]);
        }
        /*******  d89e7cde-848e-4490-9548-a523b4017a99  *******/
      } else {
        addToast('error', replyRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setReply('');
      setIsReplying(null);
      setIsAddReply(null);
    }
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Updates a reply by sending a PATCH request to the API.
   *
   * @remarks
   * 1. Finds the comment to which the reply belongs and its index in the commentsList state.
   * 2. Finds the reply to be updated and its index in the comment's replies array.
   * 3. Updates the reply's content with the new value and sets the updated commentsList state.
   * 4. If the request fails, shows an error toast with the error message.
   * 5. Finally, resets the editReply state and sets isEditLoading to false.
   * @param {number} replyId - The ID of the reply to be updated.
   */
  /*******  99d38a36-dc86-46fc-9efc-aade74f7dfb2  *******/
  const updateReply = async (replyId: number) => {
    try {
      setIsEditLoading(true);
      setIsEditingReply(replyId);
      const updateReplyRes = await api.patch(
        `${API_PATHS.UPDATE_REPLY}${replyId}/`,
        {
          data: {
            content: editReply,
          },
        }
      );
      if (updateReplyRes?.data?.status) {
        const tempComments = [...commentsList];
        const commentIndex = tempComments.findIndex((cm) =>
          cm.replies.some((reply) => reply.id === replyId)
        );
        if (commentIndex !== -1) {
          const replyIndex = tempComments[commentIndex].replies.findIndex(
            (reply) => reply.id === replyId
          );
          if (replyIndex !== -1) {
            tempComments[commentIndex].replies[replyIndex].content = editReply;
            setCommentsList([...tempComments]);
          }
        }
      } else {
        addToast('error', updateReplyRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsEditingReply(null);
      setIsEditLoading(false);
    }
  };

  const updateComment = async (commentId: number) => {
    try {
      setIsEditLoading(true);
      setIsEditingComment(commentId);
      const updateCommentRes = await api.patch(
        `${API_PATHS.UPDATE_COMMENT}${commentId}/`,
        {
          data: {
            content: editComment,
          },
        }
      );
      if (updateCommentRes?.data?.status) {
        const tempComments = [...commentsList];
        const findIndex = tempComments.findIndex((cm) => cm?.id == commentId);
        tempComments[findIndex].content = editComment;
        setCommentsList([...tempComments]);
      } else {
        addToast('error', updateCommentRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsEditingComment(null);
      setIsEditLoading(false);
    }
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Deletes a comment.
   *
   * @remarks
   * 1. Sends a DELETE request to the API to delete the comment.
   * 2. If the request is successful, resets the comments list and fetches the comment list again with the first page.
   * 3. If the request fails, shows an error toast with the error message.
   */
  /*******  24a45256-85a8-448a-af36-1a0bae38c9e2  *******/
  const deleteComment = async (commentId: number) => {
    try {
      const deleteCommentRes = await api.delete(
        `${API_PATHS.DELETE_COMMENT}${commentId}/`
      );
      if (deleteCommentRes?.data?.status) {
        setCurrentPage(1);
        setCommentsList([]);
        fetchCommentList(true);
      } else {
        addToast('error', deleteCommentRes?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    }
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Deletes a reply given its ID.
   *
   * @remarks
   * 1. Sends a DELETE request to the API to remove the reply.
   * 2. If the request is successful, removes the reply from the local comments list and updates the state.
   * 3. If the reply or comment is not found, or the request fails, shows an error toast with the error message.
   *
   * @param {number} replyId - The ID of the reply to be deleted.
   */

  /*******  a69ca41e-8b74-4284-8455-4e1f102a69c0  *******/
  const deleteReply = async (replyId: number, commentId: number) => {
    try {
      const deleteReplyRes = await api.delete(
        `${API_PATHS.DELETE_REPLY}${replyId}/`
      );
      if (deleteReplyRes?.data?.status) {
        const newReplies = await api.get(`${API_PATHS.GET_REPLIES}`, {
          params: {
            comment_id: commentId,
            page_size: 2,
            page: 1,
          },
        });
        if (newReplies?.data?.status) {
          const tempComments = [...commentsList];
          const findIndex = tempComments.findIndex((cm) => cm?.id == commentId);
          tempComments[findIndex].replies = newReplies.data.data.list;
          tempComments[findIndex].nextReply = newReplies.data.data.next;
          tempComments[findIndex].replay_count =
            newReplies.data.data.total_record;
          setCommentsList([...tempComments]);
        }
      } else {
        addToast('error', deleteReplyRes?.data?.message);
      }
      /*******  7d04ffa5-284d-4022-84d6-ec2943902faf  *******/
    } catch (err) {
      addToast('error', err as string);
    }
  };

  /*************  ✨ Windsurf Command ⭐  *************/
  /**
   * Scrolls the comment box into view.
   *
   * @remarks
   * This function finds the comment element by its ID, which corresponds to the
   * currently editing comment, and scrolls it into view smoothly, centering it
   * in the viewport if possible.
   */

  /*******  926cba99-bf8f-4ff4-a896-83cf09d517b7  *******/
  const scrollToCommentBox = () => {
    const element = document.getElementById(String(isEditingComment));
    if (element) {
      // Scroll the element into view with smooth behavior
      element.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
  };

  return (
    <div className="h-full p-3 flex flex-1 shadow-primary flex-col rounded-[6px] bg-white ">
      {!isSinglePost && (
        <div className="flex  items-center justify-between">
          <div className="font-semibold text-lg pb-2">
            Comments <span>{totalComments > 0 && `(${totalComments})`}</span>
          </div>
          <CloseIcon
            height={12}
            width={12}
            className="cursor-pointer"
            onClick={() => {
              if (onClose) {
                onClose(totalComments !== commentCount);
                setCommentsList([]);
              }
            }}
          />
        </div>
      )}
      <div className="pb-2 font-medium text-[16px]">
        {' '}
        <ExpandableText
          maxLength={90}
          className="text-[14px] pt-2"
          text={title}
        />
      </div>
      <InputField
        type="textarea"
        placeholder="Add new comment....."
        inputClassName="!max-h-32"
        isComment
        value={comment}
        onChange={onCommentChange}
        maxLength={2000}
      />
      <div className="pt-1.5 flex">
        <Button
          text={!isCommenting ? 'Post' : '---'}
          type="button"
          width="w-max-fit"
          className="!h-6.5 text-xs flex justify-center items-center text-center disabled:opacity-50"
          onClick={postComment}
          disabled={isCommenting || comment.trim() == ''}
          loaderHW={13}
        />
      </div>
      <div className="flex flex-col pt-4 overflow-auto max-h-[56vh] custom-scrollbar mt-2.5 gap-y-4 pr-1">
        {isCommentsLoading && !commentsList?.length && (
          <div className="flex py-6  w-full justify-center items-center">
            <Loader fill="#fff" height={36} width={36} />
          </div>
        )}
        {!isCommentsLoading && !commentsList?.length && currentPage === 1 && (
          <div className="flex py-6 text-secondary flex-1 h-full w-full justify-center items-center">
            No Comments Available
          </div>
        )}
        {commentsList.map((el, index) => (
          <div
            key={el?.id}
            ref={index == 0 ? firstCommentRef : null}
            className="flex flex-col border border-gray-200 rounded-[8px] pt-4 pb-2 px-4 gap-y-2"
          >
            <div>
              <div className="flex flex-1 justify-between items-center">
                <div className="flex items-center gap-x-3">
                  <div className="h-9 w-9 flex rounded-full">
                    {el?.author?.profile_picture ? (
                      <img
                        src={el?.author?.profile_picture}
                        className="h-full w-full rounded-full"
                      />
                    ) : (
                      <div className="h-full w-full rounded-full object-cover flex justify-center items-center text-sm bg-[#EDD6C2] text-primary-100 font-medium">
                        {el?.author?.first_name?.charAt(0)}
                        {el?.author?.last_name?.charAt(0)}
                      </div>
                    )}
                  </div>
                  <div className="flex flex-col ">
                    <div className="font-semibold text-[14px]">
                      {trim(
                        `${el?.author?.first_name} ${el?.author?.last_name}`,
                        40
                      )}
                    </div>
                    <div className="text-[#666666] flex items-center text-xs gap-x-1">
                      <ClockIcon height={10} width={10} />
                      {formatPostDate(el?.created_at)}
                    </div>
                  </div>
                </div>
                {String(el?.author?.id) === String(userData?.id) && (
                  <div className="flex justify-start items-center pt-1 relative">
                    {/* Trigger */}
                    <div
                      onClick={(e) => {
                        e.stopPropagation();
                        setIsPopoverOpen((prev) =>
                          prev === el?.id ? null : el?.id
                        );
                      }}
                      className="cursor-pointer"
                    >
                      <MenuIcon />
                    </div>

                    {/* Popover */}
                    {isPopoverOpen === el?.id && (
                      <div
                        className="absolute top-4 right-2 z-10"
                        ref={popoverRef} // <-- ref applied to popover box
                      >
                        <div className="flex rounded-lg flex-col shadow-primary bg-white min-w-[120px]">
                          {el?.replay_count <= 0 && (
                            <div
                              className="text-secondary hover:bg-primary-light py-2 px-3 flex gap-x-2 justify-start items-center cursor-pointer"
                              onClick={() => {
                                if (isAddReply) setIsAddReply(null);
                                setEditComment(el?.content);
                                setIsEditingComment(el?.id);
                                setIsPopoverOpen(null);
                              }}
                            >
                              <EditIcon height={14} width={14} /> Edit
                            </div>
                          )}
                          <div
                            className="hover:bg-primary-light py-2 px-3 flex justify-start items-center gap-x-2 text-red-400 cursor-pointer"
                            onClick={() => {
                              deleteComment(el?.id);
                              setIsPopoverOpen(null);
                            }}
                          >
                            <DeleteIcon height={14} width={14} /> Delete
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
              {isEditingComment !== el?.id ? (
                <>
                  <ExpandableText
                    maxLength={160}
                    className="text-[14px] pt-2"
                    text={el?.content}
                  />
                  {isAddReply !== el?.id && (
                    <div className="flex flex-1 justify-between items-center py-1">
                      <div
                        className="flex gap-x-1 hover:scale-105 rounded-md text-xs text-primary-100 items-center cursor-pointer"
                        onClick={() => setIsAddReply(el?.id)}
                      >
                        Add Reply
                      </div>
                      {String(userData?.id) !== String(el?.author?.id) && (
                        <div
                          className=" flex p-1.5 gap-x-1 hover:scale-105 rounded-md text-xs items-center cursor-pointer"
                          onClick={() => {
                            setIsReportOpen({ type: 'comment', id: el?.id });
                          }}
                        >
                          <ReportIcon height={14} width={14} />
                          <span>Report</span>
                        </div>
                      )}
                    </div>
                  )}
                </>
              ) : (
                <div className="pt-2" id={String(isEditingComment)}>
                  <InputField
                    type="textarea"
                    value={editComment}
                    onChange={(e) => setEditComment(e.target.value)}
                    inputClassName="!max-h-64 !h-10"
                    isComment
                    autoFocus
                    maxLength={2000}
                  />
                  <div className="flex flex-row w-full justify-end gap-x-2 pt-1.5">
                    <Button
                      text={!isEditLoading ? 'Update' : '-----'}
                      variant="outline"
                      width="w-min-fit"
                      className="!h-6 text-primary-100 text-xs flex justify-center items-center disabled:opacity-50"
                      type="button"
                      onClick={() => updateComment(el?.id)}
                      disabled={
                        isEditLoading ||
                        editComment.trim() === '' ||
                        editComment === el?.content
                      }
                      // loading={isEditLoading}
                    />
                    <Button
                      text="Cancel"
                      variant="outline"
                      width="w-min-fit"
                      className="!h-6 text-secondary text-xs flex justify-center items-center border border-secondary  hover:border-secondary"
                      onClick={() => setIsEditingComment(null)}
                      disabled={isEditLoading}
                    />
                  </div>
                </div>
              )}
              {isAddReply === el?.id && (
                <div className="pt-1">
                  <InputField
                    type="textarea"
                    placeholder="Add reply..."
                    inputClassName="!max-h-64 !h-12"
                    isComment
                    value={reply}
                    onChange={onReplyChange}
                    maxLength={2000}
                  />
                  <div className="flex flex-row w-full justify-end gap-x-2 pt-1.5">
                    <Button
                      text={!isReplying ? 'Reply' : '----'}
                      variant="outline"
                      width="w-min-fit"
                      className="!h-6 text-primary-100 text-xs flex justify-center items-center disabled:opacity-50"
                      onClick={() => postReply(el?.id)}
                      type="button"
                      disabled={isReplying === el?.id || reply.trim() === ''}
                      loaderHW={14}
                    />
                    <Button
                      text="Cancel"
                      variant="outline"
                      width="w-min-fit"
                      className="!h-6 text-secondary text-xs flex justify-center items-center border border-secondary  hover:border-secondary"
                      onClick={() => setIsAddReply(null)}
                      disabled={isReplying === el?.id}
                    />
                  </div>
                </div>
              )}
            </div>
            {el?.replies?.length > 0 && (
              <div className="pl-6 pt-4">
                {/* <Loader height={16} width={16} /> */}

                {el?.replies.map((reply, index) => (
                  <>
                    <div
                      key={reply?.id}
                      className="py-2 border-t border-t-gray-200"
                    >
                      <div className="flex flex-1 justify-between items-center ">
                        <div className="flex items-center gap-x-3">
                          <div className="h-9 w-9 flex rounded-full">
                            {reply?.author?.profile_picture ? (
                              <img
                                src={reply?.author?.profile_picture}
                                className="h-full w-full rounded-full"
                              />
                            ) : (
                              <div className="h-full w-full rounded-full object-cover flex justify-center items-center text-sm bg-[#EDD6C2] text-primary-100 font-medium">
                                {reply?.author?.first_name?.charAt(0)}
                                {reply?.author?.last_name?.charAt(0)}
                              </div>
                            )}
                          </div>
                          <div className="flex flex-col ">
                            <div className="font-semibold text-[14px]">
                              {trim(
                                `${reply?.author?.first_name} ${reply?.author?.last_name}`,
                                35
                              )}
                            </div>
                            <div className="text-[#666666] flex items-center text-xs gap-x-1">
                              <ClockIcon height={10} width={10} />
                              {formatPostDate(reply?.created_at)}
                            </div>
                          </div>
                        </div>
                        {String(reply?.author?.id) === String(userData?.id) && (
                          <div className="flex justify-start items-center pt-1 relative">
                            {/* Trigger */}
                            <div
                              onClick={(e) => {
                                e.stopPropagation();
                                setIsPopoverOpen((prev) =>
                                  prev === reply?.id ? null : reply?.id
                                );
                              }}
                              className="cursor-pointer"
                            >
                              <MenuIcon />
                            </div>

                            {/* Popover */}
                            {isPopoverOpen === reply?.id && (
                              <div
                                className="absolute top-4 right-2 z-10"
                                ref={popoverRef} // <-- ref applied to popover box
                              >
                                <div className="flex rounded-lg flex-col shadow-primary bg-white min-w-[120px]">
                                  <div
                                    className="text-secondary hover:bg-primary-light py-2 px-3 flex gap-x-2 justify-start items-center cursor-pointer"
                                    onClick={() => {
                                      setEditReply(reply?.content);
                                      setIsEditingReply(reply?.id);
                                      setIsPopoverOpen(null);
                                    }}
                                  >
                                    <EditIcon height={14} width={14} /> Edit
                                  </div>

                                  <div
                                    className="hover:bg-primary-light py-2 px-3 flex justify-start items-center gap-x-2 text-red-400 cursor-pointer"
                                    onClick={() => {
                                      deleteReply(reply?.id, el?.id);
                                      setIsPopoverOpen(null);
                                    }}
                                  >
                                    <DeleteIcon height={14} width={14} /> Delete
                                  </div>
                                </div>
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                      {isEditingReply === reply?.id ? (
                        <div className="pt-2">
                          <InputField
                            type="textarea"
                            value={editReply}
                            // placeholder="Add reply..."
                            inputClassName="!max-h-64 !h-10"
                            isComment
                            onChange={(e) => setEditReply(e.target.value)}
                            maxLength={2000}
                          />

                          <div className="flex flex-row w-full justify-end gap-x-2 pt-1.5">
                            <Button
                              text={!isEditLoading ? 'Update' : '-----'}
                              variant="outline"
                              width="w-min-fit"
                              className="!h-6 text-primary-100 text-xs flex justify-center items-center"
                              disabled={
                                isEditLoading ||
                                editReply.trim() === '' ||
                                editReply === reply?.content
                              }
                              onClick={() => updateReply(reply?.id)}
                              loaderHW={14}
                            />
                            <Button
                              text="Cancel"
                              variant="outline"
                              width="w-min-fit"
                              className="!h-6 text-secondary text-xs flex justify-center items-center border border-secondary  hover:border-secondary"
                              onClick={() => setIsEditingReply(null)}
                              disabled={isEditLoading}
                            />
                          </div>
                        </div>
                      ) : (
                        <ExpandableText
                          maxLength={90}
                          className="text-[14px] pt-2"
                          text={reply?.content}
                        />
                      )}
                      {String(userData?.id) !== String(reply?.author?.id) && (
                        <div className="flex flex-1 justify-end items-center">
                          <div
                            className=" flex p-1.5 gap-x-1 hover:scale-105 rounded-md text-xs items-center cursor-pointer"
                            onClick={() => {
                              setIsReportOpen({
                                type: 'commentreplay',
                                id: reply?.id,
                              });
                            }}
                          >
                            <ReportIcon height={14} width={14} />
                            <span>Report</span>
                          </div>
                        </div>
                      )}
                    </div>
                    {el?.replay_count > 2 &&
                      el?.nextReply !== 0 &&
                      el?.replies?.length - 1 === index && (
                        <div
                          className={
                            isSinglePost
                              ? 'w-full flex items-center justify-center'
                              : ''
                          }
                        >
                          <Button
                            text="Load more replies"
                            variant="other"
                            className="h-8 border border-gray-200 !rounded-full hover:shadow-none text-primary-100 text-sm !font-normal flex justify-center items-center"
                            onClick={() =>
                              handleLoadMoreReplies(el?.id, el?.nextReply)
                            }
                            loading={isMoreCommentLoading == el?.id}
                            disabled={isMoreCommentLoading == el?.id}
                            width={isSinglePost ? 'w-96' : 'w-full'}
                          />
                        </div>
                      )}
                  </>
                ))}
              </div>
            )}
          </div>
        ))}
        {/* Infinite Scroll Trigger */}
        {currentPage <= totalPages && commentsList?.length > 0 && (
          <div ref={observerRef} className={clsx('text-center p-[1px]')}>
            {isCommentsLoading ? (
              <Loader height={20} width={20} fill="#fff" />
            ) : (
              ''
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default CommentSection;
