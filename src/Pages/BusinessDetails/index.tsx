import React, { useEffect, useState, useRef } from 'react';
import { useForm, Controller } from 'react-hook-form';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import clsx from 'clsx';
import { X } from 'lucide-react';

import { Button, InputField, Modal, InputSelect } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import {
  useGetUserBusinessData,
  useUpdateBusinessData,
} from '@Query/Hooks/useEnterprise';
import { useStateList } from '@Query/Hooks/useAuth';

import ProfileLayout from '@Components/Layout/ProfileLayout';
import { UserCirclePlusIcon, Loader } from '@Icons';
import TextAreaField from '@Components/UI/InputField/TextAreaField';

import queryClient from '@Helpers/QueryClient';
import CacheKeys from '@Helpers/CacheKeys';
import VerifyOtp from '../VerifyOtp';
import { useSelector } from 'react-redux';

// Type Definitions
interface SelectOption {
  label: string;
  value: string;
  [key: string]: any;
}

interface RegisterFormValues {
  companyName: string;
  streetName: string;
  streetNo: string;
  ABNno: string;
  countryCode: SelectOption;
  address: string;
  city: string;
  suburb: string;
  state: SelectOption;
  postCode: string;
}

interface BusinessData {
  company_name: string;
  street_name: string;
  street_no: string,
  abn: string;
  address: string;
  city: string;
  suburb: string;
  short_live_logo_url: string;
  state: SelectOption;
  post_code: string;
}
interface StateData {
  id: number;
  name: string;
}

const BusinessDetails: React.FC = () => {
  const { addToast } = useToast();
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [editToken, setEditToken] = useState<string>('');
  const [currentImage, setCurrentImage] = useState<string>('');
  const [originalBusinessData, setOriginalBusinessData] =
    useState<BusinessData | null>(null);
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const [isEdit, setIsEdit] = useState(false);
  const [isMobileChanged, setIsMobileChanged] = useState(false);
  const [isImageRemoved, setIsImageRemoved] = useState(false);
  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const [isOrgAdmin, setIsOrgAdmin] = useState<boolean>(false);

  const userData = useSelector((state: any) => state.UserControle.user);

  const { data: stateListData, isLoading: stateListLoading } = useStateList();

  const {
    data: viewProfileData,
    isError: isErrorInViewProfile,
    isLoading: isViewProfileLoading,
    refetch: refetchProfileData,
    error: errorOfViewProfile,
    isSuccess: isSuccessViewProfile,
  } = useGetUserBusinessData();

  const { mutate: updateUser, isLoading: isUpdateUserLoading } =
    useUpdateBusinessData({
      onSuccess: (data) => {
        setIsEdit(false);
        setIsImageRemoved(false);
        setSelectedFile(null);
        setIsImageUpdated(false);
        setPreviewUrl(null);
        if (isImageRemoved) {
          setCurrentImage('');
        }
        queryClient.invalidateQueries(CacheKeys.profileData);
        if (data?.data?.data?.is_mobile_updated) {
          setEditToken(data?.data?.data?.verify_token);
          setIsMobileChanged(true);
        }
        addToast('success', data?.data?.message);
        refetchProfileData();
      },
      onError: (error) => {
        addToast('error', error);
      },
    });

  const schema = yup.object().shape({
    companyName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Company name is required')
      .min(3, 'Company name must have at least 3 characters')
      .max(50, 'Company name must have at most 50 characters'),
    streetName: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Street name is required')
      .min(3, 'Street name must have at least 3 characters')
      .max(50, 'Street name must have at most 50 characters'),
    streetNo: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Street no is required')
      .min(1, 'Street no must have at least 1 number')
      .max(5, 'Street no must have at most 5 numbers')
      .matches(/^\d+$/, 'Street no must only contain numbers'),
    ABNno: yup
      .string()
      .required('ABN number is required')
      .matches(/^\d{11}$/, 'ABN number must be exactly 11 digits'),
    address: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('Address is required')
      .max(255, 'Address must have at most 255 characters'),
    city: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('city is required')
      .max(50, 'city must have at most 50 characters'),
    suburb: yup
      .string()
      .trim('Cannot include leading and trailing spaces')
      .strict(true)
      .required('suburb is required')
      .max(50, 'suburb must have at most 50 characters'),
    state: yup.mixed().required('State is required'),
    postCode: yup
      .string()
      .required('Post code no is required')
      .min(1, 'Post code no must have at least 1 numbers')
      .max(10, 'Post code no must have at most 5 numbers')
      .matches(/^\d+$/, 'Post code no must only contain numbers'),
  });

  const {
    control,
    handleSubmit,
    clearErrors,
    formState: { errors },
    setValue,
    reset,
  } = useForm<RegisterFormValues>({
    resolver: yupResolver(schema) as any,
    defaultValues: {
      companyName: '',
      streetName: '',
      streetNo: '',
      ABNno: '',
      address: '',
      city: '',
      suburb: '',
      state: null as unknown as RegisterFormValues['state'],
      postCode: '',
    },
    mode: 'onChange',
  });

  useEffect(() => {
    if (isSuccessViewProfile && viewProfileData?.data?.data) {
      const profileData = viewProfileData.data.data;

      setOriginalBusinessData(profileData);

      if (profileData?.short_live_logo_url) {
        setCurrentImage(profileData.short_live_logo_url);
      } else {
        setCurrentImage('');
      }

      setValue('companyName', profileData.company_name);
      setValue('streetName', profileData.street_name);
      setValue('streetNo', profileData.street_no);
      setValue('ABNno', profileData.abn);
      setValue('address', profileData.address);
      setValue('city', profileData.city);
      setValue('suburb', profileData.suburb);
      setValue('state', profileData.state);
      setValue('postCode', profileData.post_code);

      if (stateListData?.data?.data) {
        const stateId = parseInt(profileData?.state?.toString(), 10);
        const state = stateListData.data.data.find(
          (s: StateData) => s.id === stateId
        );

        if (state) {
          setValue('state', {
            value: state.id.toString(),
            label: state.name,
          });
        }
      }

    }

    if (isErrorInViewProfile) {
      addToast('error', errorOfViewProfile as string);
    }
  }, [
    viewProfileData,
    isSuccessViewProfile,
    isErrorInViewProfile,
    errorOfViewProfile,
    stateListData,
    setValue,
    addToast,
  ]);

  const onSubmit = (data: RegisterFormValues) => {
    const formData = new FormData();

    formData.append('company_name', data.companyName);
    formData.append('street_name', data.streetName);
    formData.append('street_no', data.streetNo);
    formData.append('abn', data.ABNno);
    formData.append('address', data.address);
    formData.append('city', data.city);
    formData.append('suburb', data.suburb);
    formData.append('state', data.state.value);
    formData.append('post_code', data.postCode);

    if (selectedFile) {
      formData.append('logo', selectedFile);
    }
    updateUser({
      formData,
      orgId: '',
      isOrgAdmin: isOrgAdmin,
    });
  };

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      setSelectedFile(file);

      if (currentImage) {
        setIsImageUpdated(true);
      } else if (isImageRemoved) {
        setIsImageUpdated(true);
        setIsImageRemoved(false);
      }
      setCurrentImage('');
      setPreviewUrl(URL.createObjectURL(file));
    }
  };

  const handleButtonClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const onEditSuccess = () => {
    setIsMobileChanged(false);
    setEditToken('');
    queryClient.invalidateQueries(CacheKeys.profileData);
  };

  const clearSelection = (e: React.MouseEvent<HTMLButtonElement>) => {
    e.stopPropagation();
    setSelectedFile(null);
    setPreviewUrl(null);
    if (currentImage) {
      setCurrentImage('');
      setIsImageRemoved(true);
    } else if (isImageUpdated) {
      setIsImageUpdated(false);
      setIsImageRemoved(true);
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  };

  const handleCancel = () => {
    setIsEdit(false);
    setSelectedFile(null);
    setIsImageRemoved(false);
    setIsImageUpdated(false);
    setPreviewUrl(null);
    clearErrors();

    if (originalBusinessData) {
      setCurrentImage(originalBusinessData.short_live_logo_url || '');
      reset({
        companyName: originalBusinessData.company_name,
        streetName: originalBusinessData.street_name,
        streetNo: originalBusinessData.street_no,
        postCode: originalBusinessData.post_code,
        ABNno: originalBusinessData.abn,
        address: originalBusinessData.address,
        city: originalBusinessData.city,
        suburb: originalBusinessData.suburb,
        state: originalBusinessData.state,
      });
    }
  };

  const stateOptions = stateListData?.data?.data?.map((state: StateData) => ({
    label: state.name,
    value: state.id.toString(),
  })) || [];
  return (
    <ProfileLayout>
      <div>
        <div className="flex flex-col justify-between sticky top-0 bg-white z-20">
          <div
            className={clsx(
              'flex justify-between gap-1.5 p-6 px-10 pb-0 h-[40px]'
            )}
          >
            <div className="flex justify-between w-full">
              <h3 className="text-xl font-semibold">Business Details</h3>
              {!isEdit && userData.role !== 'org-user' && (
                <Button
                  text="Edit"
                  type="button"
                  className="!w-[65px] h-[40px] !p-2"
                  onClick={() => setIsEdit(true)}
                  disabled={
                    isViewProfileLoading
                  }
                />
              )}
            </div>
          </div>
        </div>
        <div className="p-10 flex flex-col gap-4 ">
          <div className="max-w-[900px]">
            <form onSubmit={handleSubmit(onSubmit)}>
              <div className="flex flex-col items-center">
                <div
                  className="w-20 h-20 rounded-full bg-gray-100 flex items-center justify-center relative cursor-pointer overflow-hidden"
                  onClick={handleButtonClick}
                >
                  {previewUrl || currentImage ? (
                    <img
                      src={previewUrl || currentImage}
                      alt="Profile preview"
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <UserCirclePlusIcon />
                  )}

                  <input
                    type="file"
                    ref={fileInputRef}
                    disabled={!isEdit}
                    className="hidden"
                    accept=".jpg,.jpeg,.png"
                    onChange={handleFileSelect}
                  />
                </div>
              </div>

              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="companyName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Company Name"
                      field={field}
                      placeholder="First Name"
                      errorMessage={errors.companyName?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="ABNno"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="ABN No"
                      field={field}
                      placeholder="ABN No"
                      errorMessage={errors.ABNno?.message}
                      disabled={!isEdit}
                      type="number"
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="streetName"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Street Name"
                      field={field}
                      placeholder="Street Name"
                      errorMessage={errors.streetName?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="streetNo"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Street No"
                      field={field}
                      placeholder="Street No"
                      errorMessage={errors.streetNo?.message}
                      disabled={!isEdit}
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="city"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="City"
                      field={field}
                      placeholder="City"
                      errorMessage={errors.city?.message}
                      autoFocus
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="suburb"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Suburb"
                      field={field}
                      placeholder="Suburb"
                      errorMessage={errors.suburb?.message}
                      disabled={!isEdit}
                    />
                  )}
                />
              </div>
              <div className="flex gap-x-4 pt-8">
                <Controller
                  name="postCode"
                  control={control}
                  render={({ field }) => (
                    <InputField
                      label="Post Code"
                      field={field}
                      placeholder="Post Code"
                      errorMessage={errors.postCode?.message}
                      disabled={!isEdit}
                    />
                  )}
                />
                <Controller
                  name="state"
                  control={control}
                  render={({ field }) => (
                    <InputSelect
                      label="State"
                      field={field}
                      placeholder="Select your state"
                      errorMessage={errors.state?.message ? String(errors.state.message) : undefined}
                      options={stateOptions}
                      disabled={!isEdit}

                    />
                  )}
                />
              </div>
              <div className="flex gap-x-2 pt-8">
                <div className="w-1/2">
                  <Controller
                    name="address"
                    control={control}
                    render={({ field }) => (
                      <TextAreaField
                        label={'Address'}
                        field={field}
                        placeholder="Address"
                        rows={4}
                        errorMessage={errors.address?.message}
                        disabled={!isEdit}
                      />
                    )}
                  />
                </div>
              </div>
              {isEdit && (
                <div className="mt-8 flex justify-center gap-10">
                  <div className="min-w-[150px]">
                    <Button
                      text="Cancel"
                      type="button"
                      className="w-full"
                      variant="outline"
                      onClick={handleCancel}
                      disabled={isUpdateUserLoading}
                    />
                  </div>
                  <div className="min-w-[150px]">
                    <Button
                      text="Save"
                      type="submit"
                      className="w-full"
                      loading={isUpdateUserLoading}
                    />
                  </div>
                </div>
              )}
            </form>
          </div>
        </div>
      </div>
      {isMobileChanged && (
        <Modal
          isOpen={isMobileChanged}
          size="md"
          outsideClickClose={false}
          header={
            <div className="flex flex-col gap-1.5">
              <h3 className="text-xl text-black font-bold">Verify OTP</h3>
            </div>
          }
          onClose={() => {
            setIsMobileChanged(false);
            queryClient.invalidateQueries(CacheKeys.profileData);
          }}
          children={
            <VerifyOtp
              isEdit={true}
              onEditSuccess={onEditSuccess}
              editToken={editToken}
            />
          }
        />
      )}
    </ProfileLayout>
  );
};

export default BusinessDetails;
