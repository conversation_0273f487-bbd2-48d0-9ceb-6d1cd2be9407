/* eslint-disable */
import { AccessibleImage, Breadcrumb, Button } from '@Components/UI';
import { DownloadIcon, PdfIcon, UploadIcon, MicIcon } from '@Icons';
import PSImg from '@Assets/Images/PS.png';
import { useCallback, useRef, useState } from 'react';
import clsx from 'clsx';
import { useStreamedResponse } from '../Home';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Recorder from '@Components/Recorder';

const breadcrumbItems = [{ label: 'Home', link: '/' }, { label: 'Legal' }];

interface LegalDocObject {
  title: string;
  link: string;
}

const PerformanceSolution = (): JSX.Element => {
  const uploadDocumentsInputRef = useRef<HTMLInputElement | null>(null);
  const uploadDocumentsRef = useRef<HTMLInputElement | null>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  const [isRecordOn, setIsRecordOn] = useState<boolean>(false);
  const [text, setText] = useState<string>('');
  const { addToast } = useToast();

  const [isFileDragging, setIsFileDragging] = useState<boolean>(false);
  const [status, setStatus] = useState<string | null>(null);
  const [isComplete, setIsComplete] = useState(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [showFileError, setShowFileError] = useState<boolean>(false);
  const [docs, setDocs] = useState<LegalDocObject[]>([]);
  const [hideTextArea, setHideTextArea] = useState<boolean>(false);
  const { mutateAsync } = useStreamedResponse();

  const handleUploadClick = () => {
    uploadDocumentsInputRef.current?.click();
  };
  const validateFile = (file: File): boolean => {
    return file.type === 'application/pdf';
  };

  const handleDragOver = useCallback((e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsFileDragging(true);
  }, []);

  const handleDragLeave = useCallback(() => {
    setIsFileDragging(false);
  }, []);

  const handleDrop = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    setIsFileDragging(false);

    const file = event.dataTransfer.files[0];

    if (file && validateFile(file)) {
      handleFileUpload({
        target: { files: [file] },
      } as unknown as React.ChangeEvent<HTMLInputElement>);
    }
  }, []);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file && validateFile(file)) {
      setShowFileError(false);
      setSelectedFile(file);
    } else {
      alert('Only PDF files are allowed.');
    }
  };

  const handleOnSubmit = async (event: React.FormEvent<HTMLFormElement>) => {
    event.preventDefault();
    try {
      const formData = new FormData();
      setIsComplete(true);
      if (selectedFile || text.trim()) {
        if (selectedFile) {
          formData.append('file', selectedFile);
        } else {
          formData.append('search_query', text);
        }

        setStatus('Loading...');

        const reader = await mutateAsync(
          'rag/vcat/ask-vcat-document/',
          formData,
          true
        );

        const decoder = new TextDecoder();
        let textBuffer = '';

        while (true) {
          const { value, done } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          textBuffer += chunk;

          let newlineIndex;
          while ((newlineIndex = textBuffer.indexOf('\n')) >= 0) {
            const rawLine = textBuffer.slice(0, newlineIndex).trim();
            textBuffer = textBuffer.slice(newlineIndex + 1);

            if (!rawLine.startsWith('data: ')) continue;

            const jsonStr = rawLine.replace(/^data:\s*/, '');
            if (!jsonStr || jsonStr === '[DONE]') continue;

            let parsedData: any;
            try {
              parsedData = JSON.parse(jsonStr);
            } catch (e) {
              console.warn('JSON parse failed, skipping:', jsonStr);
              continue;
            }
            // Now process the parsed chunk
            if (parsedData?.type === 'solution_chunk') {
              setStatus(parsedData?.message);
            } else if (parsedData?.type === 'complete') {
              setStatus(null);
              setDocs(parsedData?.data?.links);
              setHideTextArea(true);
            } else if (
              parsedData?.type === 'status' ||
              parsedData?.type === 'metadata'
            ) {
              setStatus(parsedData?.message);
            } else if (parsedData?.type === 'error') {
              setStatus(null);
              setText('');
              setSelectedFile(null);
              if (uploadDocumentsInputRef.current) {
                uploadDocumentsInputRef.current.value = '';
              }
              addToast(
                'error',
                (parsedData?.message as string) || (parsedData?.error as string)
              );
            }
          }
        }
      } else {
        setShowFileError(true);
      }
      setIsComplete(false);
    } catch (error) {
      setStatus(null);
      setIsComplete(false);
    }
  };

  const onRecordClose = (voiceText: string) => {
    if (voiceText) {
      setText(voiceText);
      textareaRef.current?.focus();
    }
  };

  const downloadPdf = async (link: string, title: string) => {
    try {
      const res = await fetch(link, {
        method: 'GET',
        headers: { 'Content-Type': 'application/pdf' },
      });
      if (!res.ok) throw new Error(`Status ${res.status}`);
      const blob = await res.blob();
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = title + '.pdf';
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(url);
    } catch (err: any) {
      addToast('error', err as string);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === 'Enter') {
      if (!e.shiftKey) {
        e.preventDefault();
        const form = e.currentTarget.closest('form');
        form?.requestSubmit();
      }
    }
  };

  return (
    <div className="flex flex-1 w-full h-full flex-col">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Legal</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </div>
      <div className="p-10">
        <div className="flex w-full border border-b-primary ">
          <form className="flex flex-1 flex-col p-8" onSubmit={handleOnSubmit}>
            {!hideTextArea && (
              <>
                <div className="flex flex-row p-1 border w-full max-h-36 group items-end px-2 rounded-[10px] border-b-primary focus-within:border-primary-100 focus-within:shadow-primary">
                  <textarea
                    ref={textareaRef}
                    className="w-full p-2 text-lg border-none outline-none resize-none bg-transparent max-h-30 custom-scrollbar disabled:cursor-not-allowed"
                    rows={3}
                    placeholder="Ask REX Any Legal Questions..."
                    value={text}
                    onChange={(e) => {
                      setText(e.target.value);
                      if (e.target.value.trim() || selectedFile) {
                        setShowFileError(false);
                      }
                    }}
                    onKeyDown={handleKeyDown}
                    autoFocus
                    disabled={!!selectedFile}
                  />
                  <MicIcon
                    height={20}
                    width={20}
                    className={`cursor-pointer hover:scale-110 ${selectedFile ? 'opacity-50 cursor-not-allowed' : ''}`}
                    onClick={() => {
                      if (!selectedFile) setIsRecordOn(true);
                    }}
                  />
                </div>
                <div className="flex items-center justify-center w-full my-2">
                  <p className="px-2 text-center text-gray-500">- or -</p>
                </div>
              </>
            )}
            <div
              ref={uploadDocumentsRef}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onDragLeave={handleDragLeave}
              className={clsx(
                'pb-6 pt-6 w-full flex flex-col bg-[#f1f1f1] items-center text-center rounded-xl cursor-pointer',
                text ? 'cursor-not-allowed opacity-50' : 'cursor-pointer',
                isFileDragging &&
                  'border border-dashed border-primary-100 bg-primary-light scale-105'
              )}
              onClick={handleUploadClick}
            >
              <input
                ref={uploadDocumentsInputRef}
                type="file"
                className="sr-only"
                accept="application/pdf"
                onChange={handleFileUpload}
                multiple={false}
                disabled={!!text}
              />
              <UploadIcon height={24} width={24} />
              <div className="pb-2 pt-3 font-semibold">Upload Case</div>
              <div className="text-secondary w-1/2 text-center">
                Upload your case and find similar cases
              </div>
              {selectedFile && (
                <div className="py-1.5 mt-4 mx-2 rounded-[10px] shadow-primary bg-white flex flex-row w-fit px-6 text-primary-100 items-center gap-x-2">
                  <PdfIcon height={18} width={18} />
                  <div>{selectedFile?.name}</div>
                </div>
              )}
            </div>
            {showFileError && (
              <p className="text-red-500 text-sm break-words">
                Please upload your case.
              </p>
            )}
            <div className="flex pt-2 flex-col">
              <Button
                type="submit"
                text="Submit"
                loading={isComplete}
                className="mt-3"
              />
            </div>

            {status && (
              <div className="mt-2 text-md font-medium text-gray-900">
                <span className="animate-left-to-right-pulse">{status}</span>
              </div>
            )}

            <div className="flex flex-wrap gap-4 py-4">
              {docs.map((el: LegalDocObject, index) => (
                <div
                  key={index}
                  className="flex-1 flex py-2 border border-teal-200 rounded-lg flex-row w-fit px-6 text-primary-100 items-center gap-x-2"
                >
                  <PdfIcon height={16} width={16} />
                  <div className="relative group max-w-[240px]">
                    <div className="truncate overflow-hidden whitespace-nowrap">
                      {el.title}
                    </div>
                    <div className="absolute bottom-full left-0 mb-1 hidden w-max max-w-xs rounded bg-gray-800 px-2 py-1 text-xs text-white shadow-lg group-hover:block z-50">
                      {el.title}
                    </div>
                  </div>
                  <DownloadIcon
                    height={16}
                    width={16}
                    className="cursor-pointer"
                    onClick={() => downloadPdf(el.link, el.title)}
                  />
                </div>
              ))}
            </div>
          </form>

          {isRecordOn && (
            <Recorder setIsRecordOn={setIsRecordOn} onClose={onRecordClose} />
          )}

          <div className="flex flex-1 flex-col">
            <AccessibleImage
              src={PSImg}
              alt="Performance solution image"
              className="h-full w-full"
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceSolution;
