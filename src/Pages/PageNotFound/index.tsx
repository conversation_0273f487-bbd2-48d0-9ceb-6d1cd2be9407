import { AccessibleImage, Button } from '@Components/UI';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router';
import ROBOLOGO from '@Assets/Images/ROBOLOGO.png';

const PageNotFound = () => {
  const navigate = useNavigate();

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.8 }}
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        backgroundColor: '#0d1117',
        color: '#ffffff',
        textAlign: 'center',
      }}
    >
      <motion.div
        initial={{ y: -10 }}
        animate={{ y: [0, -10, 0] }}
        transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
      >
        <AccessibleImage src={ROBOLOGO} alt="robologo" className="w-32 h-32" />
      </motion.div>
      {/* Floating 404 */}
      <motion.h1
        initial={{ y: -10 }}
        animate={{ y: [0, -10, 0] }}
        transition={{ repeat: Infinity, duration: 2, ease: 'easeInOut' }}
        style={{
          fontSize: '8rem',
          fontWeight: 'bold',
          textShadow: '0px 0px 20px rgba(0,255,255,0.6)',
        }}
      >
        404
      </motion.h1>

      {/* Bouncy Error Text */}
      <motion.p
        initial={{ scale: 0.8 }}
        animate={{ scale: 1 }}
        transition={{ duration: 0.5, ease: 'easeOut' }}
        style={{
          fontSize: '1.5rem',
          marginBottom: '20px',
          color: '#d1d5db',
        }}
      >
        Oops! The page you're looking for doesn't exist.
      </motion.p>

      {/* Wiggling Button */}
      <motion.div
        whileHover={{ scale: 1.1, rotate: [0, 2, -2, 0] }}
        whileTap={{ scale: 0.9 }}
        transition={{ type: 'spring', stiffness: 200 }}
        onClick={() => navigate('/')}
        style={{
          boxShadow: '0px 4px 10px rgba(56, 189, 248, 0.3)',
        }}
      >
        <Button width="w-[200px]" text={'Go to home'} />
      </motion.div>
    </motion.div>
  );
};

export default PageNotFound;
