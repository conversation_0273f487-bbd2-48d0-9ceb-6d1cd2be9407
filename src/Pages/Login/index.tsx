import React, { useEffect, useRef, useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { useForm, Controller } from 'react-hook-form';
import { v4 as uuidv4 } from 'uuid';
import * as yup from 'yup';
import { yupResolver } from '@hookform/resolvers/yup';
import { setUserData } from '@Redux/SystemControl/UserControle';
import { Button, InputField, InputSelect } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { requestForToken } from '@Config/firebaseConfig';
import { PATHS } from '@Config/Path.Config';
import Api from '@Helpers/Api';

import { API_PATHS } from '@Helpers/Constants';
import {
  COUNTRY_CODES,
  DEFAULT_COUNTRY_CODE,
} from '@Helpers/PhoneCountryCodes';

const loginSchema = yup.object().shape({
  phoneNumber: yup
    .string()
    .matches(/^\d{8,12}$/, 'Enter a valid mobile number')
    .required('Mobile number is required'),
  countryCode: yup
    .object()
    .shape({
      value: yup.string().required('Country code is required'),
      label: yup.string().required('Country code label is required'),
    })
    .required('Country code is required'),
});

interface LoginFormValues {
  phoneNumber: string;
  countryCode: {
    value: string;
    label: string;
  };
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const dispatch = useDispatch();
  const api = useRef(new Api()).current;

  const [loading, setLoading] = useState<boolean>(false);

  const [deviceState, setDeviceState] = useState({
    deviceId: '',
    firebaseToken: '',
    isInitialized: false,
    notificationPermission: null as NotificationPermission | null,
  });

  const getStoredFormValues = useCallback(() => {
    const storedValues = sessionStorage.getItem('loginFormValues');
    if (storedValues) {
      return JSON.parse(storedValues) as LoginFormValues;
    }
    return {
      phoneNumber: '',
      countryCode: DEFAULT_COUNTRY_CODE,
    };
  }, []);

  const {
    control,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<LoginFormValues>({
    resolver: yupResolver(loginSchema),
    defaultValues: getStoredFormValues(),
    mode: 'onChange',
  });

  const formValues = watch();

  useEffect(() => {
    if (
      formValues.phoneNumber ||
      formValues.countryCode.value !== DEFAULT_COUNTRY_CODE.value
    ) {
      sessionStorage.setItem('loginFormValues', JSON.stringify(formValues));
    }
  }, [formValues]);

  const getDeviceId = useCallback(() => {
    let storedDeviceId = localStorage.getItem('deviceId');
    if (!storedDeviceId) {
      storedDeviceId = uuidv4();
      localStorage.setItem('deviceId', storedDeviceId);
    }
    return storedDeviceId;
  }, []);

  const initializeDevice = useCallback(async () => {
    const checkNotificationPermission = async () => {
      if (!('Notification' in window)) {
        addToast('error', 'This browser does not support notifications');
        return false;
      }

      let permission = Notification.permission;
      if (permission === 'default') {
        try {
          permission = await Notification.requestPermission();
          // eslint-disable-next-line @typescript-eslint/no-unused-vars
        } catch (error) {
          addToast('error', 'Failed to request notification permissions');
          return false;
        }
      }

      return permission === 'granted';
    };

    const deviceId = getDeviceId();
    const firebaseToken = (await requestForToken()) || '';
    const notificationPermission = await checkNotificationPermission();

    setDeviceState({
      deviceId,
      firebaseToken,
      isInitialized: notificationPermission,
      notificationPermission: Notification.permission,
    });
  }, [addToast, getDeviceId]);

  useEffect(() => {
    initializeDevice();
  }, [initializeDevice]);

  const onSubmit = useCallback(
    async ({ phoneNumber, countryCode }: LoginFormValues) => {
      setLoading(true);
      const { deviceId, firebaseToken, isInitialized } = deviceState;

      // if (!isInitialized) {
      //   addToast('error', 'Please allow notifications to proceed');
      //   setLoading(false);
      //   return;
      // }

      try {
        const { data } = await api.post(API_PATHS.LOGIN, {
          data: {
            mobile_no: phoneNumber,
            country_code: countryCode.value,
            device_id: deviceId ? deviceId : getDeviceId(),
            device_token: firebaseToken,
          },
        });
        const responseData = data;
        if (responseData) {
          localStorage.setItem(
            'verify_token',
            responseData?.data?.verify_token
          );
          dispatch(setUserData(responseData?.data));
          setLoading(false);
          navigate(PATHS.VERIFY_OTP);
        }
      } catch (error) {
        addToast('error', error as string);
        setLoading(false);
      }
    },
    [deviceState, addToast, api, navigate]
  );

  return (
    <div>
      <form
        className="flex flex-col space-y-4 pt-2"
        onSubmit={handleSubmit(onSubmit)}
        autoComplete="off"
      >
        <div className="relative mt-6">
          <label className="text-sm font-medium text-left absolute -top-5 text-gray-700">
            Mobile Number
          </label>
          <div className="flex gap-x-2">
            <div className="w-[108px]">
              <Controller
                name="countryCode"
                control={control}
                render={({ field }) => (
                  <InputSelect
                    label
                    field={field}
                    placeholder="+61"
                    errorMessage={errors.countryCode?.message}
                    options={COUNTRY_CODES}
                  />
                )}
              />
            </div>
            <div className="w-full">
              <Controller
                name="phoneNumber"
                control={control}
                render={({ field }) => (
                  <InputField
                    label
                    field={field}
                    placeholder="Enter your mobile number"
                    errorMessage={errors.phoneNumber?.message}
                    autoFocus
                  />
                )}
              />
            </div>
          </div>
        </div>

        <Button text="Login" className="mt-2" type="submit" loading={loading} />

        <Button
          text={
            <span className="font-normal">
              Don't have an account? <span className="font-bold">Sign up</span>
            </span>
          }
          className="mt-2"
          variant="outline"
          onClick={() => navigate('/sign-up')}
        />
      </form>
    </div>
  );
};

export default Login;
