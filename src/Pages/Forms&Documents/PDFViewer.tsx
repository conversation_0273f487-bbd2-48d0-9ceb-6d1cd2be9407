import { useEffect, useRef } from 'react';
import Logo from '@Assets/Images/RexLogo.png';
import { useLocation } from 'react-router';
import clsx from 'clsx';

declare global {
  interface Window {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    AdobeDC?: any;
  }
}

const AdobePdfViewer = () => {
  const viewerRef = useRef(null);
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const fileUrl = searchParams.get('fileUrl');
  const isMobile = searchParams.get('isMobile');
  const formName = searchParams.get('formName');

  useEffect(() => {
    const adobeDCView = new window.AdobeDC.View({
      clientId: import.meta.env.VITE_ADOBE_CLIENT_ID, // Replace with your actual Client ID
      divId: 'adobe-dc-view',
    });

    adobeDCView.previewFile(
      {
        content: {
          location: {
            url: fileUrl,
          },
        },
        metaData: { fileName: formName || 'Form_DoCument.pdf' },
      },
      {
        embedMode: 'FULL_WINDOW',
        showDownloadPDF: true,
        showPrintPDF: true,
        showAnnotationTools: true,
        enableFormFilling: true,
      }
    );
  }, []);

  return (
    <div className="h-[100vh] w-screen flex flex-1 flex-col">
      {!isMobile && (
        <div className="h-[8vh] px-6 flex flex-1 justify-between items-center shadow-primary mb-1">
          <div className="flex h-[72px] w-[120px]">
            <img src={Logo} className="h-full w-full" />
          </div>
        </div>
      )}
      <div
        id="adobe-dc-view"
        className={clsx('w-full', isMobile ? 'h-[100%]' : 'h-[92vh]')}
        ref={viewerRef}
      ></div>
      {/* </div> */}
    </div>
  );
};

export default AdobePdfViewer;
