import { Button } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { EditIcon, Loader, UserCirclePlusIcon } from '@Icons';
import React, { useEffect, useRef, useState } from 'react';

export const EnterpriseLogoModal = ({
  isFormLoading = false,
  setError,
  error = '',
  handleUpdateLogo,
  orgId = null,
}: {
  isFormLoading?: boolean;
  error?: string;
  orgId: string | null;
  setError: (val: string) => void;
  handleUpdateLogo: (
    isImageUpdated?: boolean,
    selectedFile?: File | null,
    currentImage?: string | null,
    id?: string | null
  ) => Promise<void>;
}): JSX.Element => {
  const api = new Api();
  const fileInputRef = useRef<HTMLInputElement | null>(null);
  const { addToast } = useToast();

  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [currentImage, setCurrentImage] = useState<string | null>(null);
  const [isImageUpdated, setIsImageUpdated] = useState(false);
  const [isLoading, setIsLoading] = useState<boolean>(true);

  useEffect(() => {
    (async () => {
      try {
        const res = await api.get(API_PATHS.BUSINESS_DETAILS);
        console.log("🚀 ~ res:", res)

        if (res?.data?.status) {
          setCurrentImage(res?.data?.data?.short_live_logo_url);
        } else {
          addToast('error', res?.data?.message);
        }
      } catch (err) {
        addToast('error', err as string);
      } finally {
        setIsLoading(false);
      }
    })();
  }, []);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file && ['image/png', 'image/jpeg', 'image/jpg'].includes(file.type)) {
      setSelectedFile(file);
      setIsImageUpdated(true);
      setCurrentImage(null);
      setPreviewUrl(URL.createObjectURL(file));
      setError('');
    } else {
      setCurrentImage(null);
      setSelectedFile(null);
      setPreviewUrl(null);
      setError('Please upload a valid PNG or JPG image.');
      e.target.value = ''; // Reset the input
    }
  };

  const handleSelectLogo = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  // const handleUpdateLogo = async () => {
  //   if (currentImage || selectedFile) {
  //     try {
  //       setIsFormLoading(true);
  //       const formData = new FormData();

  //       formData.append('form_id', formId as string);
  //       formData.append('is_logo_updated', isImageUpdated ? 'true' : 'false');
  //       if (isImageUpdated && selectedFile) {
  //         formData.append('logo_file', selectedFile);
  //       }

  //       const response = await api.post(API_PATHS.GET_FORM_UPDATE_LOGO, {
  //         data: formData as unknown as Record<string, unknown>,
  //         isMultipart: true,
  //       });

  //       if (response?.data?.status) {
  //         const params = new URLSearchParams({
  //           fileUrl: response?.data?.data?.file_url,
  //         });
  //         const url = `/show-pdf?${params.toString()}`;
  //         if (onClose) onClose();
  //         window.open(url, '_blank'); // opens in a new tab
  //       } else {
  //         addToast('error', response?.data?.message);
  //       }
  //     } catch (err) {
  //       addToast('error', err as string);
  //     } finally {
  //       setIsFormLoading(false);
  //     }
  //   } else {
  //     setError('Please add a logo');
  //   }
  // };

  return (
    <div className="min-h-[36px]">
      {isLoading ? (
        <Loader height={24} width={24} fill="#fff" />
      ) : (
        <div>
          <div className="flex flex-col items-center">
            <div className="font-medium pb-2 text-lg">
              {!currentImage ? 'Add Logo' : 'Update Logo'}
            </div>
            <div
              className="w-20 h-20 flex items-center justify-center relative cursor-pointer overflow-hidden"
              onClick={handleSelectLogo}
            >
              {previewUrl || currentImage ? (
                <img
                  src={previewUrl ?? currentImage ?? undefined}
                  alt="Profile preview"
                  className="w-full h-full object-cover border border-primary-100 p-1  rounded-full"
                />
              ) : (
                <UserCirclePlusIcon height={64} width={64} />
              )}
              {(selectedFile || currentImage) && (
                <div className="absolute right-0 top-0 text-primary-100  bg-white border border-primary-100 p-1 rounded-full">
                  <EditIcon height={12} width={12} fill="currentColor" />
                </div>
              )}
              <input
                type="file"
                ref={fileInputRef}
                className="hidden"
                accept=".jpg,.jpeg,.png"
                onChange={handleFileSelect}
              />
            </div>
          </div>
          {error && <p className="text-red-500 text-sm break-words">{error}</p>}
          <Button
            text={'Get Form'}
            className="mt-6"
            type="button"
            onClick={() =>
              handleUpdateLogo(isImageUpdated, selectedFile, currentImage)
            }
            loading={isFormLoading}
            disabled={isFormLoading}
          />
        </div>
      )}
    </div>
  );
};
