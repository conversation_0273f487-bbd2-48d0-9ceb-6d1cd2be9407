import { DocDetailCard } from '@Components/Common';
import { Breadcrumb, Button, InputField, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { useDebounce } from '@Hooks/useDebouce';
import { Loader, SearchIcon } from '@Icons';
import { useEffect, useRef, useState } from 'react';
import { EnterpriseLogoModal } from './EnterpriseLogoModal';
import { useSelector } from 'react-redux';

const breadcrumbItems = [
  { label: 'Home', link: '/' },
  { label: 'Forms & Documents' },
];

export interface DocumentItem {
  id: number;
  name: string;
  file: string;
  file_type: string;
  is_active: boolean;
  category: number;
  category_name: string;
  created_at: string; // You can change this to `Date` if parsing into a Date object
  updated_at: string; // Same here
}

const FormsAndDocuments = (): JSX.Element => {
  const api = new Api();
  const observerRef = useRef<HTMLDivElement | null>(null);
  const { addToast } = useToast();
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state.UserControle.user);

  const [data, setData] = useState<DocumentItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalPages, setTotalPages] = useState<number>(1);
  const [search, setSearch] = useState<string>('');
  const debouncedSearchTerm = useDebounce(search, 1000);
  const [isLogoModalOpen, setIsLogoModalOpen] = useState<boolean>(false);
  const [formId, setFormId] = useState<number | null>(null);
  const [formName, setFormName] = useState<string | null>(null);
  const [isFormLoading, setIsFormLoading] = useState<boolean>(false);
  const [error, setError] = useState<string>('');
  const [fileUrl, setFileUrl] = useState<string | null>(null);

  const fetchForms = async (reset: boolean = false) => {
    try {
      const response = await api.get(API_PATHS.GET_FORMS_AND_DOCS_LIST, {
        params: {
          page_size: 10,
          page: currentPage,
          search: debouncedSearchTerm,
        },
      });
      if (response?.data?.status) {
        let tempForms: DocumentItem[] = [];
        if (data?.length && !reset) {
          tempForms = [...data];
        }
        tempForms = [...tempForms, ...response.data.data.list];
        setData([...tempForms]);
        setTotalPages(response?.data?.data?.total_pages);
      } else {
        addToast('error', response?.data?.message);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    // If debouncedSearchTerm is active, the search effect is already handling the fetch.
    if (debouncedSearchTerm.trim() === '') {
      fetchForms();
    }
  }, [currentPage]);

  useEffect(() => {
    setCurrentPage(1);
    fetchForms(true);
  }, [debouncedSearchTerm]);

  // Infinite Scroll Observer
  useEffect(() => {
    if (isLoading) return;

    const observer = new IntersectionObserver(
      (entries) => {
        if (entries[0].isIntersecting) {
          setCurrentPage((p) => Math.min(p + 1, totalPages));
        }
      },
      { threshold: 1.0 }
    );

    if (observerRef.current) observer.observe(observerRef.current);

    return () => observer.disconnect();
  }, [isLoading]);

  const searchHandler = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value.trim();

    if (!search.trim() && searchTerm === '') {
      return;
    }

    setIsLoading(true);
    setData([]);
    setSearch(searchTerm);
  };

  const handleLogoModal = (formId: number) => {
    setFormId(formId);
    setIsLogoModalOpen((prev) => !prev);
  };

  const handleLogoModalClose = () => {
    setIsLogoModalOpen(false);
    setFormId(null);
    setFormName(null);
  };

  const handleFileModalClose = () => {
    setFileUrl(null);
    setFormName(null);
  };

  const handleUpdateLogo = async (
    isImageUpdated: boolean = false,
    selectedFile: File | null = null,
    currentImage: unknown | string | null = null,
    id: string | null = null
  ) => {
    if (currentImage || selectedFile || userData?.role === 'org-user') {
      try {
        setIsFormLoading(true);
        const formData = new FormData();
        const fId = formId ? formId : id;
        formData.append('form_id', fId as unknown as string);
        formData.append('is_logo_updated', isImageUpdated ? 'true' : 'false');
        if (isImageUpdated && selectedFile) {
          formData.append('logo_file', selectedFile);
        }

        const response = await api.post(API_PATHS.GET_FORM_UPDATE_LOGO, {
          data: formData as unknown as Record<string, unknown>,
          isMultipart: true,
        });

        if (response?.data?.status) {
          const params = new URLSearchParams({
            fileUrl: response?.data?.data?.file_url,
            formName: formName || 'Forms_Document.pdf',
          });
          const url = `/show-pdf?${params.toString()}`;
          setFileUrl(url);
          handleLogoModalClose();
        } else {
          addToast('error', response?.data?.message);
        }
      } catch (err) {
        addToast('error', err as string);
      } finally {
        setIsFormLoading(false);
      }
    } else {
      setError('Please add a logo');
    }
  };

  return (
    <div className="flex flex-col h-full w-full">
      <Modal
        isOpen={isLogoModalOpen}
        onClose={handleLogoModalClose}
        header={'Get Form'}
        hideCloseButton={isFormLoading}
        outsideClickClose={!isFormLoading}
      >
        <EnterpriseLogoModal
          isFormLoading={isFormLoading}
          error={error}
          setError={setError}
          handleUpdateLogo={handleUpdateLogo}
          orgId={userData?.role !== 'user' ? userData?.org_id : null}
        />
      </Modal>
      <Modal
        isOpen={!!fileUrl}
        onClose={handleFileModalClose}
        header={'View PDF'}
        outsideClickClose={false}
      >
        <div className="px-4 py-2 text-center flex flex-col">
          <div className="text-secondary pb-4">
            Your PDF is ready. Click on the button below to view.
          </div>
          <Button
            text="View PDF"
            type="button"
            onClick={() => {
              if (fileUrl) {
                window.open(fileUrl, '_blank');
                handleFileModalClose();
              }
            }}
          />
        </div>
      </Modal>
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Forms & Documents</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="w-[389px]">
          <InputField
            containerClassName="pt-0"
            placeholder="Search..."
            rightIcon={<SearchIcon height={25} width={25} />}
            field={null}
            onChange={searchHandler}
            value={search}
          />
        </div>
      </div>

      <div className="pt-0 h-dvh overflow-y-auto custom-scrollbar p-10">
        {isLoading && !data?.length && (
          <div className="flex py-6  w-full justify-center items-center">
            <Loader fill="#fff" height={36} width={36} />
          </div>
        )}
        {!isLoading && !data?.length && currentPage === 1 && (
          <div className="flex py-6 text-secondary flex-1 h-full w-full justify-center items-center">
            No Data Found
          </div>
        )}
        {data?.length > 0 && (
          <>
            <div className="pt-0 flex flex-col gap-4">
              {data?.map((el: DocumentItem, index) => (
                <DocDetailCard
                  key={index}
                  title={`${el?.name}`}
                  onDownload={() => {
                    if (userData?.role !== 'org-user') {
                      setFormName(el?.name);
                      handleLogoModal(el?.id);
                    } else {
                      setFormId(el?.id);
                      setFormName(el?.name);
                      setTimeout(() => {
                        handleUpdateLogo(false, null, null, String(el?.id));
                      }, 500);
                    }
                  }}
                  loading={formId === el?.id && userData?.role === 'org-user'}
                />
              ))}
            </div>
            {/* Infinite Scroll Trigger */}
            {currentPage <= totalPages && (
              <div ref={observerRef} className="text-center p-4">
                {isLoading ? <Loader height={36} width={36} fill="#fff" /> : ''}
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default FormsAndDocuments;
