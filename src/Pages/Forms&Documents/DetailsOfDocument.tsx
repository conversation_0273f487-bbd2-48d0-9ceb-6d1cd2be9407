import { DocDetailCard } from '@Components/Common';
import { Accordion, Breadcrumb, InputField } from '@Components/UI';
import { SearchIcon } from '@Icons';

const DetailsDocuments = (): JSX.Element => {
  const breadcrumbItems = [
    { label: 'Home', link: '/' },
    { label: 'Forms & Documents', link: '/forms-and-documents' },
    { label: 'Lorem-ipsum-document' }, // Active Page (No Link)
  ];

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">Forms & Documents</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
        <div className="w-[389px]">
          <InputField
            field={null}
            containerClassName="pt-0"
            placeholder="Search..."
            rightIcon={<SearchIcon height={25} width={25} />}
          />
        </div>
      </div>

      <div className="pt-0 p-10">
        <Accordion title="Lorem ipsum dolor sit amet" isOpen showIcon={false}>
          {new Array(10).fill(null).map((_, index) => (
            <DocDetailCard
              key={index}
              title={`Important doc ${index + 1}`}
              onDownload={() => console.log('Downloading', index + 1)}
            />
          ))}
        </Accordion>
      </div>
    </div>
  );
};

export default DetailsDocuments;
