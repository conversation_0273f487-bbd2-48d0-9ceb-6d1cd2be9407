import { useState, useEffect, useRef, useCallback } from 'react';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { <PERSON><PERSON>crumb, <PERSON><PERSON>, Button } from '@Components/UI';
import {
  BellIcon,
  DeleteIcon,
  CloseIcon,
  ClockIcon,
  VideoIcon,
  FileIcon,
  BellRingingIcon,
  QuestionsIcon,
  ChartLineUpIcon,
  UserCircleIcon,
  ClockCounterwiseIcon,
} from '@Icons';
import { AnimatePresence, motion } from 'framer-motion';

import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import relativeTime from 'dayjs/plugin/relativeTime';
import localizedFormat from 'dayjs/plugin/localizedFormat';
import isToday from 'dayjs/plugin/isToday';
import isYesterday from 'dayjs/plugin/isYesterday';
import { useNavigate } from 'react-router';
import { PATHS } from '@Config/Path.Config';

dayjs.extend(utc);
dayjs.extend(relativeTime);
dayjs.extend(localizedFormat);
dayjs.extend(isToday);
dayjs.extend(isYesterday);

interface NotificationItem {
  id: number;
  title: string;
  message: string;
  notification_type: string;
  is_read: boolean;
  created_at: string;
  custom_data: {
    video_id?: string;
    form_id?: string;
    post_id?: string;
  };
}

interface PaginationResponse {
  total_record: number;
  next: number;
  previous: number;
  total_pages: number;
  list: NotificationItem[];
  notifications_count: number;
}

const Notification = (): JSX.Element => {
  const { addToast } = useToast();
  const api = new Api();
  const navigate = useNavigate();
  const observerRef = useRef<IntersectionObserver | null>(null);
  const loaderRef = useRef<HTMLDivElement | null>(null);

  const [notificationList, setNotificationList] = useState<NotificationItem[]>(
    []
  );
  const [selectedRow, setSelectedRow] = useState<NotificationItem | null>(null);

  const [openDeleteModal, setOpenDeleteModal] = useState<boolean>(false);
  const [openDeleteAllModal, setOpenDeleteAllModal] = useState<boolean>(false);
  const [pagination, setPagination] = useState({
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 5,
    totalItems: 0,
    hasMore: true,
  });
  const [loading, setLoading] = useState(false);

  const getNotificationList = async (page = 1, isInitialLoad = false) => {
    if (loading) return;

    setLoading(true);
    try {
      const { data } = await api.get(
        `${API_PATHS.NOTIFICATION_LIST}?page=${page}&page_size=${pagination.itemsPerPage}`
      );
      if (data?.data) {
        // For initial load, replace the list; for subsequent loads, append to the list
        setNotificationList((prev) =>
          isInitialLoad ? data.data.list : [...prev, ...data.data.list]
        );

        setPagination((prev) => ({
          ...prev,
          currentPage: page,
          totalPages: data.data.total_pages,
          totalItems: data.data.total_record,
          hasMore: page < data.data.total_pages,
        }));
      }
    } catch (error: any) {
      addToast('error', error);
    } finally {
      setLoading(false);
    }
  };

  // Load more data when user scrolls to bottom
  const handleObserver = useCallback(
    (entries: IntersectionObserverEntry[]) => {
      const [target] = entries;
      if (target.isIntersecting && pagination.hasMore && !loading) {
        getNotificationList(pagination.currentPage + 1, false);
      }
    },
    [pagination.currentPage, pagination.hasMore, loading]
  );

  // Setup IntersectionObserver when component mounts
  useEffect(() => {
    const options = {
      root: null,
      rootMargin: '0px',
      threshold: 0.1,
    };

    observerRef.current = new IntersectionObserver(handleObserver, options);

    if (loaderRef.current) {
      observerRef.current.observe(loaderRef.current);
    }

    return () => {
      if (observerRef.current) {
        observerRef.current.disconnect();
      }
    };
  }, [handleObserver]);

  const markAsRead = async (notificationId: number, showMessage: boolean) => {
    try {
      const res = await api.patch(
        `${API_PATHS.NOTIFICATION}${notificationId}/read/`
      );
      setNotificationList((prev) =>
        prev.map((item) =>
          item.id === notificationId ? { ...item, is_read: true } : item
        )
      );
      if (showMessage) {
        addToast('success', res?.data?.message);
      }
    } catch (error: any) {
      addToast('error', error);
    }
  };

  const markAllAsRead = async () => {
    try {
      const res = await api.post(API_PATHS.NOTIFICATION_READ_ALL);
      setNotificationList((prev) =>
        prev.map((item) => ({ ...item, is_read: true }))
      );

      addToast('success', res?.data?.message);
    } catch (error: any) {
      addToast('error', error);
    }
  };

  const deleteNotification = async () => {
    try {
      if (!selectedRow) return;

      setLoading(true);
      const res = await api.delete(
        `${API_PATHS.NOTIFICATION}${selectedRow.id}/`
      );
      setOpenDeleteModal(false);
      addToast('success', res?.data?.message);
      setSelectedRow(null);
      setLoading(false);
      getNotificationList(1, true); // Refresh the list from the first page
    } catch (error: any) {
      addToast('error', error?.message || 'Something went wrong');
    }
  };

  const deleteAllNotifications = async () => {
    try {
      setLoading(true);
      const res = await api.post(API_PATHS.NOTIFICATION_DELETE_ALL);
      addToast(
        'success',
        res?.data?.message || 'All notifications deleted successfully'
      );
      getNotificationList(1, true); // Refresh the list from the first page
      setOpenDeleteAllModal(false);
      setLoading(false);
    } catch (error: any) {
      addToast('error', error?.message || 'Something went wrong');
    }
  };

  // Initial load
  useEffect(() => {
    getNotificationList(1, true);
  }, []);

  const onClickNotification = (notification: NotificationItem) => {
    if (notification.is_read === false) {
      markAsRead(notification.id, false);
    }

    switch (notification.notification_type) {
      case 'TRAINING_AND_EDUCATION':
        navigate(`/video/${notification.custom_data.video_id}`);
        break;

      case 'FORMS_AND_DOCUMENT':
        navigate(PATHS.FORMSANDDOC);
        break;

      case 'COMMENT':
      case 'REPLY':
      case 'REPORT':
        navigate(`${PATHS.TRADIE_HUB}/${notification.custom_data.post_id}`);
        break;

      case 'LICENSE_REJECTED':
      case 'ORG_USER_UPDATED':
      case 'ORG_USER_DELETED':
        navigate(PATHS.PROFILE);
        break;

      case 'PDF_GENERATED':
        navigate(PATHS.HISTORY);
        break;

      case 'SUBSCRIPTION_ENDING':
        navigate(PATHS.MY_SUBSCRIPTION);
        break;

      case 'ANALYZE_DOCUMENT':
        navigate(PATHS.ANALYTICS);
        break;

      default:
        console.warn('Unknown notification type');
    }
  };

  const formatPostDate = (utcDateString: string): string => {
    const localDate = dayjs.utc(utcDateString).local();

    if (localDate.isToday()) {
      return localDate.format('hh:mm A'); // Example: 04:30 PM
    }

    if (localDate.isYesterday()) {
      return 'Yesterday';
    }

    const now = dayjs();
    const diffInDays = now.diff(localDate, 'day');
    const diffInWeeks = now.diff(localDate, 'week');
    const diffInMonths = now.diff(localDate, 'month');

    if (diffInDays < 7) {
      return `${diffInDays} day${diffInDays > 1 ? 's' : ''} ago`;
    }

    if (diffInWeeks < 4) {
      return `${diffInWeeks} week${diffInWeeks > 1 ? 's' : ''} ago`;
    }

    return `${diffInMonths} month${diffInMonths > 1 ? 's' : ''} ago`;
  };

  const breadcrumbItems = [
    { label: 'Home', link: '/' },
    { label: 'Notifications' },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        when: 'beforeChildren',
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 20,
      },
    },
    exit: {
      opacity: 0,
      x: -300,
      transition: {
        type: 'tween',
        ease: 'easeOut',
        duration: 0.3,
      },
    },
  };

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10">
        <div>
          <h1 className="text-2xl md:text-[32px] font-semibold text-gray-800">
            Notifications
          </h1>
        </div>
        <motion.div
          className="flex gap-3 ml-auto"
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <button
            className="h-8 px-4 cursor-pointer border-[1px] border-[#E0DEF7] text-[#000000] font-medium rounded-md 
            transition-all duration-200 hover:bg-[#FD3B30] hover:text-white"
            onClick={() => setOpenDeleteAllModal(true)}
          >
            Delete All
          </button>

          <button
            className="h-8 px-4 cursor-pointer border-[1px] border-[#E0DEF7] text-[#000000] font-medium rounded-md 
            transition-all duration-200 hover:bg-[#FFA033] hover:text-white"
            onClick={markAllAsRead}
          >
            Mark as read
          </button>
        </motion.div>
      </div>

      <div className="flex flex-col gap-4 p-8">
        {notificationList.length > 0 ? (
          <>
            <motion.div
              className="relative flex flex-1 flex-col gap-y-4 overflow-auto h-full max-h-[75vh] custom-scrollbar px-2 "
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <AnimatePresence>
                {notificationList.map((item) => (
                  <motion.div
                    key={item.id}
                    className="relative"
                    variants={itemVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    layout
                  >
                    <div
                      className={`
                flex flex-col border rounded-[8px] p-4 gap-y-2
                transition-all duration-300 shadow-primary
                ${item.is_read ? 'border-gray-200 bg-white shadow-sm'
                          : 'border-l-4 border-l-[#FFA033] border-t-gray-200 border-r-gray-200 border-b-gray-200 bg-white shadow-primary'
                        }
              `}
                    >
                      <div className="flex-1">
                        <div className="flex justify-between items-start">
                          <div
                            // onClick={() => onClickNotification(item)}
                            className="cursor-pointer flex-1 group"
                          >
                            <h2
                              className={`
                              flex items-center gap-2 text-lg font-semibold 
                              ${item.is_read ? 'text-gray-600' : 'text-gray-800'}
                              group-hover:text-[#FFA033] transition-colors
                            `}
                            >
                              <span>{item.title}</span>
                              {!item.is_read && (
                                <span className="inline-block w-2 h-2 bg-[#FFA033] rounded-full animate-pulse" />
                              )}
                            </h2>
                            <p
                              className={`
                              text-sm mt-1 
                              ${item.is_read ? 'text-gray-500' : 'text-gray-700'}
                              break-words
                              max-w-[100ch]
                            `}
                              style={{
                                display: 'block',
                                overflowWrap: 'break-word',
                                wordWrap: 'break-word'
                              }}
                            >
                              {item.message}
                            </p>
                            <div className="mt-2 flex items-center gap-3">
                              <div className="text-[#666666] flex items-center text-xs gap-x-1">
                                <ClockIcon height={12} width={12} />
                                {formatPostDate(item.created_at)}
                              </div>
                            </div>
                          </div>

                          <motion.div
                            onClick={() => {
                              setOpenDeleteModal(true);
                              setSelectedRow(item);
                            }}
                            whileHover={{
                              scale: 1.1,
                            }}
                            whileTap={{ scale: 0.9 }}
                            className={`
                              cursor-pointer h-9 w-9 flex justify-center items-center 
                              rounded-full shrink-0 ml-2 transition-all
                            `}
                          >
                            <DeleteIcon fill='#FFA033' className="h-5 w-5 text-gray-600" />
                          </motion.div>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                ))}
              </AnimatePresence>

              {/* Loading indicator */}
              <div ref={loaderRef} className="w-full py-4 text-center">
                {loading && pagination.hasMore && (
                  <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-[#FFA033] border-r-transparent"></div>
                )}
                {!pagination.hasMore && notificationList.length > 0 && (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    className="text-gray-500 text-sm"
                  >
                    No more notifications
                  </motion.p>
                )}
              </div>
            </motion.div>
          </>
        ) : loading ? (
          <div className="text-center py-8">
            <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-[#FFA033] border-r-transparent"></div>
            <p className="mt-2 text-gray-600">Loading notifications...</p>
          </div>
        ) : (
          <div className="text-center py-16 bg-white rounded-xl shadow-md">
            <div className="bg-gray-100 h-20 w-20 flex justify-center items-center rounded-full mx-auto">
              <BellRingingIcon className="h-10 w-10 text-gray-400" />
            </div>
            <h3 className="mt-4 text-lg font-medium text-gray-700">
              No notifications
            </h3>
            <p className="mt-2 text-gray-500">
              You're all caught up! Check back later for updates.
            </p>
          </div>
        )}
      </div>
      <Modal
        size="md"
        children={
          <div className="flex flex-col items-center gap-[36px] justify-center">
            <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
              <CloseIcon width={18} height={18} fill="#FF3B30" />
            </div>
            <div>
              <h4 className="text-black text-xl font-bold text-center">
                Are you sure want to delete this Notification?
              </h4>
            </div>
            <div className=" w-full flex justify-center gap-6">
              <Button
                loading={loading}
                text="Delete"
                variant="other"
                className="h-[50px] w-[50px] border border-[#FF0000]
              text-[#FF0000] bg-transparent hover:border-[#FF0000]"
                onClick={() => deleteNotification()}
              />
              <Button
                text="Cancel"
                variant="outline"
                onClick={() => {
                  setOpenDeleteModal(false);
                }}
              />
            </div>
          </div>
        }
        hideCloseButton
        isOpen={openDeleteModal}
        onClose={() => setOpenDeleteModal(false)}
      />
      <Modal
        size="md"
        children={
          <div className="flex flex-col items-center gap-[36px] justify-center">
            <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
              <CloseIcon width={18} height={18} fill="#FF3B30" />
            </div>
            <div>
              <h4 className="text-black text-xl font-bold text-center">
                Are you sure you want to delete all notifications?
              </h4>
              <p className="text-gray-500 text-center mt-2">
                This action cannot be undone.
              </p>
            </div>
            <div className="w-full flex justify-center gap-6">
              <Button
                text="Delete All"
                variant="other"
                className="h-[50px] border border-[#FF0000]
                text-[#FF0000] bg-transparent hover:border-[#FF0000]"
                onClick={() => deleteAllNotifications()}
              />
              <Button
                text="Cancel"
                variant="outline"
                onClick={() => setOpenDeleteAllModal(false)}
              />
            </div>
          </div>
        }
        hideCloseButton
        isOpen={openDeleteAllModal}
        onClose={() => setOpenDeleteAllModal(false)}
      />
    </div>
  );
};

export default Notification;
