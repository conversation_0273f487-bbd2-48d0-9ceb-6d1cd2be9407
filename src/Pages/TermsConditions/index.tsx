import ProfileLayout from '@Components/Layout/ProfileLayout';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import React, { useEffect, useState } from 'react';
import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import parse from 'html-react-parser';
import { Loader } from '@Icons';

const TermsConditions = (): JSX.Element => {
  const { addToast } = useToast();
  const api = new Api();

  const [contentData, setContentData] = useState<{
    title?: string;
    content?: string;
  } | null>(null);
  const [loading, setLoading] = useState<boolean>(true);

  useEffect(() => {
    const fetchTermsConditions = async () => {
      try {
        const { data } = await api.get(API_PATHS.TERMS_AND_CONDITION);
        const termsAndConditionContent = data?.data;

        if (termsAndConditionContent) {
          setContentData(termsAndConditionContent);
        }
      } catch (error: any) {
        addToast('error', error as string);
      } finally {
        setLoading(false);
      }
    };

    fetchTermsConditions();
  }, []);

  return (
    <ProfileLayout>
      <div
        className="reset-styles h-[750px] overflow-y-auto p-4 text-gray-700 leading-relaxed "
        style={{ scrollBehavior: 'smooth' }}
      >
        {loading ? (
          <div className="flex py-6  w-full justify-center items-center">
            <Loader fill="#fff" height={36} width={36} />
          </div>
        ) : contentData?.content ? (
          parse(contentData.content)
        ) : (
          <p className="text-center">No content available</p>
        )}
      </div>
    </ProfileLayout>
  );
};
export default TermsConditions;
