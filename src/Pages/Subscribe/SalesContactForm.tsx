import { But<PERSON>, InputField, InputSelect } from '@Components/UI';
import { PATHS } from '@Config/Path.Config';
import { yupResolver } from '@hookform/resolvers/yup';
import { Controller, useForm } from 'react-hook-form';
import { useNavigate } from 'react-router';
import * as yup from 'yup';

const schema = yup.object().shape({
  firstName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('First name is required')
    .min(3, 'Name must have 3 characters'),
  lastName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Last name is required')
    .min(3, 'Name must have 3 characters'),
  companyName: yup
    .string()
    .trim('Cannot include leading and trailing spaces')
    .strict(true)
    .required('Company name is required'),
  email: yup
    .string()
    .required('Email is required')
    .matches(
      /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
      'Please enter a valid email'
    ),
  orgSize: yup.mixed().required('Size of organization is required'),
  reason: yup.mixed().required('Reason for contacting is required'),
});

interface SalesContactFormValues {
  firstName: string;
  lastName: string;
  email: string;
  companyName: string;
  orgSize: object;
  reason: object;
}

const SalesContactForm = (): JSX.Element => {
  const navigate = useNavigate();

  const {
    control,
    handleSubmit,
    formState: { errors },
  } = useForm<SalesContactFormValues>({
    resolver: yupResolver(schema),
    defaultValues: {},
    mode: 'onChange',
  });

  const onSubmit = async (data: SalesContactFormValues) => {
    navigate(PATHS.HOME);
  };

  return (
    <form
      className="flex flex-col space-y-4 px-2"
      onSubmit={handleSubmit(onSubmit)}
      autoComplete="off"
    >
      <div className="flex gap-x-4">
        <Controller
          name="firstName"
          control={control}
          render={({ field: fieldProps }) => (
            <InputField
              label="First Name"
              field={fieldProps} // ✅ Fix: Explicitly pass 'field' prop
              placeholder="First Name"
              errorMessage={errors.firstName?.message}
              autoFocus
            />
          )}
        />
        <Controller
          name="lastName"
          control={control}
          render={({ field: fieldProps }) => (
            <InputField
              label="Last Name"
              field={fieldProps} // ✅ Fix: Explicitly pass 'field' prop
              placeholder="Last Name"
              errorMessage={errors.lastName?.message}
            />
          )}
        />
      </div>
      <Controller
        name="email"
        control={control}
        render={({ field: fieldProps }) => (
          <InputField
            label="Business Email"
            field={fieldProps} // ✅ Fix: Explicitly pass 'field' prop
            placeholder="Email"
            errorMessage={errors.email?.message}
          />
        )}
      />
      <Controller
        name="companyName"
        control={control}
        render={({ field: fieldProps }) => (
          <InputField
            label="Company Name"
            field={fieldProps} // ✅ Fix: Explicitly pass 'field' prop
            placeholder="Company Name"
            errorMessage={errors.companyName?.message}
          />
        )}
      />

      <Controller
        name="orgSize"
        control={control}
        render={({ field: fieldProps }) => (
          <InputSelect
            label="What is the size of Your organization ? Please select the best applies"
            control={control}
            field={fieldProps} // ✅ Fix: Explicitly pass 'field' prop
            placeholder="Select"
            errorMessage={errors.orgSize?.message}
            options={[
              { value: 'Value 1', label: 'Value 1' },
              { value: 'Value 2', label: 'Value 2' },
            ]}
            menuPlacement="top"
          />
        )}
      />

      <Controller
        name="reason"
        control={control}
        render={({ field: fieldProps }) => (
          <InputSelect
            label="Reason for contacting"
            control={control}
            field={fieldProps} // ✅ Fix: Explicitly pass 'field' prop
            placeholder="Select"
            errorMessage={errors.reason?.message}
            options={[
              { value: 'Value 1', label: 'Value 1' },
              { value: 'Value 2', label: 'Value 2' },
            ]}
            menuPlacement="top"
          />
        )}
      />
      {/* Login Button */}
      <Button text="Post" className="mt-2" type="submit" />
    </form>
  );
};

export default SalesContactForm;
