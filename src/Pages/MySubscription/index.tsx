/* eslint-disable @typescript-eslint/no-explicit-any */
import ContentLayout from '@Components/Layout/ContentLayout';
import ProfileLayout from '@Components/Layout/ProfileLayout';
import { motion } from 'framer-motion';
import useScreenScale from '@Hooks/useScreenScale';
import clsx from 'clsx';
import { CircleTickIcon, CloseIcon, Loader } from '@Icons';
import { Button, Modal } from '@Components/UI';
import { API_PATHS } from '@Helpers/Constants';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import { useEffect, useState } from 'react';
import Api from '@Helpers/Api';
import { useDispatch, useSelector } from 'react-redux';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import { capitalize } from '@Helpers/Utils';
import { setUserData } from '@Redux/SystemControl/UserControle';

dayjs.extend(utc);
dayjs.extend(timezone);
interface Feature {
  id: number;
  name: string;
  display_name: string;
}

interface Plan {
  id: number;
  subscription_name: string;
  subscription_price: number;
  subscription_duration: number;
  no_of_uploads: number;
  features: Feature[];
  price_id: string;
  product_id: string;
}

const MySubscription = (): JSX.Element => {
  const scale = useScreenScale();
  const api = new Api();
  const dispatch = useDispatch();
  const { addToast } = useToast();
  const subscriptionData = useSelector(
    (state: any) => state?.UserControle?.user?.subscription_data
  );
  const userData = useSelector((state: any) => state?.UserControle?.user);

  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [plans, setPlans] = useState<Plan[]>([]);
  const [isCreatingSession, setIsCreatingSession] = useState<string | null>(
    null
  );
  const [isCancelSubWarnOpen, setIsCancelSubWarnOpen] =
    useState<boolean>(false);
  const [isCancelling, setIsCancelling] = useState<boolean>(false);
  const [features, setFeatures] = useState<Feature[]>([]);

  useEffect(() => {
    (async () => {
      if (userData?.role === 'org-admin') {
        try {
          const { data } = await api.get(API_PATHS.FEATURE_LIST, {
            params: {
              is_cms_feature: false,
            },
          });

          if (data?.data?.length) {
            setFeatures([...data.data]);
          }
        } catch (err) {
          console.log('err', err);
        }
      }
    })();
  }, [userData?.role]);

  useEffect(() => {
    (async () => {
      if (!isCancelling)
        try {
          const { data } = await api.get(API_PATHS.USER_PROFILE);
          if (data?.status) {
            dispatch(setUserData({ ...userData, ...data?.data }));
          }
        } catch (err) {
          console.log('err', err);
        }
    })();
  }, [isCancelling]);

  useEffect(() => {
    (async () => {
      if (userData?.role === 'user')
        try {
          setIsLoading(true);
          const response = await api.get(API_PATHS.GET_SUBSCRIPTION_PLAN);
          if (response?.data?.data?.length) {
            setPlans([...(response?.data?.data as Plan[])]);
          }
          if (!response.data?.status)
            addToast('error', response?.data?.message);
        } catch (err) {
          addToast('error', err as string);
        } finally {
          setIsLoading(false);
        }
    })();
  }, []);

  const getSessionForPlan = async (priceId: string) => {
    try {
      setIsCreatingSession(priceId);
      const sessionResponse = await api.post(API_PATHS.CREATE_STRIPE_SESSION, {
        data: {
          price_id: priceId,
          is_update: true,
        },
      });
      if (sessionResponse?.data?.data?.url) {
        window.location.href = sessionResponse?.data?.data?.url;
      }
      if (!sessionResponse?.data?.status) {
        addToast('error', sessionResponse?.data?.message as string);
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsCreatingSession(null);
    }
  };

  const cancelSubscription = async () => {
    try {
      setIsCancelling(true);
      const cancelResponse = await api.post(API_PATHS.CANCEL_SUBSCRIPTION);
      if (cancelResponse?.data?.status) {
        addToast(
          cancelResponse?.data?.status ? 'success' : 'error',
          cancelResponse?.data?.message
        );
      }
    } catch (err) {
      addToast('error', err as string);
    } finally {
      setIsCancelling(false);
      setIsCancelSubWarnOpen(false);
    }
  };

  return (
    <ProfileLayout>
      <Modal isOpen={isCancelSubWarnOpen} hideCloseButton>
        <div className="flex flex-col gap-y-8 p-3">
          <div className="text-center font-semibold text-lg text-black">
            Are you sure you want to cancel your subscription?
          </div>
          <div className="flex w-full gap-6">
            <Button
              loading={isCancelling}
              text="Yes"
              variant="other"
              className="h-[50px] w-[50px] border border-[#FF0000]
              text-[#FF0000] bg-transparent hover:border-[#FF0000]"
              onClick={cancelSubscription}
              disabled={isCancelling}
            />
            <Button
              text="No"
              variant="outline"
              disabled={isCancelling}
              onClick={() => {
                setIsCancelSubWarnOpen(false);
              }}
            />
          </div>
        </div>
      </Modal>
      <ContentLayout title="My Subscription">
        <div className="flex flex-1 flex-col">
          {userData?.is_subscribed && (
            <motion.div
              whileHover={{ scale: 1.02 }}
              className="flex mb-8 rounded-lg shadow-primary py-4 px-6 text-lg"
            >
              <div className="grid grid-cols-2 gap-y-6 gap-x-16 w-full text-secondary">
                {userData?.role === 'org-admin' && (
                  <div className="flex-1 flex justify-between items-center py-2.5 border-b border-gray-300">
                    <div className="font-medium">Price</div>
                    <div className="font-bold text-gray-800">
                      {subscriptionData?.price ?? '-'}
                    </div>
                  </div>
                )}
                {userData?.role === 'user' && (
                  <div className="flex-1 flex justify-between items-center text-lg py-2.5 border-b border-gray-300">
                    <div className="font-medium">Subscription Level</div>
                    <div className="font-bold text-gray-800">
                      {subscriptionData?.subscription_plan}, Month
                    </div>
                  </div>
                )}
                <div className="flex-1 flex justify-between items-center py-2.5 border-b border-gray-300">
                  <div className="font-medium">Subscription Since</div>
                  <div className="font-bold text-gray-800">
                    {dayjs
                      .utc(subscriptionData?.start_date)
                      .local()
                      .format('MMM DD, YYYY')}
                  </div>
                </div>
                <div className="flex-1 flex justify-between items-center py-2.5 border-b border-gray-300">
                  <div className="font-medium">Need Renewal</div>
                  <div className="font-bold text-gray-800">
                    {dayjs
                      .utc(subscriptionData?.expire_date)
                      .local()
                      .format('MMM DD, YYYY')}
                  </div>
                </div>
                {userData?.role === 'org-admin' && (
                  <div className="flex-1 flex justify-between items-center py-2.5 border-b border-gray-300">
                    <div className="font-medium">Allowed number of uploads</div>
                    <div className="font-bold text-gray-800">
                      {subscriptionData?.allowed_number_of_uploads ?? '-'}
                    </div>
                  </div>
                )}
                {userData?.role === 'user' && (
                  <div className="flex-1 flex justify-between items-center py-2.5 border-b border-gray-300">
                    <div className="font-medium">Purchased from</div>
                    <div className="font-bold text-gray-800">
                      {subscriptionData?.subscription_configurations?.length &&
                      subscriptionData?.subscription_configurations[0]?.platform
                        ? capitalize(
                            subscriptionData?.subscription_configurations[0]
                              ?.platform
                          )
                        : '-'}
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          )}
          {userData?.role === 'org-admin' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4 }}
              className="lg:col-span-2 w-full"
            >
              <motion.div
                whileHover={{ scale: 1.02 }}
                className="shadow-primary p-6 rounded-xl border border-gray-200 bg-white h-full"
              >
                <h3 className="text-lg font-semibold mb-4 text-secondary">
                  Module Access
                </h3>
                <div className="grid grid-cols-2 gap-4">
                  {features?.map((i: Feature) => {
                    const isGranted = subscriptionData?.features?.find(
                      (el: Feature) => el?.name === i?.name
                    );
                    //rolePermissions?.includes(i.id);
                    return (
                      <div
                        key={i.id}
                        className={`flex items-center justify-between px-4 py-2 rounded-lg select-none transition-all ${
                          isGranted
                            ? 'bg-[#F3B377] text-primary-100 border border-primary-0'
                            : 'bg-red-50 text-red-400 border border-red-500'
                        }`}
                      >
                        <p className="text-md font-medium">{i.display_name}</p>
                        {isGranted ? (
                          <CircleTickIcon
                            height={20}
                            width={20}
                            fill="#4CAF50"
                          />
                        ) : (
                          <CloseIcon
                            height={14}
                            width={14}
                            fill="#f2584d"
                            className="mr-[2px]"
                          />
                        )}
                      </div>
                    );
                  })}
                </div>
              </motion.div>
            </motion.div>
          )}
          {isLoading ? (
            <div className="flex flex-1 h-full w-full items-center justify-center">
              <Loader height={32} width={32} fill="#fff" />
            </div>
          ) : (
            userData?.role === 'user' && (
              <div
                className={clsx(
                  'grid grid-cols-2 lg:grid-cols-3 w-full px-0',
                  scale <= 100 ? 'gap-6' : 'gap-4'
                )}
              >
                {plans.map((plan: Plan) => (
                  <motion.div
                    key={plan?.id}
                    className="group border border-b-primary rounded-lg p-6 bg:white shadow-primary flex flex-1 flex-col text-left text-black hover:text-white cursor-pointer"
                    whileHover={{
                      scale: 1.05,
                      boxShadow: '0px 10px 20px rgba(0, 0, 0, 0.1)',
                      backgroundColor: 'var(--color-primary-100)',
                    }}
                    transition={{ duration: 0.3, ease: 'easeInOut' }}
                  >
                    <div className="font-semibold text-2xl">
                      {plan?.id === subscriptionData?.subscription_plan_id && (
                        <span className="font-extrabold">Active - </span>
                      )}
                      {plan?.subscription_name}
                    </div>
                    <div
                      className={clsx(
                        'font-semibold text-primary-100 group-hover:text-white',
                        scale <= 100 ? 'py-8 text-2xl' : 'py-3 text-xl'
                      )}
                    >
                      <span
                        className={clsx(scale <= 100 ? 'text-4xl' : 'text-xl')}
                      >
                        ${plan?.subscription_price}
                      </span>
                      /month
                    </div>
                    <div
                      key={plan?.no_of_uploads}
                      className={clsx(
                        'flex items-center',
                        scale <= 100 ? 'pb-2' : 'pb-1 text-xs'
                      )}
                    >
                      <div className="group-hover:fill-black pr-2 flex  h-full">
                        <div className="flex items-start  justify-center h-full pt-[4px] pb-1">
                          <CircleTickIcon
                            height={15}
                            width={15}
                            fill="currentColor"
                          />
                        </div>
                      </div>
                      <div className="font-bold h-full items-start justify-start">
                        {plan?.no_of_uploads} Reports per Month
                      </div>
                    </div>
                    {plan?.features.map((benefit: Feature) => (
                      <div
                        key={benefit.id}
                        className={clsx(
                          'flex items-center',
                          scale <= 100 ? 'pb-2' : 'pb-1 text-xs'
                        )}
                      >
                        <div className="group-hover:fill-black pr-2 flex  h-full">
                          <div className="flex items-start  justify-center flex-1 h-full pt-[4px] pb-1">
                            <CircleTickIcon
                              height={15}
                              width={15}
                              fill="currentColor"
                            />
                          </div>
                        </div>
                        <div className="font-normal h-full items-start justify-start">
                          Access to {benefit?.display_name}
                        </div>
                      </div>
                    ))}
                    <div className="flex flex-1 justify-center items-end">
                      <Button
                        text={
                          !userData?.is_subscribed
                            ? 'Subscribe now'
                            : subscriptionData?.subscription_plan_id ===
                                plan?.id
                              ? userData?.is_subscription_cancel
                                ? 'Cancelled'
                                : 'Cancel Subscription'
                              : subscriptionData?.subscription_plan_id >
                                  plan?.id
                                ? 'Update Plan'
                                : 'Upgrade Plan'
                        }
                        className={clsx(
                          'mt-6 ',
                          subscriptionData?.subscription_plan_id === plan?.id
                            ? 'bg-red-400 group-hover:!bg-red-400 text-white'
                            : 'bg-primary-100 text-white group-hover:!bg-white group-hover:!text-primary-100'
                        )}
                        disabled={
                          !!isCreatingSession ||
                          (!!userData?.is_subscription_cancel &&
                            subscriptionData?.subscription_plan_id === plan?.id)
                        }
                        loading={isCreatingSession === plan?.price_id}
                        variant="other"
                        onClick={() => {
                          if (
                            subscriptionData?.subscription_plan_id !== plan?.id
                          ) {
                            getSessionForPlan(plan.price_id);
                          } else {
                            setIsCancelSubWarnOpen(true);
                          }
                        }}
                      />
                    </div>
                  </motion.div>
                ))}
              </div>
            )
          )}
        </div>
      </ContentLayout>
    </ProfileLayout>
  );
};

export default MySubscription;
