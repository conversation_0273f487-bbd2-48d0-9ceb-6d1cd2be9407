import React, { useState } from 'react';
import { InputSelect } from '@Components/UI';
import { Loader } from '@Icons';
import { useNavigate, useParams } from 'react-router';
import {
  useGetCategories,
  useGetCategoryVideos,
} from '@Query/Hooks/useTraining';
import { VideoCard } from './VideoCard';
import { useToast } from '@Components/UI/Toast/ToastProvider';

interface CategoryOption {
  value: string;
  label: string;
}

interface CategoryData {
  id: number;
  category_name: string;
  description: string;
  created_at: string;
  updated_at: string;
  is_active: boolean;
  video_count: number;
}

const ViewAllVideos: React.FC = () => {
  const navigate = useNavigate();
  const { addToast } = useToast();
  const { categoryId } = useParams<{
    categoryId: string;
  }>();
  const [filter, setFilter] = useState<string | null>(categoryId ?? null);
  const [selectedCategory, setSelectedCategory] = useState<
    CategoryOption | 'all'
  >('all');
  const [categoryOptions, setCategoryOptions] = useState<CategoryOption[]>([]);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const { isLoading: isCategoriesLoading } = useGetCategories({
    onSuccess: (response) => {
      const categories = response.data.data.list;
      const options: CategoryOption[] = categories.map(
        (category: CategoryData) => ({
          value: category.id,
          label: category.category_name,
        })
      );
      setCategoryOptions([{ value: 'all', label: 'All' }, ...options]);

      if (categoryId) {
        const matchingCategory = options.find(
          (option) => option.value.toString() === categoryId
        );
        if (matchingCategory) {
          setSelectedCategory(matchingCategory);
        }
      }
    },
    onError: (error) => {
      addToast('error', error as string);
    },
  });

  const {
    data: videosResponse,
    isLoading: isVideosLoading,
    isFetching: videoListFetching,
  } = useGetCategoryVideos(filter, currentPage, {
    enabled:
      !isCategoriesLoading ||
      selectedCategory !== null ||
      categoryId !== undefined,
    onSuccess: () => {},
    onError: (error) => {
      addToast('error', error as string);
    },
  });

  const handleCategoryChange = (option: any) => {
    if (option?.value) {
      setFilter(option.value);
    } else {
      setFilter('all');
    }
    setSelectedCategory(option);
    setCurrentPage(1);
  };

  const handleVideoClick = (videoId: number) => {
    navigate(`/video/${videoId}`);
  };

  const handleNextPage = () => {
    if (videosResponse && currentPage < videosResponse.data.data.total_pages) {
      setCurrentPage(currentPage + 1);
    }
  };

  const handlePrevPage = () => {
    if (currentPage > 1) {
      setCurrentPage(currentPage - 1);
    }
  };

  const getCategoryName = () => {
    // if (catName) {
    //   return catName;
    // }
    // if (selectedCategory) {
    //   return selectedCategory.label;
    // }
    return 'Training & Education';
  };

  const videos = videosResponse?.data.data.list || [];
  const totalPages = videosResponse?.data.data.total_pages || 1;

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10 pb-0">
        <h1 className="font-roboto font-semibold text-[32px]">
          {getCategoryName()}
        </h1>

        <div className="w-[200px] mr-0 xl:mr-4 2xl:mr-6">
          <InputSelect
            label={true}
            placeholder="Filter by Category"
            options={categoryOptions}
            // isClearable={true}
            field={{
              value: selectedCategory,
              onBlur: () => {},
              onChange: handleCategoryChange,
            }}
            isDisabled={isCategoriesLoading}
          />
        </div>
      </div>

      <div className="h-dvh p-10 pt-8">
        {isCategoriesLoading || isVideosLoading || videoListFetching ? (
          <div className="w-full h-full flex flex-col items-center justify-center">
            <Loader height={40} width={40} fill="#fff" />
            {/* <br />
            Loading... */}
          </div>
        ) : (
          <>
            <div className="pb-8">
              <div className="flex gap-x-6 gap-y-8 flex-wrap">
                {videos.length > 0 ? (
                  videos.map((video: any) => (
                    <VideoCard
                      key={video.id}
                      video={video}
                      onClick={() => handleVideoClick(video.id)}
                      isViewAll={true}
                    />
                  ))
                ) : (
                  <p className="text-gray-500 w-full text-center py-8">
                    No videos found
                  </p>
                )}
              </div>
            </div>

            {videos.length > 0 && totalPages > 1 && (
              <div className="flex items-center justify-between pb-4">
                <button
                  onClick={handlePrevPage}
                  disabled={currentPage === 1}
                  className={`
        flex items-center justify-center
        space-x-2
        py-3 px-4
        text-md font-semibold
        rounded-full
        transition
        ${
          currentPage === 1
            ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
            : 'bg-[#FF8800] text-white hover:bg-[#FFA033] hover:cursor-pointer'
        }
      `}
                >
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M15 19l-7-7 7-7"
                    />
                  </svg>
                  <span className="leading-none">Previous</span>
                </button>

                {/* Page indicator */}
                <div className="text-lg text-gray-700 font-medium px-4">
                  Page {currentPage} of {totalPages}
                </div>

                {/* Next on the right */}
                <button
                  onClick={handleNextPage}
                  disabled={currentPage === totalPages}
                  className={`
        flex items-center justify-center
        space-x-2
        py-3 px-4
        text-md font-semibold
        rounded-full
        transition
        ${
          currentPage === totalPages
            ? 'bg-gray-200 text-gray-400 cursor-not-allowed'
            : 'bg-[#FF8800] text-white hover:bg-[#FFA033] hover:cursor-pointer'
        }
      `}
                >
                  <span className="leading-none">Next</span>
                  <svg
                    className="h-6 w-6"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M9 5l7 7-7 7"
                    />
                  </svg>
                </button>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ViewAllVideos;
