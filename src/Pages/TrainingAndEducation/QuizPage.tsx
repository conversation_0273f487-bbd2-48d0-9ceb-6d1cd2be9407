import React, { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import QuestionsList from './QuestionsList';
import QuestionDisplay from './QuestionDisplay';
import ProgressBar from './ProgressBar';
import { PATHS } from '@Config/Path.Config';
import { BackIcon, Loader } from '@Icons';
import {
  useGetQuizAnswers,
  useGetQuizDetail,
  useSubmitQuiz,
} from '@Query/Hooks/useTraining';
import { useToast } from '@Components/UI/Toast/ToastProvider';

interface LocationState {
  videoId?: string | number;
}

interface AnswerItem {
  question_id: number;
  correct_answers: number[];
}

export const QuizPage: React.FC = () => {
  const navigate = useNavigate();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState<number>(0);
  const { addToast } = useToast();
  const [userAnswers, setUserAnswers] = useState<Record<number, number[]>>({});
  const location = useLocation();
  const { videoId } = (location.state as LocationState) || {};
  const [tempAnswers, setTempAnswers] = useState<Record<number, number[]>>({});
  const [answersData, setAnswersData] = useState<any>({});
  const [resultData, setResultData] = useState<{
    score: number;
    totalScore: number;
    passed: boolean;
    title: string;
    description: string;
  }>({
    score: 0,
    totalScore: 0,
    passed: false,
    title: '',
    description: '',
  });
  const [submittedQuestions, setSubmittedQuestions] = useState<number[]>([]);

  const {
    data: quizData,
    isLoading: isQuizLoading,
    isFetching: isQuizFetching,
    isError: isQuizError,
  } = useGetQuizDetail(Number(videoId), {
    onSuccess: (response) => {},
    onError: (error: any) => {
      addToast('error', error);
    },
  });

  const {
    isLoading: isAnswersLoading,
    isError: isAnswersError,
    isFetching: isAnswerFetching,
  } = useGetQuizAnswers(Number(quizData?.data?.data?.id), {
    enabled: !!quizData?.data?.data?.id,
    onSuccess: (response) => {
      if (response.data.data.answers) {
        const decodedAnswers: AnswerItem[] = JSON.parse(
          atob(response.data.data.answers!)
        );
        setAnswersData({ data: { ...response.data.data, decodedAnswers } });
      } else {
        setAnswersData({});
      }
    },
    onError: (error: any) => {
      addToast('error', error);
    },
  });

  const {
    data: submitQuizData,
    mutate: submitQuiz,
    isLoading: isSubmitQuizLoading,
  } = useSubmitQuiz({
    onSuccess: (data) => {},
    onError: (error: string) => {
      setResultData({
        score: 0,
        totalScore: 0,
        passed: false,
        title: '',
        description: '',
      });
      addToast('error', error);
    },
  });

  const isLoading =
    isQuizLoading || isAnswersLoading || isAnswerFetching || isQuizFetching;
  const isError = isQuizError || isAnswersError;

  useEffect(() => {
    if (submitQuizData) {
      navigate(PATHS.RESULT, {
        state: {
          ...resultData,
          certificate: submitQuizData?.data?.data?.certificate?.certificate_url,
        },
      });
    }
  }, [submitQuizData]);

  if (isLoading) {
    return (
      <div className="w-full h-full flex flex-col items-center justify-center">
        <Loader height={40} width={40} fill="#fff" />
      </div>
    );
  }

  const handleBack = () => {
    navigate(-1);
  };

  if (isError || isAnswersError || !quizData?.data?.data?.questions?.length) {
    return (
      <div className="flex flex-col w-full p-6 h-screen overflow-hidden">
        <div className="flex items-center mb-6">
          <button
            onClick={handleBack}
            className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 mr-4 hover:cursor-pointer"
          >
            <BackIcon />
          </button>
          <h1 className="font-roboto font-semibold text-2xl">
            Training & Education
          </h1>
        </div>
        <div className="text-xl font-semibold w-full h-full flex items-center justify-center">
          No quiz found.
        </div>
      </div>
    );
  }

  const questions = quizData.data.data.questions;
  const totalQuestions = questions.length;
  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === totalQuestions - 1;

  const handleOptionSelect = (optionId: number) => {
    const questionType = currentQuestion.question_type;
    const questionId = currentQuestion.id;

    if (questionType === 'MCQ') {
      setTempAnswers({
        ...tempAnswers,
        [questionId]: [optionId],
      });
    } else if (questionType === 'MSQ') {
      const currentSelections = tempAnswers[questionId] || [];
      if (currentSelections.includes(optionId)) {
        setTempAnswers({
          ...tempAnswers,
          [questionId]: currentSelections.filter((id) => id !== optionId),
        });
      } else {
        setTempAnswers({
          ...tempAnswers,
          [questionId]: [...currentSelections, optionId],
        });
      }
    }
  };

  const handleQuestionClick = (index: number) => {
    if (tempAnswers[currentQuestion.id]) {
      setUserAnswers({
        ...userAnswers,
        [currentQuestion.id]: tempAnswers[currentQuestion.id],
      });
    }

    setCurrentQuestionIndex(index);

    const targetQuestionId = questions[index].id;
    if (userAnswers[targetQuestionId]) {
      setTempAnswers({
        ...tempAnswers,
        [targetQuestionId]: [...userAnswers[targetQuestionId]],
      });
    }
  };

  const handleBackClick = () => {
    if (tempAnswers[currentQuestion.id]) {
      setUserAnswers({
        ...userAnswers,
        [currentQuestion.id]: tempAnswers[currentQuestion.id],
      });
    }

    if (currentQuestionIndex > 0) {
      const prevIndex = currentQuestionIndex - 1;
      setCurrentQuestionIndex(prevIndex);

      const prevQuestionId = questions[prevIndex].id;
      if (userAnswers[prevQuestionId]) {
        setTempAnswers({
          ...tempAnswers,
          [prevQuestionId]: [...userAnswers[prevQuestionId]],
        });
      }
    }
  };

  const handleNextClick = () => {
    const questionId = currentQuestion.id;
    const isCurrentQuestionAnswered =
      !submittedQuestions.includes(currentQuestionIndex);
    const userAnsweredCurrentQuestion = tempAnswers[questionId];

    if (userAnsweredCurrentQuestion) {
      setUserAnswers({
        ...userAnswers,
        [questionId]: tempAnswers[questionId],
      });
    }

    if (isCurrentQuestionAnswered) {
      setSubmittedQuestions([...submittedQuestions, currentQuestionIndex]);
    }

    if (currentQuestionIndex < totalQuestions - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);

      const nextQuestionId = questions[nextIndex].id;
      if (userAnswers[nextQuestionId]) {
        setTempAnswers({
          ...tempAnswers,
          [nextQuestionId]: [...userAnswers[nextQuestionId]],
        });
      } else {
        const nextTempAnswers = { ...tempAnswers };
        delete nextTempAnswers[nextQuestionId];
        setTempAnswers(nextTempAnswers);
      }
    } else {
      finishQuiz(
        isCurrentQuestionAnswered
          ? [...submittedQuestions, currentQuestionIndex]
          : submittedQuestions,
        userAnsweredCurrentQuestion
          ? {
              ...userAnswers,
              [questionId]: tempAnswers[questionId],
            }
          : userAnswers
      );
    }
  };

  const handleSkipClick = () => {
    const isCurrentQuestionAnswered =
      !submittedQuestions.includes(currentQuestionIndex);

    if (isCurrentQuestionAnswered) {
      setSubmittedQuestions([...submittedQuestions, currentQuestionIndex]);
    }

    if (currentQuestionIndex < totalQuestions - 1) {
      const nextIndex = currentQuestionIndex + 1;
      setCurrentQuestionIndex(nextIndex);

      const nextQuestionId = questions[nextIndex].id;
      if (userAnswers[nextQuestionId]) {
        setTempAnswers({
          ...tempAnswers,
          [nextQuestionId]: [...userAnswers[nextQuestionId]],
        });
      } else {
        const nextTempAnswers = { ...tempAnswers };
        delete nextTempAnswers[nextQuestionId];
        setTempAnswers(nextTempAnswers);
      }
    } else {
      finishQuiz(
        isCurrentQuestionAnswered
          ? [...submittedQuestions, currentQuestionIndex]
          : submittedQuestions
      );
    }
  };

  const finishQuiz = (
    submittedQuestions: number[],
    userGivenAnswers: { [key: number]: number[] } = userAnswers
  ) => {
    const correctAnswersMap: Record<number, number[]> =
      answersData.data.decodedAnswers.reduce((acc: any, item: any) => {
        acc[item.question_id] = item.correct_answers;
        return acc;
      }, {});

    let score = 0;

    submittedQuestions.forEach((questionIndex) => {
      const question = questions[questionIndex];
      const questionId = question.id;
      const userSelections = userGivenAnswers[questionId] || [];

      const correctSelections = correctAnswersMap[questionId] || [];

      const isCorrect =
        userSelections.length === correctSelections.length &&
        userSelections.every((selection) =>
          correctSelections.includes(selection)
        );

      if (isCorrect) {
        score += 1;
      }
    });

    const passingScore = quizData.data.data.passing_score;
    const totalScore = quizData.data.data.total_score;
    const passed = score >= passingScore;

    submitQuiz({
      quiz_id: quizData?.data?.data?.id,
      marks_obtained: score,
      pass_status: passed,
      total_score: totalScore,
      passing_score: passingScore,
      total_questions: totalQuestions,
    });
    setResultData({
      score,
      totalScore,
      passed,
      title: passed ? 'Congratulations' : 'Oops!',
      description: passed
        ? 'Passing this test is a major milestone. Well done on your achievement.'
        : 'You failed. Give test again to get your certificate!',
    });
  };

  const hasCurrentSelection =
    tempAnswers[currentQuestion.id] &&
    tempAnswers[currentQuestion.id].length > 0;
  // const currentQuestionSubmitted =
  submittedQuestions.includes(currentQuestionIndex);

  const currentOptions =
    tempAnswers[currentQuestion.id] || userAnswers[currentQuestion.id] || [];

  const buttonLabel = isLastQuestion
    ? 'Finish'
    : hasCurrentSelection
      ? 'Next'
      : 'Skip';
  const onButtonClick = hasCurrentSelection ? handleNextClick : handleSkipClick;

  return (
    <div className="flex flex-col w-full p-6 h-screen overflow-hidden">
      <div className="flex items-center mb-6">
        <button
          onClick={handleBack}
          className="flex items-center justify-center w-10 h-10 rounded-full hover:bg-gray-100 mr-4 hover:cursor-pointer"
        >
          <BackIcon />
        </button>
        <h1 className="font-roboto font-semibold text-2xl">
          Training & Education
        </h1>
      </div>
      <div className="w-full flex overflow-y-auto h-full">
        <div className="w-[30%] px-4 p-1 pr-4">
          <QuestionsList
            questions={questions}
            currentQuestionIndex={currentQuestionIndex}
            userAnswers={userAnswers}
            submittedQuestions={submittedQuestions}
            onQuestionClick={handleQuestionClick}
          />
        </div>

        <div className="w-[70%] flex flex-col">
          <div
            className="flex-grow overflow-y-auto bg-white rounded-lg p-4 m-2"
            style={{ boxShadow: '0 0 10px rgba(0,0,0,0.1)' }}
          >
            <ProgressBar
              submittedCount={submittedQuestions.length}
              totalQuestions={totalQuestions}
            />
            {/* <div className="h-96 overflow-y-auto p-6"> */}
            <QuestionDisplay
              question={currentQuestion}
              selectedOptions={currentOptions}
              onOptionSelect={handleOptionSelect}
              currentQuestionIndex={currentQuestionIndex}
            />
            {/* </div> */}
          </div>

          <div className="mt-6 flex justify-between">
            <button
              onClick={handleBackClick}
              disabled={currentQuestionIndex === 0}
              className={`w-[144px] h-[50px] gap-1.5 py-3 px-[50px] rounded-[10px] border  ${
                currentQuestionIndex === 0
                  ? 'bg-gray-100 cursor-not-allowed text-gray-700 border-gray-400'
                  : 'bg-white text-black hover:bg-gray-100 border-[#FFA033] cursor-pointer'
              }`}
            >
              Back
            </button>

            <button
              disabled={isSubmitQuizLoading}
              onClick={onButtonClick}
              className={`w-[144px] h-[50px] gap-1.5 py-3 px-[50px] rounded-[10px] border text-[#FFA033] bg-white hover:bg-gray-100 border-[#FFA033] cursor-pointer`}
            >
              {isSubmitQuizLoading ? (
                <div className="w-full h-full flex flex-col items-center justify-center">
                  <Loader height={20} width={20} fill="#FFA033" />
                </div>
              ) : (
                buttonLabel
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuizPage;
