import React from 'react';

interface Props {
  submittedCount: number;
  totalQuestions: number;
}

const ProgressBar: React.FC<Props> = ({ submittedCount, totalQuestions }) => {
  const progressPercentage = (submittedCount / totalQuestions) * 100;

  return (
    <div className="w-full bg-[#F1F1F1] p-4 rounded-lg mb-6">
      <div className="flex items-center">
        <span className="font-roboto font-bold text-sm tracking-normal align-middle mr-6 text-[#333333]">
          {submittedCount}/{totalQuestions}
        </span>
        <div className="flex-grow bg-[#D9D9D9] rounded-[10px] h-[10px]">
          <div
            className="bg-[#19E95B] h-[10px] rounded-full"
            style={{ width: `${progressPercentage}%` }}
          ></div>
        </div>
      </div>
    </div>
  );
};

export default ProgressBar;
