import { useState, useRef, useEffect } from 'react';
import { motion } from 'framer-motion';
import clsx from 'clsx';

interface TooltipProps {
  content: string;
  children: React.ReactNode;
  className?: string;
}

const Tooltip: React.FC<TooltipProps> = ({ content, children, className }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [position, setPosition] = useState('top');
  const tooltipRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (!tooltipRef.current) return;

    const tooltip = tooltipRef.current;
    const { top, bottom, left, right } = tooltip.getBoundingClientRect();
    const { innerWidth, innerHeight } = window;

    let newPosition = 'top';
    if (top < 50) newPosition = 'bottom';
    else if (bottom > innerHeight - 50) newPosition = 'top';
    else if (left < 50) newPosition = 'right';
    else if (right > innerWidth - 50) newPosition = 'left';

    setPosition(newPosition);
  }, [isVisible]);

  return (
    <div
      className="relative flex items-center"
      onMouseEnter={() => setIsVisible(true)}
      onMouseLeave={() => setIsVisible(false)}
    >
      {children}
      {isVisible && (
        <motion.div
          ref={tooltipRef}
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.2 }}
          className={clsx(
            'absolute px-3 py-1 text-sm text-black bg-gradient-to-l from-primary-100 to-[#FF8800] rounded-lg shadow-lg whitespace-nowrap',
            {
              'top-[-20px] left-1/2 transform -translate-x-1/2':
                position === 'top',
              'bottom-[-40px] left-1/2 transform -translate-x-1/2':
                position === 'bottom',
              'left-[-110%] top-1/2 transform -translate-y-1/2':
                position === 'left',
              'right-[-10%] top-1/2 transform -translate-y-1/2':
                position === 'right',
            },
            className
          )}
        >
          {content}
        </motion.div>
      )}
    </div>
  );
};

export default Tooltip;
