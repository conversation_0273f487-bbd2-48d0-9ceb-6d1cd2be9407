import { CaretRightIcon } from '@Icons';
import { Link } from 'react-router';

const Breadcrumb = ({
  items,
}: {
  items: { label: string; link?: string }[];
}) => {
  return (
    <nav className="text-gray-600 text-sm" aria-label="breadcrumb">
      <ul className="flex items-center space-x-2">
        {items.map((item, index) => (
          <li key={index} className="flex items-center">
            {item.link ? (
              <Link
                to={item.link}
                className="text-primary-100 hover:text-primary-100 transition-colors"
              >
                {item.label}
              </Link>
            ) : (
              <span className="text-gray-500">{item.label}</span>
            )}
            {index < items.length - 1 && (
              <CaretRightIcon className="h-4 w-4 mt-1 ml-5 text-gray-400 mx-1" />
            )}
          </li>
        ))}
      </ul>
    </nav>
  );
};

export default Breadcrumb;
