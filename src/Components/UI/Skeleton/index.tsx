import { motion } from 'framer-motion';

const Skeleton = ({ className = '' }: { className?: string }) => {
  return (
    <div
      className={`relative overflow-hidden rounded-md bg-gray-200 ${className}`}
    >
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/60 to-transparent"
        initial={{ x: '-100%' }}
        animate={{ x: '100%' }}
        transition={{ repeat: Infinity, duration: 1.5, ease: 'easeInOut' }}
      />
    </div>
  );
};

export default Skeleton;
