import { InfoIcon, ErrorIcon } from '@Icons';
import clsx from 'clsx';
import React from 'react';
import { ControllerRenderProps } from 'react-hook-form';

interface InputFieldProps
  extends React.InputHTMLAttributes<HTMLInputElement | HTMLTextAreaElement> {
  type?: string;
  label?: string | boolean;
  placeholder?: string;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  field?: ControllerRenderProps<any, any> | null; // Proper typing for react-hook-form field
  errorMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  infoMsg?: string;
  containerClassName?: string;
  inputContainerClassName?: string;
  inputClassName?: string;
  labelColor?: string;
  disabled?: boolean;
  showErrorIcon?: boolean;
  rows?: number;
  commentHeight?: string;
  isComment?: boolean;
}

const InputField: React.FC<InputFieldProps> = ({
  type = 'text',
  label,
  placeholder,
  field,
  errorMessage,
  leftIcon,
  rightIcon,
  infoMsg = '',
  containerClassName = 'pt-0',
  inputContainerClassName = 'max-h-[46px]', 
  inputClassName = '',
  labelColor = 'text-gray-700',
  disabled = false,
  showErrorIcon = true,
  rows = 4,
  commentHeight = 'min-h-18 !py-2.5 max-h-64',
  isComment = false,
  ...rest
}) => {
  const isTextarea = type === 'textarea';

  const adjustedInputContainerClassName = isTextarea
    ? clsx(
        inputContainerClassName,
        isComment ? commentHeight : 'min-h-[140px] h-auto'
      )
    : inputContainerClassName;

  return (
    <div
      className={clsx(
        'flex flex-col gap-1.5 w-full relative select-none',
        containerClassName
      )}
    >
      {/* Label & Info Icon */}
      {(label || infoMsg) && (
        <div className="flex items-center">
          {label && (
            <label
              className={clsx('text-sm font-medium text-left', labelColor)}
            >
              {label}
            </label>
          )}
          {!!infoMsg && (
            <div className="relative group flex cursor-pointer pl-1.5">
              <InfoIcon height={16} width={16} />
              <div className="absolute left-0 mb-1 bottom-full p-2 rounded-xl min-w-max border border-[#E4E7EC] bg-white text-secondary-color-text text-xs opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none">
                {infoMsg}
              </div>
            </div>
          )}
        </div>
      )}

      <div
        className={clsx(
          'flex items-center flex-1 py-6 px-4 border rounded-[10px] border-b-primary focus-within:ring-1 focus-within:ring-teal-100 focus-within:shadow-primary',
          adjustedInputContainerClassName,
          disabled
            ? 'bg-[#e5e7eb] cursor-not-allowed focus-within:border-b-primary hover:border-b-primary'
            : 'bg-white hover:border-primary-100 focus-within:border-primary-100',
          errorMessage &&
            'border-red-500 focus-within:border-red-500 focus-within:shadow-red-200 shadow-red-200 hover:border-red-500'
        )}
      >
        {leftIcon && <div className="flex pr-2">{leftIcon}</div>}

        {isTextarea ? (
          <textarea
            {...field}
            {...rest}
            placeholder={placeholder}
            disabled={disabled}
            className={clsx(
              'w-full h-full flex-1 pt-0 pl-0 border-none overflow-y-auto outline-none appearance-none disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-base resize-none custom-scrollbar',
              inputClassName
            )}
            rows={rows}
          />
        ) : (
          <input
            {...field}
            {...rest}
            type={type}
            placeholder={placeholder}
            disabled={disabled}
            className={clsx(
              'w-full flex-1 p-1.5 border-none outline-none appearance-none disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-base',
              inputClassName
            )}
          />
        )}

        {rightIcon && <div className="flex items-center">{rightIcon}</div>}
        {errorMessage && !rightIcon && showErrorIcon && <ErrorIcon />}
      </div>

      {/* Error Message */}
      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}
    </div>
  );
};

export default InputField;
