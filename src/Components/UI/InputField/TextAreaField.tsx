import { InfoIcon, ErrorIcon, Loader } from '@Icons';
import clsx from 'clsx';
import React from 'react';
import { ControllerRenderProps } from 'react-hook-form';
import Tooltip from '../Tooltip';

interface TextAreaFieldProps
  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  label?: string | boolean;
  placeholder?: string;
  variant?: string;
  field?: ControllerRenderProps<any, any>;
  errorMessage?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  infoMsg?: string;
  containerClassName?: string;
  textareaContainerClassName?: string;
  textareaClassName?: string;
  labelColor?: string;
  disabled?: boolean;
  loading?: boolean;
  showErrorIcon?: boolean;
  rows?: number;
  minHeight?: string;
  maxHeight?: string;
}

const TextAreaField: React.FC<TextAreaFieldProps> = ({
  label,
  placeholder,
  field,
  errorMessage,
  leftIcon,
  rightIcon,
  infoMsg = '',
  containerClassName = 'pt-2',
  textareaContainerClassName = '',
  textareaClassName = '',
  labelColor = 'text-gray-700',
  disabled = false,
  showErrorIcon = true,
  loading = false,
  rows = 3,
  minHeight = '46px',
  maxHeight = '',
  ...rest
}) => {
  return (
    <div
      className={clsx(
        'flex flex-col gap-1.5 w-full relative select-none',
        containerClassName
      )}
    >
      {/* Label & Info Icon */}
      {(label || infoMsg) && (
        <div className="flex items-center">
          {label && (
            <label
              className={clsx('text-sm font-medium text-left', labelColor)}
            >
              {label}
            </label>
          )}
          {!!infoMsg && (
            <div className="relative group flex cursor-pointer pl-1.5">
              <Tooltip content={infoMsg}>
                <InfoIcon height={16} width={16} />
              </Tooltip>
            </div>
          )}
        </div>
      )}

      <div
        className={clsx(
          'flex border rounded-[10px] border-[#E0DEF7] focus-within:ring-1 focus-within:ring-teal-100 focus-within:shadow-[0px_0px_14px_rgba(0,0,0,0.15)]',
          textareaContainerClassName,
          disabled
            ? 'bg-[#e5e7eb] cursor-not-allowed focus-within:border-[#E0DEF7] hover:border-[#E0DEF7]'
            : 'bg-white hover:border-primary-100 focus-within:border-primary-100',
          errorMessage &&
            'border-red-500 focus-within:border-red-500 focus-within:shadow-red-200 shadow-red-200 hover:border-red-500'
        )}
      >
        {/* Left icon container */}
        {leftIcon && <div className="flex-shrink-0 px-2 py-3">{leftIcon}</div>}

        {/* Textarea wrapper with flex-grow to expand */}
        <div className="flex-grow py-3 px-4 overflow-hidden">
          <textarea
            {...field}
            {...rest}
            placeholder={placeholder}
            disabled={disabled}
            rows={rows}
            className={clsx(
              'w-full border-none outline-none appearance-none resize-none overflow-auto disabled:bg-[#e5e7eb] disabled:cursor-not-allowed text-base min-h-[46px] custom-scrollbar',
              textareaClassName
            )}
            style={{
              minHeight: minHeight,
              maxHeight: maxHeight || 'none',
              scrollbarWidth: 'thin',
              scrollbarColor: '#D1D5DB transparent',
            }}
          />
        </div>
      </div>

      {/* Error Message */}
      {errorMessage && (
        <p className="text-red-500 text-sm break-words">{errorMessage}</p>
      )}
    </div>
  );
};

export default TextAreaField;
