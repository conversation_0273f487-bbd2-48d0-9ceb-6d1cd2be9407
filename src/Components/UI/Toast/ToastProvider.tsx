import React, {
  createContext,
  useContext,
  useState,
  useCallback,
  ReactNode,
} from 'react';
import Toast from '.';

// Define Toast Type
type ToastType = 'info' | 'success' | 'warn' | 'error';

// Define Toast Object Interface
interface ToastItem {
  id: number;
  type: ToastType;
  message: string;
  duration: number;
}

// Define Context Interface
interface ToastContextType {
  addToast: (type: ToastType, message: string, duration?: number) => void;
}

// Create Context for Toasts
const ToastContext = createContext<ToastContextType | undefined>(undefined);

// Declare global CustomToast function
export let CustomToast: (
  type: ToastType,
  message: string,
  duration?: number
) => void;

// Toast Provider Props
interface ToastProviderProps {
  children: ReactNode;
}

// Toast Provider Component
export const ToastProvider: React.FC<ToastProviderProps> = ({ children }) => {
  const [toasts, setToasts] = useState<ToastItem[]>([]);

  const addToast = useCallback(
    (type: ToastType, message: string, duration: number = 5000) => {
      const id = Date.now();
      setToasts((prevToasts) => [
        ...prevToasts,
        { id, type, message, duration },
      ]);

      // Remove toast after duration
      setTimeout(() => {
        setToasts((prevToasts) =>
          prevToasts.filter((toast) => toast.id !== id)
        );
      }, duration);
    },
    []
  );

  // Assign function globally
  CustomToast = (type: ToastType, message: string, duration: number = 3000) => {
    addToast(type, message, duration);
  };

  return (
    <ToastContext.Provider value={{ addToast }}>
      {children}

      {/* Toast Container */}
      <div className="fixed top-5 right-5 flex flex-col items-end z-50">
        {toasts.map((toast) => (
          <Toast
            key={toast.id}
            message={toast.message}
            type={toast.type}
            onClose={() =>
              setToasts((prevToasts) =>
                prevToasts.filter((t) => t.id !== toast.id)
              )
            }
          />
        ))}
      </div>
    </ToastContext.Provider>
  );
};

// Custom Hook for Toast Context
// eslint-disable-next-line react-refresh/only-export-components
export const useToast = (): ToastContextType => {
  const context = useContext(ToastContext);
  if (!context) {
    throw new Error('useToast must be used within a ToastProvider');
  }
  return context;
};
