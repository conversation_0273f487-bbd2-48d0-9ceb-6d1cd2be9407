import React, { useEffect } from 'react';
import clsx from 'clsx';

interface ToastProps {
  message: string;
  type?: 'info' | 'success' | 'warn' | 'error';
  onClose: () => void;
  duration?: number;
}

const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  onClose,
  duration = 3000,
}) => {
  useEffect(() => {
    const timer = setTimeout(() => {
      onClose();
    }, duration);

    return () => clearTimeout(timer);
  }, [onClose, duration]);

  return (
    <div
      className={clsx(
        'flex items-center px-4 py-2 rounded-md shadow-lg mb-2 w-80',
        {
          'bg-blue-500 text-white': type === 'info',
          'bg-green-500 text-white': type === 'success',
          'bg-yellow-500 text-white': type === 'warn',
          'bg-red-500 text-white': type === 'error',
        }
      )}
    >
      <span className="flex-1">{message}</span>
      <button className="ml-4 text-white" onClick={onClose}>
        ✕
      </button>
    </div>
  );
};

export default Toast;
