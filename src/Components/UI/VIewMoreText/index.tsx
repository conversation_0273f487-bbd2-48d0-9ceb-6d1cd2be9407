import React, { useState } from 'react';

interface ViewMoreProps {
  text: string;
  maxLength?: number; // Default to 100 if not provided
}

const ViewMore: React.FC<ViewMoreProps> = ({ text, maxLength = 30 }) => {
  const [expanded, setExpanded] = useState(false);

  if (text?.length <= maxLength) return <span>{text}</span>;

  const displayText = expanded ? text : text?.slice(0, maxLength) + '...';

  return (
    <span
      style={{
        whiteSpace: 'normal',
        wordWrap: 'break-word',
        overflowWrap: 'break-word',
      }}
    >
      {displayText}
      <button
        className="text-primary-100 ml-1 hover:underline"
        onClick={() => setExpanded(!expanded)}
      >
        {expanded ? 'View Less' : 'View More'}
      </button>
    </span>
  );
};

export default ViewMore;
