import { useState } from 'react';
import { SearchIcon } from '@Icons';
import InputField from '../InputField';

interface PopoverSearchProps {
  searchTerm: string;
  setSearchTerm: (value: string) => void;
}

const Popover: React.FC<PopoverSearchProps> = ({
  searchTerm,
  setSearchTerm,
}) => {
  const [open, setOpen] = useState(false);

  return (
    <div className="relative inline-block text-left">
      <button
        onClick={() => setOpen(!open)}
        className="p-2 rounded-full hover:bg-gray-200"
      >
        <SearchIcon height={16} width={16} fill="#475467" />
      </button>

      {open && (
        <div className="absolute right-0 mt-2 w-60 bg-white border border-gray-200 rounded-lg shadow-lg p-3">
          <InputField
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            placeholder="Search..."
            className="w-full border border-gray-300 rounded-md p-2"
          />
        </div>
      )}
    </div>
  );
};

export default Popover;
