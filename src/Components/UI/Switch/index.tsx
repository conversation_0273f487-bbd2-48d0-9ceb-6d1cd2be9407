import { motion } from 'framer-motion';

export type SwitchProps = {
  enabled?: boolean;
  onChange?: (value: boolean) => void;
};

const Switch: React.FC<SwitchProps> = ({ enabled = false, onChange }) => {
  const toggleSwitch = () => onChange?.(!enabled);

  return (
    <button
      role="switch"
      aria-checked={enabled}
      tabIndex={0}
      onClick={toggleSwitch}
      onKeyDown={(e) => (e.key === 'Enter' || e.key === ' ') && toggleSwitch()}
      className={`relative w-12 h-6 flex items-center rounded-full transition-colors ${
        enabled ? 'bg-primary-100' : 'bg-gray-300'
      }`}
    >
      <motion.div
        className="w-5 h-5 bg-white rounded-full shadow-md"
        layout
        transition={{ type: 'spring', stiffness: 300, damping: 20 }}
        animate={{ x: enabled ? 26 : 2 }}
      />
    </button>
  );
};

export default Switch;
