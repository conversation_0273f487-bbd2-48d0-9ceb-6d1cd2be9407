import { Column } from '@tanstack/react-table';

type FilterProps<TData> = {
  column: Column<TData, unknown>;
};

function SelectFilter<TData>({ column }: FilterProps<TData>) {
  const columnFilterValue = column.getFilterValue();

  return (
    <select
      onChange={(e) => column.setFilterValue(e.target.value)}
      value={columnFilterValue?.toString() || ''}
      className="w-36 border shadow rounded"
    >
      <option value="">All</option>
      <option value="complicated">Complicated</option>
      <option value="relationship">Relationship</option>
      <option value="single">Single</option>
    </select>
  );
}

export default SelectFilter;
