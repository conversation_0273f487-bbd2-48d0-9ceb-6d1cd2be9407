import React from 'react';

export interface CustomCheckboxProps
  extends React.InputHTMLAttributes<HTMLInputElement> {
  label?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({ label, ...props }) => {
  return (
    <label className="select-none flex items-center space-x-2 cursor-pointer">
      {/* Hidden Default Checkbox */}
      <input {...props} type="checkbox" className="hidden" />

      {/* Custom Checkbox */}
      <div
        className={`w-5 h-5 border-2 flex items-center justify-center rounded-sm transition-all ${
          props.checked
            ? 'bg-primary-100 border-primary-100'
            : 'border-secondary'
        }`}
      >
        {props.checked && <span className="text-white">✔</span>}
      </div>

      {/* Label Text */}
      {label && <span className="text-gray-700">{label}</span>}
    </label>
  );
};

export default CustomCheckbox;
