import { ArrowLeftIcon } from '@Icons';
import React from 'react';

// Define types for the props
interface PaginationProps {
  totalPages: number;
  currentPage: number;
  onPageChange: (page: number) => void;
  isLoading: boolean;
  pageSize: number;
  setPageSize: (size: number) => void;
  options: number[];
  onPageSizeChange?: (size: number) => void;
  disabled?: boolean;
}

const Pagination: React.FC<PaginationProps> = ({
  totalPages,
  currentPage,
  onPageChange,
  isLoading,
  pageSize,
  setPageSize,
  options,
  onPageSizeChange,
  disabled = false,
}) => {
  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newSize = Number(e.target.value);
    setPageSize(newSize);
    if (onPageSizeChange) {
      onPageSizeChange(newSize);
    }
  };

  const getPageNumbers = () => {
    const pages: (number | string)[] = [];

    if (totalPages <= 7) {
      // If 7 or fewer pages, show all
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      if (currentPage <= 3) {
        pages.push(1, 2, 3);
        pages.push('...');
        pages.push(totalPages - 2, totalPages - 1, totalPages);
      } else if (currentPage >= totalPages - 2) {
        pages.push(1, 2, 3);
        pages.push('...');
        pages.push(totalPages - 2, totalPages - 1, totalPages);
      } else {
        pages.push(1);
        pages.push('...');
        pages.push(currentPage - 1, currentPage, currentPage + 1);
        pages.push('...');
        pages.push(totalPages);
      }
    }

    return pages;
  };
  // #D9FFF8
  return (
    <>
      {!isLoading ? (
        <>
          <hr />
          <div className="flex items-center justify-between space-x-2 p-4 h-[72px]">
            <button
              onClick={() => !isLoading && onPageChange(currentPage - 1)}
              disabled={currentPage === 1}
              className={`flex w-[115px] h-[36px] items-center justify-center px-[14px] py-2 rounded-md gap-2 border border-[#D0D5DD] ${
                currentPage === 1
                  ? 'bg-gray-100 cursor-not-allowed'
                  : ' hover:bg-gray-50'
              }`}
            >
              <ArrowLeftIcon height="20" width="20" stroke="#344054" />
              <div className="font-inter text-[14px] font-semibold leading-[20px] text-left text-[#344054]">
                Previous
              </div>
            </button>

            <div className="flex space-x-1 items-center">
              {getPageNumbers().map((page, index) => (
                <button
                  key={index}
                  onClick={() =>
                    typeof page === 'number' ? onPageChange(page) : null
                  }
                  className={`px-3 py-1 text-[#667085] rounded-md w-[40px] h-[40px] font-inter text-[14px] font-medium leading-[20px] text-center ${
                    page === currentPage
                      ? 'bg-[#D9FFF8] text-[#7F56D9]'
                      : page === '...'
                        ? ' border-transparent cursor-default'
                        : ' hover:bg-[#D9FFF8]'
                  }`}
                >
                  {page}
                </button>
              ))}
            </div>
            <div className="flex justify-between gap-2">
              <select
                value={pageSize}
                onChange={handleChange}
                disabled={disabled}
                className="h-[36px] w-[50px] border border-[#D0D5DD] rounded-md focus:border-grey-500 focus:outline-none bg-white font-inter text-[14px] leading-[20px] text-center text-[#344054]"
              >
                {options.map((option) => (
                  <option key={option} value={option}>
                    {option}
                  </option>
                ))}
              </select>

              <button
                onClick={() => !isLoading && onPageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className={`flex w-[115px] h-[36px] items-center justify-center px-[14px] py-2 rounded-md gap-2 border border-[#D0D5DD] ${
                  currentPage === totalPages
                    ? 'bg-gray-100 cursor-not-allowed'
                    : ' hover:bg-gray-50'
                }`}
              >
                <div className="font-inter text-[14px] font-semibold leading-[20px] text-left text-[#344054]">
                  Next
                </div>
                <div className="rotate-180">
                  <ArrowLeftIcon height="20" width="20" stroke="#344054" />
                </div>
              </button>
            </div>
          </div>
        </>
      ) : (
        <div></div>
      )}
    </>
  );
};

export default Pagination;
