import React from 'react';
import clsx from 'clsx';
import { Loader } from '@Icons';

interface ButtonProps {
  className?: string;
  onClick?: () => void;
  text: React.ReactNode;
  type?: 'button' | 'submit' | 'reset';
  loading?: boolean;
  disabled?: boolean;
  variant?: 'primary' | 'outline' | 'other';
  width?: string;
  fillLoader?: string;
  borderColor?: string;
  loaderHW?: number;
}

const Button: React.FC<ButtonProps> = ({
  className = '',
  onClick,
  text,
  type = 'button',
  loading = false,
  disabled = false,
  variant = 'primary',
  width,
  fillLoader,
  borderColor,
  loaderHW,
}) => {
  return (
    <button
      className={clsx(
        `px-4 py-3 rounded-md font-semibold transition-all duration-200 hover:shadow-[0px_0px_14px_rgba(0,0,0,0.15)]`,
        {
          'bg-gradient-to-t from-[#FF8800] to-[#FFA033] text-white':
            variant === 'primary',
          'border-1 border-[#FF8800] text-gray-700 bg-transparent hover:border-primary-100':
            variant === 'outline',
          'opacity-80 cursor-not-allowed': loading || disabled,
          'cursor-pointer': !(loading || disabled),
        },
        borderColor && 'border-2 bg-transparent',
        width ? width : 'w-full',
        className
      )}
      onClick={onClick}
      type={type}
      disabled={disabled}
      style={borderColor ? { borderColor: borderColor } : {}}
    >
      {!loading ? (
        text
      ) : (
        <Loader
          height={loaderHW ?? 20}
          width={loaderHW ?? 20}
          fill={fillLoader ?? '#fff'}
        />
      )}
    </button>
  );
};

export default Button;
