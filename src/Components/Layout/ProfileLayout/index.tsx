import { Button, Modal } from '@Components/UI';
import { useToast } from '@Components/UI/Toast/ToastProvider';
import queryClient from '@Helpers/QueryClient';
import {
  AdminIcon,
  LogoutIcon,
  BellRingingIcon,
  PhoneCallIcon,
  UserCircleMinusIcon,
  AboutUs,
  PrivacyPolicy,
  TermsAndConditionIcon,
  CloseIcon,
  EnterpriseIcon,
} from '@Icons';
import clsx from 'clsx';
import { FC, ReactNode, SVGProps, useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router';
import { PATHS } from '@Config/Path.Config';
import { useLogout, useDeleteAccount } from '@Query/Hooks/useAuth';
import { clearUserData } from '@Redux/SystemControl/UserControle';
import { useDispatch, useSelector } from 'react-redux';

interface SidebarItemProps {
  title: string;
  Icon: FC<SVGProps<SVGSVGElement>>;
  isActive: boolean;
  onClick?: () => void;
  clickAble?: boolean;
}

const ProfileSidebarItem: FC<SidebarItemProps> = ({
  title,
  Icon,
  isActive,
  onClick,
  clickAble = false,
}) => (
  <div
    className={clsx(
      'flex items-center gap-2 border-b border-[#F1F1F1] p-4 cursor-pointer transition-all duration-300',
      isActive ? 'bg-[#FFE3C4]' : 'hover:bg-[#F9F9F9]'
    )}
    onClick={clickAble ? onClick : undefined} // Prevent unnecessary clicks
  >
    <div
      className={clsx(
        'bg-[#F1F1F1] h-10 w-10 rounded-full flex items-center justify-center transition-all duration-300',
        isActive && 'bg-gradient-to-b from-[#FFA033] to-[#FF8800]'
      )}
    >
      <Icon height={18} width={18} fill={isActive ? '#FFFFFF' : '#FFA033'} />
    </div>
    <h4
      className={clsx(
        'text-[#333333] text-base font-semibold transition-all duration-300',
        isActive && 'text-[#FFA033]'
      )}
    >
      {title}
    </h4>
  </div>
);

interface ProfileLayoutProps {
  children: ReactNode;
}

const ProfileLayout: FC<ProfileLayoutProps> = ({ children }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const dispatch = useDispatch();
  const [openLogoutModal, setOpenLogoutModal] = useState<boolean>(false);
  const [openDeleteAccountModal, setOpenDeleteAccountModal] =
    useState<boolean>(false);

  const { addToast } = useToast();

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const userData = useSelector((state: any) => state.UserControle.user);

  const {
    mutate: logout,
    isLoading,
    isError,
    error,
    isSuccess,
    data,
  } = useLogout();

  useEffect(() => {
    if (isError) {
      addToast('error', error as string);
    }
  }, [isError, error]);

  useEffect(() => {
    if (isSuccess) {
      addToast('success', data?.data?.message);
      setOpenLogoutModal(false);
      localStorage.clear();
      sessionStorage.clear();
      dispatch(clearUserData());
      queryClient.clear();
      navigate(PATHS.LOGIN, { replace: true });
    }
  }, [isSuccess]);

  const {
    mutate: deleteAccount,
    isLoading: deleteAccountLoading,
    isError: deleteAccountIsError,
    error: deleteAccountError,
    isSuccess: deleteAccountSuccess,
    data: deleteAccountData,
  } = useDeleteAccount();

  useEffect(() => {
    if (deleteAccountIsError) {
      addToast('error', deleteAccountError as string);
    }
  }, [isError, deleteAccountError]);

  useEffect(() => {
    if (deleteAccountSuccess) {
      addToast('success', deleteAccountData?.data?.message);
      setOpenDeleteAccountModal(false);
      localStorage.clear();
      sessionStorage.clear();
      dispatch(clearUserData());
      queryClient.clear();
      navigate(PATHS.LOGIN, { replace: true });
    }
  }, [deleteAccountSuccess]);

  const handleLogout = () => setOpenLogoutModal(true);
  const handleDeleteAccount = () => setOpenDeleteAccountModal(true);

  const sidebarList = [
    {
      key: 'myProfile',
      title: 'My Profile',
      Icon: AdminIcon,
      path: PATHS.PROFILE,
      clickAble: true,
    },
    {
      key: 'businessDetails',
      title: 'Business Details',
      Icon: EnterpriseIcon,
      path: PATHS.ENTERPRISE_BUSINESS_DETAILS,
      clickAble: true,
    },
    {
      key: 'mySubscriptionPlan',
      title: 'Subscription',
      Icon: AdminIcon,
      path: PATHS.MY_SUBSCRIPTION,
      clickAble: true,
    },
    // {
    //   key: 'purchaseHistory',
    //   title: 'Purchase History',
    //   Icon: PurchaseHistoryIcon,
    //   path: null,
    //   clickAble: true,
    // },
    // {
    //   key: 'changePassword',
    //   title: 'Change Password',
    //   Icon: KeyIcon,
    //   path: null,
    //   clickAble: true,
    // },
    {
      key: 'notificationSettings',
      title: 'Notification Settings',
      Icon: BellRingingIcon,
      path: PATHS.NOTIFICATION_SETTING,
      clickAble: true,
    },
    {
      key: 'aboutUs',
      title: 'About Us',
      Icon: AboutUs,
      path: PATHS.ABOUT_US,
      clickAble: true,
    },
    {
      key: 'termsConditions',
      title: 'Terms & Conditions',
      Icon: TermsAndConditionIcon,
      path: PATHS.TERMS_CONDITIONS,
      clickAble: true,
    },
    {
      key: 'privacyPolicy',
      title: 'Privacy Policy',
      Icon: PrivacyPolicy,
      path: PATHS.PRIVACY_POLICY,
      clickAble: true,
    },
    {
      key: 'ConatctUs',
      title: 'Conatct Us',
      Icon: PhoneCallIcon,
      path: PATHS.CONTACT_US,
      clickAble: true,
    },
    {
      key: 'deleteAccount',
      title: 'Delete Account',
      Icon: UserCircleMinusIcon,
      path: null,
      clickAble: true,
      action: handleDeleteAccount,
    },
    {
      key: 'logout',
      title: 'Logout',
      Icon: LogoutIcon,
      path: null,
      clickAble: true,
      action: handleLogout,
    },
  ];

  const filteredSidebarList = sidebarList.filter((item) => {
    if (item.key === 'mySubscriptionPlan') {
      return userData.role !== 'org-user';
    }

    return true;
  });

  return (
    <>
      <div className="p-10">
        <span className="text-[32px] font-semibold">My Account</span>

        <div className="flex gap-8 pt-6">
          <div className="w-[380px] h-[80vh] shadow-md rounded-md overflow-y-auto p-4 text-gray-700 leading-relaxed bg-white">
            {filteredSidebarList.map(
              ({ key, title, Icon, path, clickAble, action }) => (
                <ProfileSidebarItem
                  key={key}
                  title={title}
                  Icon={Icon}
                  isActive={path === location.pathname}
                  onClick={() => (path ? navigate(path) : action?.())}
                  clickAble={clickAble}
                />
              )
            )}
          </div>

          <div className="w-full border border-[#F1F1F1] rounded-md bg-white">
            {children}
          </div>
          <Modal
            children={
              <div className="flex flex-col items-center gap-[36px] justify-center">
                <div className="bg-[#F9E2CF] h-14 w-14 flex justify-center items-center rounded-full">
                  <LogoutIcon width={18} height={18} fill="#FFA033" />
                </div>
                <div>
                  <h4 className="text-black text-2xl font-bold text-center">
                    Are you sure want to logout this account?
                  </h4>
                </div>
                <div className="flex w-full gap-6">
                  <Button
                    text="Yes"
                    loading={isLoading}
                    onClick={() => logout()}
                  />
                  <Button
                    text="No"
                    variant="outline"
                    onClick={() => setOpenLogoutModal(false)}
                  />
                </div>
              </div>
            }
            hideCloseButton
            isOpen={openLogoutModal}
            onClose={() => setOpenLogoutModal(false)}
          />

          <Modal
            size="md"
            children={
              <div className="flex flex-col items-center gap-[36px] justify-center">
                <div className="bg-[#FFD8E0] h-14 w-14 flex justify-center items-center rounded-full">
                  <CloseIcon width={18} height={18} fill="#FF3B30" />
                </div>
                <div>
                  <h4 className="text-black text-2xl font-bold text-center">
                    Are you sure want to delete this account?
                  </h4>
                </div>
                <div className=" w-full flex justify-center gap-6">
                  <Button
                    loading={deleteAccountLoading}
                    text="Delete"
                    variant="other"
                    className="h-[50px] w-[50px] border border-[#FF0000]
              text-[#FF0000] bg-transparent hover:border-[#FF0000]"
                    onClick={() => deleteAccount()}
                  />
                  <Button
                    text="Cancel"
                    variant="outline"
                    onClick={() => {
                      setOpenDeleteAccountModal(false);
                    }}
                  />
                </div>
              </div>
            }
            hideCloseButton
            isOpen={openDeleteAccountModal}
            onClose={() => setOpenDeleteAccountModal(false)}
          />
        </div>
      </div>
    </>
  );
};

export default ProfileLayout;
