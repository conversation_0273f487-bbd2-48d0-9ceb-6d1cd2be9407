import React, { ReactNode } from 'react';
import { BackArrowIcon, RoboIcon } from '@Icons';
import { useNavigate } from 'react-router';

interface RegisterLayoutProps {
  title: string;
  subTitle: string;
  showBack?: boolean;
  children: ReactNode;
}

const RegisterLayout: React.FC<RegisterLayoutProps> = ({
  title,
  subTitle,
  showBack = false,
  children,
}) => {
  const navigaate = useNavigate();
  const currentYear = new Date().getFullYear();
  return (
    <div className="flex flex-col h-screen w-screen items-center pt-12 relative overflow-y-auto">
      <div className="flex">
        <RoboIcon/>
      </div>
      <div></div>
      <div className="flex pt-6 w-full sm:w-[500px] items-center">
        {showBack && (
          <div
            className="cursor-pointer"
            role="button"
            onClick={() => navigaate(-1)}
          >
            <BackArrowIcon height={24} width={24} />
          </div>
        )}
        <div className="text-black font-bold text-4xl w-full text-center">
          {title}
        </div>
      </div>
      <div className="text-black font-normal text-lg pt-2 text-center w-[500px]">
        {subTitle}
      </div>
      <div className="w-full sm:w-[500px] flex flex-col pb-20 pt-4">
        {children}
      </div>
      <div className="fixed w-full text-xs text-center bottom-0 py-2 bg-white text-secondary">
        Copyright © {currentYear} REX. All rights reserved.
      </div>
    </div>
  );
};

export default RegisterLayout;
