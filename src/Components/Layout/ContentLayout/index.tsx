import clsx from 'clsx';
import React, { ReactNode } from 'react';

interface ContentLayoutProps {
  children?: ReactNode;
  title?: string | ReactNode;
  subtitle?: string;
  headerRightSideChildren?: ReactNode;
}

const ContentLayout: React.FC<ContentLayoutProps> = ({
  children,
  subtitle,
  title,
  headerRightSideChildren,
}: ContentLayoutProps) => {
  return (
    <div>
      <div className="flex flex-col justify-between sticky top-0 bg-white z-20">
        <div className={clsx('flex justify-between gap-1.5 p-6 px-10 pb-0')}>
          <div className="flex flex-col gap-1.5">
            <h3 className="text-xl font-semibold">{title}</h3>
            <p className="text-sm text-gray-500">{subtitle}</p>
          </div>
          <div>{headerRightSideChildren}</div>
        </div>
        {/* <Divider /> */}
      </div>
      <div className="p-10 flex flex-col gap-4 ">{children}</div>
    </div>
  );
};
export default ContentLayout;
