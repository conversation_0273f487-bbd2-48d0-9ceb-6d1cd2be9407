import React, { ReactNode, useEffect, useState } from 'react';
import Sidebar from './Sidebar';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useParams } from 'react-router';
import { API_PATHS } from '@Helpers/Constants';
import { PATHS } from '@Config/Path.Config';
import { setUserData } from '@Redux/SystemControl/UserControle';
import Api from '@Helpers/Api';
import clsx from 'clsx';

interface MainLayoytInterface {
  children: ReactNode;
}

const MainLayout: React.FC<MainLayoytInterface> = ({ children }) => {
  const features = useSelector(
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    (state: any) => state?.UserControle?.user
  );
  const api = new Api();
  const { id } = useParams();
  const dispatch = useDispatch();
  const { pathname } = useLocation();

  const [isProfileLoading, setIsProfileLoading] = useState<boolean>(true);

  useEffect(() => {
    if (pathname === PATHS.HOME) {
      setTimeout(async () => {
        try {
          const { data } = await api.get(API_PATHS.USER_PROFILE);
          if (data?.status) {
            dispatch(setUserData({ ...features, ...data?.data }));
          }
        } catch (err) {
          console.log('err', err);
        } finally {
          setIsProfileLoading(false);
        }
      }, 1000);
    } else {
      setIsProfileLoading(false);
    }
  }, [pathname]);

  return (
    <div className="h-screen w-screen max-w-screen max-h-screen flex flex-1 overflow-hidden relative">
      <Sidebar />
      <div
        className={clsx(
          'h-full w-full flex flex-col relative',
          id ? '!overflow-hidden' : '!overflow-auto '
        )}
      >
        {children}
      </div>
    </div>
  );
};

export default MainLayout;