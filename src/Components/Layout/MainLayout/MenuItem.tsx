import React, { useState } from 'react';
import { CaretRightIcon } from '@Icons';
import clsx from 'clsx';
import { useNavigate } from 'react-router';
import { Button, Modal } from '@Components/UI';
// import AccessibleImage from './AccessibleImage';

interface MenuItem {
  key: string;
  title: string;
  Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  path?: string;
  active: boolean;
  bottom?: boolean;
}

interface MenuItemProps {
  keyName: string;
  title: string;
  Icon?: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  activeMenu: string;
  setActiveMenu: (key: string) => void;
  showNotificationBadge?: boolean;
  showCaretIcon?: boolean;
  notificationCount?: number;
  isCollapsed?: boolean;
  path?: string;
}

const MenuItem = ({
  keyName,
  title,
  Icon,
  activeMenu,
  setActiveMenu,
  showNotificationBadge = false,
  showCaretIcon = false,
  notificationCount = 0,
  isCollapsed = false,
  path = '/',
}: MenuItemProps) => {
  const navigate = useNavigate();
  const [isSessionWarningOpen, setIsSessionWarningOpen] =
    useState<boolean>(false);

  const handleNavigate = async () => {
    // Check for active Home (Ask Rex) session
    const hasActiveHomeSession =
      activeMenu === 'home' &&
      keyName !== 'home' &&
      localStorage.getItem('SESSION_ID') !== 'null' &&
      localStorage.getItem('NAVIGATED_FROM') !== 'HISTORY';

    // Check for active Quick Calc session
    const hasActiveQuickCalcSession =
      activeMenu === 'quickCalc' &&
      keyName !== 'quickCalc' &&
      localStorage.getItem('SESSION_ID_QUICK_CALC') !== 'null' &&
      localStorage.getItem('NAVIGATED_FROM') !== 'HISTORY';

    if (hasActiveHomeSession || hasActiveQuickCalcSession) {
      setIsSessionWarningOpen(true);
    } else {
      localStorage.removeItem('NAVIGATED_FROM');
      setActiveMenu(keyName);
      navigate(path);
    }
  };

  return (
    <>
      <Modal isOpen={isSessionWarningOpen} hideCloseButton>
        <div className="flex flex-col gap-y-8 p-3">
          <div className="text-center font-semibold text-lg text-black">
            Your chat session will end. Are you sure you want to continue?
          </div>
          <div className="flex w-full gap-6">
            <Button
              text="Yes"
              onClick={() => {
                setIsSessionWarningOpen(false);
                setActiveMenu(keyName);
                navigate(path);
              }}
            />
            <Button
              text="No"
              variant="outline"
              onClick={() => setIsSessionWarningOpen(false)}
            />
          </div>
        </div>
      </Modal>

      <li key={keyName}>
        <button
          onClick={handleNavigate}
          className={clsx(
            'relative w-full flex items-center p-4 pl-7 space-x-3 text-left hover:bg-[#F4F4FB] text-[#333333] cursor-pointer',
            activeMenu === keyName &&
            'bg-gradient-to-t from-[#FF8800] to-[#FFA033] text-white shadow-[0px_0px_14px_rgba(255,136,0,0.2)]'
          )}
        >
          {Icon && (
            <div>
              <Icon
                height={24}
                width={24}
                fill={activeMenu === keyName ? '#fff' : '#FFA033'}
              />
            </div>
          )}
          {!isCollapsed && (
            <span className="text-base font-medium">{title}</span>
          )}
          {!isCollapsed &&
            showNotificationBadge &&
            keyName === 'notifications' &&
            notificationCount > 0 && (
              <div
                className={`shadow-md absolute right-5 h-[30px] w-[30px] font-semibold ${activeMenu === 'notifications'
                    ? 'bg-white text-primary-100'
                    : 'bg-gradient-to-tr from-primary-100 to-[#FF8800] text-white'
                  } rounded-full flex items-center justify-center`}
              >
                {notificationCount}
              </div>
            )}
          {!isCollapsed && showCaretIcon && keyName === 'account' && (
            <CaretRightIcon
              className="absolute right-5 mt-2.5"
              height={25}
              width={25}
              fill={activeMenu === 'account' ? '#fff' : '#FFA033'}
            />
          )}
        </button>
      </li>
    </>
  );
};

export default MenuItem;
