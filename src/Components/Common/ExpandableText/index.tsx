import React, { useState } from 'react';

interface ExpandableTextProps {
  text: string;
  maxLength?: number;
  className?: string;
}

const ExpandableText: React.FC<ExpandableTextProps> = ({
  text,
  maxLength = 50,
  className = '',
}) => {
  const [expanded, setExpanded] = useState(false);

  if (!text) return null;

  const isLong = text.length > maxLength;
  const displayText =
    expanded || !isLong ? text : text.slice(0, maxLength).trim() + '...';

  return (
    <p className={className ?? ''}>
      {displayText}
      {isLong && (
        <span
          onClick={() => setExpanded(!expanded)}
          className="ml-1 text-primary-100 cursor-pointer hover:underline"
        >
          {expanded ? 'Show less' : 'Read more'}
        </span>
      )}
    </p>
  );
};

export default ExpandableText;
