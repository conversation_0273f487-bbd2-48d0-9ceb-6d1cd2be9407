import React from 'react';

interface AccessibleImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  title?: string;
  caption?: string;
  isDecorative?: boolean;
  className?: string;
}

const AccessibleImage: React.FC<AccessibleImageProps> = ({
  src,
  alt,
  width,
  height,
  title,
  caption,
  isDecorative = false,
  className,
}) => {
  const imageAlt = isDecorative ? '' : alt;

  return (
    <figure
      className={className}
      style={{ maxWidth: '100%', display: 'inline-block' }}
    >
      <img
        src={src}
        alt={imageAlt}
        title={title}
        width={width}
        height={height}
        loading="lazy"
        style={{
          width: '100%',
          height: 'auto',
          display: 'block',
        }}
      />
      {caption && <figcaption>{caption}</figcaption>}
    </figure>
  );
};

export default AccessibleImage;
