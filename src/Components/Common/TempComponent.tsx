import { Breadcrumb } from '@Components/UI';
import { PATHS } from '@Config/Path.Config';
import { useLocation } from 'react-router';

const TITLES = {
  [PATHS.ANALYTICS]: 'Analytics',
  [PATHS.LEGALS]: 'Legal',
  [PATHS.TRAINING]: 'Training & Education',
  [PATHS.TRADIE_HUB]: `<PERSON><PERSON><PERSON>'s Hub`,
};

const TempComponent = (): JSX.Element => {
  const { pathname } = useLocation();
  const breadcrumbItems = [
    { label: 'Home', link: '/' },
    { label: TITLES[pathname] },
  ];

  return (
    <div className="flex flex-col h-full w-full">
      <div className="flex items-center justify-between p-10">
        <div>
          <span className="text-[32px] font-semibold">{TITLES[pathname]}</span>
          <Breadcrumb items={breadcrumbItems} />
        </div>
      </div>
      <div className="flex flex-1 justify-center items-center font-medium text-secondary text-3xl text-center px-12 md:px-40 xl:px-90">
        Development is in progress. You will soon be enjoying this feature
        seamlessly.
      </div>
    </div>
  );
};

export default TempComponent;
