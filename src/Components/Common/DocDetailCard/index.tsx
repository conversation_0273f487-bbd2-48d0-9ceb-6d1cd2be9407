import { Loader, PdfIcon, PrintIcon } from '@Icons';

const DocDetailCard = ({
  title,
  onDownload,
  loading = false,
}: {
  title: string;
  onDownload: () => void;
  loading?: boolean;
}): JSX.Element => {
  return (
    <div className="select-none rounded-[10px] border border-[#F1F1F1] flex items-center justify-between p-4 hover:bg-primary-light hover:text-primary-100 hover:border-primary-100 ">
      <div
        className="flex flex-1 gap-5 items-center cursor-pointer "
        onClick={onDownload}
      >
        <div className="h-[33px] w-[33px] bg-[#E9F1FF] rounded-full flex items-center justify-center">
          <PdfIcon height={16} width={16} />
        </div>
        <div className="w-[35rem] text-lg  font-medium overflow-hidden whitespace-nowrap text-ellipsis">
          {title}
        </div>
      </div>
      <div>
        {!loading ? (
          <button
            onClick={onDownload}
            className="cursor-pointer gap-2 select-none text-sm text-primary-100 px-3 py-1.5 hover:scale-105 bg-[#F6F6F6] rounded-sm flex items-center justify-center"
          >
            <PrintIcon height={16} width={16} /> Print
          </button>
        ) : (
          <Loader height={20} width={20} fill="#fff" />
        )}
      </div>
    </div>
  );
};

export default DocDetailCard;
