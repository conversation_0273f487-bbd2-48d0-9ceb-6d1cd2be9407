import { Switch } from '@Components/UI';
import { SwitchProps } from '@Components/UI/Switch';
import { ClockCounterwiseIcon, DeleteIcon, EditIcon, EyeIcon } from '@Icons';
import clsx from 'clsx';
import { MouseEventHandler } from 'react';

interface ActionButtonsProps {
  deleteIcon?: boolean;
  deleteIconDisabled?: boolean;
  editIcon?: boolean;
  editIconDisabled?: boolean;
  viewIcon?: boolean;
  isSwitch?: boolean;
  isPurchaseHistory?: boolean;
  switchProps?: SwitchProps;
  onDelete?: MouseEventHandler<SVGSVGElement>;
  onEdit?: MouseEventHandler<SVGSVGElement>;
  onView?: <PERSON><PERSON>ventHandler<SVGSVGElement>;
  onPurchaseHistoryView?: MouseEventHandler<SVGSVGElement>;
  loadingView?: boolean;
  loadingPurchaseHistory?: boolean;
  loadingEdit?: boolean;
}

const ActionButtons = ({
  deleteIcon,
  editIcon,
  viewIcon,
  onDelete,
  onEdit,
  onView,
  isSwitch,
  switchProps,
  isPurchaseHistory,
  editIconDisabled,
  deleteIconDisabled,
  onPurchaseHistoryView,
  loadingView,
  loadingEdit,
  // loadingDelete,
  loadingPurchaseHistory,
}: ActionButtonsProps) => {
  return (
    <div className="flex gap-4">
      {isSwitch && <Switch {...switchProps} />}
      {viewIcon && (
        <EyeIcon
          height={20}
          width={20}
          className={`cursor-pointer ${loadingView ? 'opacity-50' : ''}`}
          onClick={onView && !loadingView ? onView : () => null}
        />
      )}
      {editIcon && (
        <EditIcon
          height={16}
          width={16}
          className={clsx(
            loadingEdit ? 'opacity-60' : '',
            editIconDisabled
              ? 'cursor-not-allowed opacity-60'
              : 'cursor-pointer'
          )}
          onClick={editIconDisabled ? undefined : onEdit}
        />
      )}

      {deleteIcon && (
        <DeleteIcon
          height={18}
          width={18}
          className={
            deleteIconDisabled
              ? 'cursor-not-allowed opacity-60 '
              : 'cursor-pointer'
          }
          onClick={deleteIconDisabled ? undefined : onDelete}
        />
      )}
      {isPurchaseHistory && (
        <ClockCounterwiseIcon
          height={18}
          width={18}
          className={`cursor-pointer ${loadingPurchaseHistory ? 'opacity-50' : ''}`}
          onClick={
            onPurchaseHistoryView && !loadingPurchaseHistory
              ? onPurchaseHistoryView
              : () => null
          }
        />
      )}
    </div>
  );
};

export default ActionButtons;
