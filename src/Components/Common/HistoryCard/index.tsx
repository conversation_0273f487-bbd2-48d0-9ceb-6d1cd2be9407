import { ClockIcon } from '@Icons';
import PopoverMenu, { PopoverMenuProps } from '../Popover';
import CustomCheckbox, { CustomCheckboxProps } from '@Components/UI/Checkbox';
import { getFormattedDate } from '@Helpers/Utils';

const HistoryCard = ({
  // time,
  date,

  id,
  title,

  // popoverProps,
  // checkboxProps,
}: {
  id: number;
  date: string;

  title: string;
  // popoverProps: PopoverMenuProps;
  // checkboxProps: CustomCheckboxProps;
}) => {
  return (
    <div
      key={id}
      className="p-6 border border-[#F1F1F1] rounded-[10px] shadow-[0px_4px_14px_0px_#9E9E9E1A]"
    >
      <div className="pb-6">
        <text className="text-xl font-normal text-black">
          {/* {getFormattedDate(date)} */}
          {date}
        </text>
      </div>
      <div className="flex flex-col gap-2">
        {new Array(10).fill('abc').map((_, index) => (
          <div key={index} className="flex items-center justify-between">
            {/* Left Section: Checkbox & Time */}
            <div className="flex items-center gap-4 w-[7.44%] min-w-[111px]">
              <CustomCheckbox />

              <div className="flex items-center gap-2">
                <ClockIcon height={14} width={14} />
                {/* <span className="text-sm text-secondary">{time}</span> */}
              </div>
            </div>

            {/* Middle Section: Truncated Text */}
            <div className="w-[86.16%]">
              <div className="w-[35rem] overflow-hidden whitespace-nowrap text-ellipsis">
                {title}
              </div>
            </div>

            {/* Right Section: Popover Menu */}
            <div className="w-[1.61%] min-w-[24px] flex justify-end">
              {/* <PopoverMenu {...popoverProps} /> */}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default HistoryCard;
