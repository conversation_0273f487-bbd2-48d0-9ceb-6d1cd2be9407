import { Dispatch, SetStateAction } from 'react';

export interface PopoverMenuProps {
  open: boolean;
  setOpen: Dispatch<SetStateAction<boolean>>;
}

const PopoverMenu: React.FC<PopoverMenuProps> = ({ open, setOpen }) => {
  return (
    <div className="relative inline-block text-left">
      {/* Three Dots Button */}
      <button
        onClick={() => setOpen(!open)}
        className="p-2 rounded-full hover:bg-gray-200"
      >
        <span className="text-2xl">⋮</span>
      </button>

      {/* Dropdown Menu */}
      {open && (
        <div className="absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg">
          <button
            className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setOpen(false)}
          >
            Edit
          </button>
          <button
            className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setOpen(false)}
          >
            Delete
          </button>
          <button
            className="block w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-100"
            onClick={() => setOpen(false)}
          >
            Share
          </button>
        </div>
      )}
    </div>
  );
};

export default PopoverMenu;
