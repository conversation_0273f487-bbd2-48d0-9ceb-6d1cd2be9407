import { initializeApp } from 'firebase/app';
import { getMessaging, getToken } from 'firebase/messaging';

const firebaseConfig = {
  apiKey: import.meta.env.VITE_FIREBASE_API_KEY,
  authDomain: import.meta.env.VITE_FIREBASE_AUTH_DOMAIN,
  projectId: import.meta.env.VITE_FIREBASE_PROJECT_ID,
  storageBucket: import.meta.env.VITE_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: import.meta.env.VITE_FIREBASE_MESSAGING_SENDER_ID,
  appId: import.meta.env.VITE_FIREBASE_APP_ID,
  measurementId: import.meta.env.VITE_FIREBASE_MEASUREMENT_ID,
};

const app = initializeApp(firebaseConfig);
const messaging = getMessaging(app);

export const requestForToken = async (): Promise<string | void> => {
  try {
    if (Notification.permission === 'denied') {
      alert(
        'Notifications are blocked. Please enable them in your browser settings.'
      );
      return;
    }

    const permission = await Notification.requestPermission();
    if (permission === 'granted') {
      const registration = await navigator.serviceWorker.register(
        '/firebase-messaging-sw.js'
      );

      const vapidKey =
        'BJnJ355VEfudkU3NDfuhm1L0mvd3Nhe_U5_ZJcas5IxFC19nzpwdS1M2-iUxSkqrDqr1LQ0_rHNddwV1sKKpidI';

      const token = await getToken(messaging, {
        vapidKey,
        serviceWorkerRegistration: registration,
      });
      return token;
    } else {
      console.warn('Permission for notifications denied');
    }
  } catch (error) {
    console.error('Error getting device token:', error);
  }
};
