export function isIEBrowser(): boolean {
  // BROWSER CHECK VARIABLES
  const ua = window.navigator.userAgent;
  const msie = ua.indexOf('MSIE ');
  const msie11 = ua.indexOf('Trident/');
  // const msedge = ua.indexOf('Edge/');
  return msie > 0 || msie11 > 0;
  // const isEdge = msedge > 0;
}

export const getFormattedDate = (dateStr: string) => {
  const inputDate = new Date(
    dateStr.split('-').reverse().join('-') // Convert "dd-MM-yyyy" to "yyyy-MM-dd"
  );

  const today = new Date();
  const yesterday = new Date();
  yesterday.setDate(today.getDate() - 1);

  // Remove time for accurate comparison
  today.setHours(0, 0, 0, 0);
  yesterday.setHours(0, 0, 0, 0);
  inputDate.setHours(0, 0, 0, 0);

  let prefix = '';
  if (inputDate.getTime() === today.getTime()) prefix = 'Today - ';
  else if (inputDate.getTime() === yesterday.getTime()) prefix = 'Yesterday - ';

  // Format as "Friday 14 February 2025"
  const options: Intl.DateTimeFormatOptions = {
    weekday: 'long',
    day: '2-digit',
    month: 'long',
    year: 'numeric',
  };
  const formattedDate = inputDate.toLocaleDateString('en-US', options);

  return `${prefix}${formattedDate}`;
};

export const isValidJson = (str: string): boolean => {
  try {
    JSON.parse(str);
    return true;
  } catch {
    return false;
  }
};

export const EMAIL_REGX =
  /^(?!.*\.\.)[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
export const NO_SPEC_CHARACTER = /^[A-Za-z ]+$/;

export const FormatDateToYMD = (date: string) => {
  const _date = new Date(date);

  return `${_date.getFullYear()}-${(_date.getMonth() + 1)
    .toString()
    .padStart(2, '0')}-${_date.getDate().toString().padStart(2, '0')}`;
};

export const capitalize = (str: string) =>
  str ? str?.charAt(0).toUpperCase() + str?.slice(1).toLowerCase() : '';

export const trim = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).trim() + '...';
};

export const DateConvertor2 = (
  date: string,
  format:
    | 'MM/DD/YYYY'
    | 'YYYY-MM-DD'
    | 'Month'
    | 'Day'
    | 'Year'
    | 'Full' = 'MM/DD/YYYY'
) => {
  const _date = new Date(date);

  const monthNames = [
    'January',
    'February',
    'March',
    'April',
    'May',
    'June',
    'July',
    'August',
    'September',
    'October',
    'November',
    'December',
  ];

  switch (format) {
    case 'Month':
      return monthNames[_date.getMonth()];
    case 'Day':
      return _date.getDate().toString().padStart(2, '0');
    case 'Year':
      return _date.getFullYear().toString();
    case 'Full':
      return `${monthNames[_date.getMonth()]} ${_date.getDate()}, ${_date.getFullYear()}`;
    case 'YYYY-MM-DD':
      return `${_date.getFullYear()}-${(_date.getMonth() + 1)
        .toString()
        .padStart(2, '0')}-${_date.getDate().toString().padStart(2, '0')}`;
    case 'MM/DD/YYYY':
    default:
      return `${(_date.getMonth() + 1)
        .toString()
        .padStart(
          2,
          '0'
        )}/${_date.getDate().toString().padStart(2, '0')}/${_date.getFullYear()}`;
  }
};
