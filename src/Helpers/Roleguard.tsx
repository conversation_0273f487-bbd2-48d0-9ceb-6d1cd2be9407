import { Navigate, Outlet } from 'react-router-dom';
import { useSelector } from 'react-redux';
import { PATHS } from '@Config/Path.Config';

interface RoleGuardProps {
  redirectPath?: string;
  isPublicRoute?: boolean;
}

export const RoleGuard: React.FC<RoleGuardProps> = ({
  redirectPath = '/login',
  isPublicRoute = false,
}) => {
  const userData = useSelector((state: any) => state.UserControle.user);

  if (isPublicRoute && userData?.access_token) {
    return <Navigate to={PATHS.HOME} replace />;
  }

  if (!isPublicRoute && !userData?.access_token) {
    return <Navigate to={redirectPath} replace />;
  }

  return <Outlet />;
};