export const COUNTRY_CODES = [
  {
    label: '+1',
    value: '+1',
    flag: 'https://flagcdn.com/w320/us.png',
  },
  {
    label: '+93',
    value: '+93',
    flag: 'https://flagcdn.com/w320/af.png',
  },
  {
    label: '+355',
    value: '+355',
    flag: 'https://flagcdn.com/w320/al.png',
  },
  {
    label: '+374',
    value: '+374',
    flag: 'https://flagcdn.com/w320/am.png',
  },
  {
    label: '+244',
    value: '+244',
    flag: 'https://flagcdn.com/w320/ao.png',
  },
  {
    label: '+54',
    value: '+54',
    flag: 'https://flagcdn.com/w320/ar.png',
  },
  {
    label: '+1-684',
    value: '+1-684',
    flag: 'https://flagcdn.com/w320/as.png',
  },
  {
    label: '+61',
    value: '+61',
    flag: 'https://flagcdn.com/w320/au.png',
  },
  {
    label: '+43',
    value: '+43',
    flag: 'https://flagcdn.com/w320/at.png',
  },
  {
    label: '+994',
    value: '+994',
    flag: 'https://flagcdn.com/w320/az.png',
  },
  {
    label: '+387',
    value: '+387',
    flag: 'https://flagcdn.com/w320/ba.png',
  },
  {
    label: '+1-246',
    value: '+1-246',
    flag: 'https://flagcdn.com/w320/bb.png',
  },
  {
    label: '+880',
    value: '+880',
    flag: 'https://flagcdn.com/w320/bd.png',
  },
  {
    label: '+32',
    value: '+32',
    flag: 'https://flagcdn.com/w320/be.png',
  },
  {
    label: '+226',
    value: '+226',
    flag: 'https://flagcdn.com/w320/bf.png',
  },
  {
    label: '+359',
    value: '+359',
    flag: 'https://flagcdn.com/w320/bg.png',
  },
  {
    label: '+257',
    value: '+257',
    flag: 'https://flagcdn.com/w320/bi.png',
  },
  {
    label: '+229',
    value: '+229',
    flag: 'https://flagcdn.com/w320/bj.png',
  },
  {
    label: '+1-441',
    value: '+1-441',
    flag: 'https://flagcdn.com/w320/bm.png',
  },
  {
    label: '+673',
    value: '+673',
    flag: 'https://flagcdn.com/w320/bn.png',
  },
  {
    label: '+591',
    value: '+591',
    flag: 'https://flagcdn.com/w320/bo.png',
  },
  {
    label: '+55',
    value: '+55',
    flag: 'https://flagcdn.com/w320/br.png',
  },
  {
    label: '+1-242',
    value: '+1-242',
    flag: 'https://flagcdn.com/w320/bs.png',
  },
  {
    label: '+375',
    value: '+375',
    flag: 'https://flagcdn.com/w320/by.png',
  },
  {
    label: '+1',
    value: '+1',
    flag: 'https://flagcdn.com/w320/ca.png',
  },
  {
    label: '+236',
    value: '+236',
    flag: 'https://flagcdn.com/w320/cf.png',
  },
  {
    label: '+242',
    value: '+242',
    flag: 'https://flagcdn.com/w320/cg.png',
  },
  {
    label: '+41',
    value: '+41',
    flag: 'https://flagcdn.com/w320/ch.png',
  },
  {
    label: '+225',
    value: '+225',
    flag: 'https://flagcdn.com/w320/ci.png',
  },
  {
    label: '+56',
    value: '+56',
    flag: 'https://flagcdn.com/w320/cl.png',
  },
  {
    label: '+237',
    value: '+237',
    flag: 'https://flagcdn.com/w320/cm.png',
  },
  {
    label: '+86',
    value: '+86',
    flag: 'https://flagcdn.com/w320/cn.png',
  },
  {
    label: '+57',
    value: '+57',
    flag: 'https://flagcdn.com/w320/co.png',
  },
  {
    label: '+506',
    value: '+506',
    flag: 'https://flagcdn.com/w320/cr.png',
  },
  {
    label: '+53',
    value: '+53',
    flag: 'https://flagcdn.com/w320/cu.png',
  },
  {
    label: '+357',
    value: '+357',
    flag: 'https://flagcdn.com/w320/cy.png',
  },
  {
    label: '+420',
    value: '+420',
    flag: 'https://flagcdn.com/w320/cz.png',
  },
  {
    label: '+49',
    value: '+49',
    flag: 'https://flagcdn.com/w320/de.png',
  },
  {
    label: '+253',
    value: '+253',
    flag: 'https://flagcdn.com/w320/dj.png',
  },
  {
    label: '+45',
    value: '+45',
    flag: 'https://flagcdn.com/w320/dk.png',
  },
  {
    label: '+1-767',
    value: '+1-767',
    flag: 'https://flagcdn.com/w320/dm.png',
  },
  {
    label: '+1-809',
    value: '+1-809',
    flag: 'https://flagcdn.com/w320/do.png',
  },
  {
    label: '+213',
    value: '+213',
    flag: 'https://flagcdn.com/w320/dz.png',
  },
  {
    label: '+593',
    value: '+593',
    flag: 'https://flagcdn.com/w320/ec.png',
  },
  {
    label: '+372',
    value: '+372',
    flag: 'https://flagcdn.com/w320/ee.png',
  },
  {
    label: '+20',
    value: '+20',
    flag: 'https://flagcdn.com/w320/eg.png',
  },
  {
    label: '+212',
    value: '+212',
    flag: 'https://flagcdn.com/w320/eh.png',
  },
  {
    label: '+291',
    value: '+291',
    flag: 'https://flagcdn.com/w320/er.png',
  },
  {
    label: '+34',
    value: '+34',
    flag: 'https://flagcdn.com/w320/es.png',
  },
  {
    label: '+251',
    value: '+251',
    flag: 'https://flagcdn.com/w320/et.png',
  },
  {
    label: '+358',
    value: '+358',
    flag: 'https://flagcdn.com/w320/fi.png',
  },
  {
    label: '+679',
    value: '+679',
    flag: 'https://flagcdn.com/w320/fj.png',
  },
  {
    label: '+691',
    value: '+691',
    flag: 'https://flagcdn.com/w320/fm.png',
  },
  {
    label: '+298',
    value: '+298',
    flag: 'https://flagcdn.com/w320/fo.png',
  },
  {
    label: '+33',
    value: '+33',
    flag: 'https://flagcdn.com/w320/fr.png',
  },
  {
    label: '+241',
    value: '+241',
    flag: 'https://flagcdn.com/w320/ga.png',
  },
  {
    label: '+44',
    value: '+44',
    flag: 'https://flagcdn.com/w320/gb.png',
  },
  {
    label: '+1-473',
    value: '+1-473',
    flag: 'https://flagcdn.com/w320/gd.png',
  },
  {
    label: '+995',
    value: '+995',
    flag: 'https://flagcdn.com/w320/ge.png',
  },
  {
    label: '+233',
    value: '+233',
    flag: 'https://flagcdn.com/w320/gh.png',
  },
  {
    label: '+350',
    value: '+350',
    flag: 'https://flagcdn.com/w320/gi.png',
  },
  {
    label: '+224',
    value: '+224',
    flag: 'https://flagcdn.com/w320/gn.png',
  },
  {
    label: '+245',
    value: '+245',
    flag: 'https://flagcdn.com/w320/gw.png',
  },
  {
    label: '+592',
    value: '+592',
    flag: 'https://flagcdn.com/w320/gy.png',
  },
  {
    label: '+852',
    value: '+852',
    flag: 'https://flagcdn.com/w320/hk.png',
  },
  {
    label: '+504',
    value: '+504',
    flag: 'https://flagcdn.com/w320/hn.png',
  },
  {
    label: '+385',
    value: '+385',
    flag: 'https://flagcdn.com/w320/hr.png',
  },
  {
    label: '+509',
    value: '+509',
    flag: 'https://flagcdn.com/w320/ht.png',
  },
  {
    label: '+36',
    value: '+36',
    flag: 'https://flagcdn.com/w320/hu.png',
  },
  {
    label: '+62',
    value: '+62',
    flag: 'https://flagcdn.com/w320/id.png',
  },
  {
    label: '+353',
    value: '+353',
    flag: 'https://flagcdn.com/w320/ie.png',
  },
  {
    label: '+972',
    value: '+972',
    flag: 'https://flagcdn.com/w320/il.png',
  },
  {
    label: '+91',
    value: '+91',
    flag: 'https://flagcdn.com/w320/in.png',
  },
  {
    label: '+964',
    value: '+964',
    flag: 'https://flagcdn.com/w320/iq.png',
  },
  {
    label: '+98',
    value: '+98',
    flag: 'https://flagcdn.com/w320/ir.png',
  },
  {
    label: '+354',
    value: '+354',
    flag: 'https://flagcdn.com/w320/is.png',
  },
  {
    label: '+39',
    value: '+39',
    flag: 'https://flagcdn.com/w320/it.png',
  },
  {
    label: '+1-876',
    value: '+1-876',
    flag: 'https://flagcdn.com/w320/jm.png',
  },
  {
    label: '+962',
    value: '+962',
    flag: 'https://flagcdn.com/w320/jo.png',
  },
  {
    label: '+81',
    value: '+81',
    flag: 'https://flagcdn.com/w320/jp.png',
  },
  {
    label: '+254',
    value: '+254',
    flag: 'https://flagcdn.com/w320/ke.png',
  },
  {
    label: '+996',
    value: '+996',
    flag: 'https://flagcdn.com/w320/kg.png',
  },
  {
    label: '+855',
    value: '+855',
    flag: 'https://flagcdn.com/w320/kh.png',
  },
  {
    label: '+686',
    value: '+686',
    flag: 'https://flagcdn.com/w320/ki.png',
  },
  {
    label: '+269',
    value: '+269',
    flag: 'https://flagcdn.com/w320/km.png',
  },
  {
    label: '+1-869',
    value: '+1-869',
    flag: 'https://flagcdn.com/w320/kn.png',
  },
  {
    label: '+850',
    value: '+850',
    flag: 'https://flagcdn.com/w320/kp.png',
  },
  {
    label: '+82',
    value: '+82',
    flag: 'https://flagcdn.com/w320/kr.png',
  },
  {
    label: '+965',
    value: '+965',
    flag: 'https://flagcdn.com/w320/kw.png',
  },
  {
    label: '+1-345',
    value: '+1-345',
    flag: 'https://flagcdn.com/w320/ky.png',
  },
  {
    label: '+7',
    value: '+7',
    flag: 'https://flagcdn.com/w320/kz.png',
  },
  {
    label: '+856',
    value: '+856',
    flag: 'https://flagcdn.com/w320/la.png',
  },
  {
    label: '+961',
    value: '+961',
    flag: 'https://flagcdn.com/w320/lb.png',
  },
  {
    label: '+1-758',
    value: '+1-758',
    flag: 'https://flagcdn.com/w320/lc.png',
  },
  {
    label: '+423',
    value: '+423',
    flag: 'https://flagcdn.com/w320/li.png',
  },
  {
    label: '+94',
    value: '+94',
    flag: 'https://flagcdn.com/w320/lk.png',
  },
  {
    label: '+231',
    value: '+231',
    flag: 'https://flagcdn.com/w320/lr.png',
  },
  {
    label: '+266',
    value: '+266',
    flag: 'https://flagcdn.com/w320/ls.png',
  },
  {
    label: '+370',
    value: '+370',
    flag: 'https://flagcdn.com/w320/lt.png',
  },
  {
    label: '+352',
    value: '+352',
    flag: 'https://flagcdn.com/w320/lu.png',
  },
  {
    label: '+371',
    value: '+371',
    flag: 'https://flagcdn.com/w320/lv.png',
  },
  {
    label: '+218',
    value: '+218',
    flag: 'https://flagcdn.com/w320/ly.png',
  },
  {
    label: '+212',
    value: '+212',
    flag: 'https://flagcdn.com/w320/ma.png',
  },
  {
    label: '+377',
    value: '+377',
    flag: 'https://flagcdn.com/w320/mc.png',
  },
  {
    label: '+373',
    value: '+373',
    flag: 'https://flagcdn.com/w320/md.png',
  },
  {
    label: '+382',
    value: '+382',
    flag: 'https://flagcdn.com/w320/me.png',
  },
  {
    label: '+590',
    value: '+590',
    flag: 'https://flagcdn.com/w320/mf.png',
  },
  {
    label: '+261',
    value: '+261',
    flag: 'https://flagcdn.com/w320/mg.png',
  },
  {
    label: '+692',
    value: '+692',
    flag: 'https://flagcdn.com/w320/mh.png',
  },
  {
    label: '+389',
    value: '+389',
    flag: 'https://flagcdn.com/w320/mk.png',
  },
  {
    label: '+223',
    value: '+223',
    flag: 'https://flagcdn.com/w320/ml.png',
  },
  {
    label: '+95',
    value: '+95',
    flag: 'https://flagcdn.com/w320/mm.png',
  },
  {
    label: '+976',
    value: '+976',
    flag: 'https://flagcdn.com/w320/mn.png',
  },
  {
    label: '+853',
    value: '+853',
    flag: 'https://flagcdn.com/w320/mo.png',
  },
  {
    label: '+1-670',
    value: '+1-670',
    flag: 'https://flagcdn.com/w320/mp.png',
  },
  {
    label: '+596',
    value: '+596',
    flag: 'https://flagcdn.com/w320/mq.png',
  },
  {
    label: '+222',
    value: '+222',
    flag: 'https://flagcdn.com/w320/mr.png',
  },
  {
    label: '+1-664',
    value: '+1-664',
    flag: 'https://flagcdn.com/w320/ms.png',
  },
  {
    label: '+356',
    value: '+356',
    flag: 'https://flagcdn.com/w320/mt.png',
  },
  {
    label: '+230',
    value: '+230',
    flag: 'https://flagcdn.com/w320/mu.png',
  },
  {
    label: '+960',
    value: '+960',
    flag: 'https://flagcdn.com/w320/mv.png',
  },
  {
    label: '+265',
    value: '+265',
    flag: 'https://flagcdn.com/w320/mw.png',
  },
  {
    label: '+52',
    value: '+52',
    flag: 'https://flagcdn.com/w320/mx.png',
  },
  {
    label: '+60',
    value: '+60',
    flag: 'https://flagcdn.com/w320/my.png',
  },
  {
    label: '+258',
    value: '+258',
    flag: 'https://flagcdn.com/w320/mz.png',
  },
  {
    label: '+264',
    value: '+264',
    flag: 'https://flagcdn.com/w320/na.png',
  },
  {
    label: '+687',
    value: '+687',
    flag: 'https://flagcdn.com/w320/nc.png',
  },
  {
    label: '+227',
    value: '+227',
    flag: 'https://flagcdn.com/w320/ne.png',
  },
  {
    label: '+672',
    value: '+672',
    flag: 'https://flagcdn.com/w320/nf.png',
  },
  {
    label: '+234',
    value: '+234',
    flag: 'https://flagcdn.com/w320/ng.png',
  },
  {
    label: '+505',
    value: '+505',
    flag: 'https://flagcdn.com/w320/ni.png',
  },
  {
    label: '+31',
    value: '+31',
    flag: 'https://flagcdn.com/w320/nl.png',
  },
  {
    label: '+47',
    value: '+47',
    flag: 'https://flagcdn.com/w320/no.png',
  },
  {
    label: '+977',
    value: '+977',
    flag: 'https://flagcdn.com/w320/np.png',
  },
  {
    label: '+674',
    value: '+674',
    flag: 'https://flagcdn.com/w320/nr.png',
  },
  {
    label: '+683',
    value: '+683',
    flag: 'https://flagcdn.com/w320/nu.png',
  },
  {
    label: '+64',
    value: '+64',
    flag: 'https://flagcdn.com/w320/nz.png',
  },
  {
    label: '+968',
    value: '+968',
    flag: 'https://flagcdn.com/w320/om.png',
  },
  {
    label: '+507',
    value: '+507',
    flag: 'https://flagcdn.com/w320/pa.png',
  },
  {
    label: '+51',
    value: '+51',
    flag: 'https://flagcdn.com/w320/pe.png',
  },
  {
    label: '+689',
    value: '+689',
    flag: 'https://flagcdn.com/w320/pf.png',
  },
  {
    label: '+675',
    value: '+675',
    flag: 'https://flagcdn.com/w320/pg.png',
  },
  {
    label: '+63',
    value: '+63',
    flag: 'https://flagcdn.com/w320/ph.png',
  },
  {
    label: '+92',
    value: '+92',
    flag: 'https://flagcdn.com/w320/pk.png',
  },
  {
    label: '+48',
    value: '+48',
    flag: 'https://flagcdn.com/w320/pl.png',
  },
  {
    label: '+508',
    value: '+508',
    flag: 'https://flagcdn.com/w320/pm.png',
  },
  {
    label: '+64',
    value: '+64',
    flag: 'https://flagcdn.com/w320/pn.png',
  },
  {
    label: '+1-787',
    value: '+1-787',
    flag: 'https://flagcdn.com/w320/pr.png',
  },
  {
    label: '+351',
    value: '+351',
    flag: 'https://flagcdn.com/w320/pt.png',
  },
  {
    label: '+680',
    value: '+680',
    flag: 'https://flagcdn.com/w320/pw.png',
  },
  {
    label: '+595',
    value: '+595',
    flag: 'https://flagcdn.com/w320/py.png',
  },
  {
    label: '+974',
    value: '+974',
    flag: 'https://flagcdn.com/w320/qa.png',
  },
  {
    label: '+262',
    value: '+262',
    flag: 'https://flagcdn.com/w320/re.png',
  },
  {
    label: '+40',
    value: '+40',
    flag: 'https://flagcdn.com/w320/ro.png',
  },
  {
    label: '+381',
    value: '+381',
    flag: 'https://flagcdn.com/w320/rs.png',
  },
  {
    label: '+7',
    value: '+7',
    flag: 'https://flagcdn.com/w320/ru.png',
  },
  {
    label: '+250',
    value: '+250',
    flag: 'https://flagcdn.com/w320/rw.png',
  },
  {
    label: '+966',
    value: '+966',
    flag: 'https://flagcdn.com/w320/sa.png',
  },
  {
    label: '+677',
    value: '+677',
    flag: 'https://flagcdn.com/w320/sb.png',
  },
  {
    label: '+248',
    value: '+248',
    flag: 'https://flagcdn.com/w320/sc.png',
  },
  {
    label: '+249',
    value: '+249',
    flag: 'https://flagcdn.com/w320/sd.png',
  },
  {
    label: '+46',
    value: '+46',
    flag: 'https://flagcdn.com/w320/se.png',
  },
  {
    label: '+65',
    value: '+65',
    flag: 'https://flagcdn.com/w320/sg.png',
  },
  {
    label: '+290',
    value: '+290',
    flag: 'https://flagcdn.com/w320/sh.png',
  },
  {
    label: '+386',
    value: '+386',
    flag: 'https://flagcdn.com/w320/si.png',
  },
  {
    label: '+47',
    value: '+47',
    flag: 'https://flagcdn.com/w320/sj.png',
  },
  {
    label: '+421',
    value: '+421',
    flag: 'https://flagcdn.com/w320/sk.png',
  },
  {
    label: '+232',
    value: '+232',
    flag: 'https://flagcdn.com/w320/sl.png',
  },
  {
    label: '+378',
    value: '+378',
    flag: 'https://flagcdn.com/w320/sm.png',
  },
  {
    label: '+221',
    value: '+221',
    flag: 'https://flagcdn.com/w320/sn.png',
  },
  {
    label: '+252',
    value: '+252',
    flag: 'https://flagcdn.com/w320/so.png',
  },
  {
    label: '+597',
    value: '+597',
    flag: 'https://flagcdn.com/w320/sr.png',
  },
  {
    label: '+211',
    value: '+211',
    flag: 'https://flagcdn.com/w320/ss.png',
  },
  {
    label: '+239',
    value: '+239',
    flag: 'https://flagcdn.com/w320/st.png',
  },
  {
    label: '+503',
    value: '+503',
    flag: 'https://flagcdn.com/w320/sv.png',
  },
  {
    label: '+1-721',
    value: '+1-721',
    flag: 'https://flagcdn.com/w320/sx.png',
  },
  {
    label: '+963',
    value: '+963',
    flag: 'https://flagcdn.com/w320/sy.png',
  },
  {
    label: '+268',
    value: '+268',
    flag: 'https://flagcdn.com/w320/sz.png',
  },
  {
    label: '+1-649',
    value: '+1-649',
    flag: 'https://flagcdn.com/w320/tc.png',
  },
  {
    label: '+235',
    value: '+235',
    flag: 'https://flagcdn.com/w320/td.png',
  },
  {
    label: '+262',
    value: '+262',
    flag: 'https://flagcdn.com/w320/tf.png',
  },
  {
    label: '+228',
    value: '+228',
    flag: 'https://flagcdn.com/w320/tg.png',
  },
  {
    label: '+66',
    value: '+66',
    flag: 'https://flagcdn.com/w320/th.png',
  },
  {
    label: '+992',
    value: '+992',
    flag: 'https://flagcdn.com/w320/tj.png',
  },
  {
    label: '+690',
    value: '+690',
    flag: 'https://flagcdn.com/w320/tk.png',
  },
  {
    label: '+670',
    value: '+670',
    flag: 'https://flagcdn.com/w320/tl.png',
  },
  {
    label: '+993',
    value: '+993',
    flag: 'https://flagcdn.com/w320/tm.png',
  },
  {
    label: '+216',
    value: '+216',
    flag: 'https://flagcdn.com/w320/tn.png',
  },
  {
    label: '+676',
    value: '+676',
    flag: 'https://flagcdn.com/w320/to.png',
  },
  {
    label: '+90',
    value: '+90',
    flag: 'https://flagcdn.com/w320/tr.png',
  },
  {
    label: '+1-868',
    value: '+1-868',
    flag: 'https://flagcdn.com/w320/tt.png',
  },
  {
    label: '+688',
    value: '+688',
    flag: 'https://flagcdn.com/w320/tv.png',
  },
  {
    label: '+255',
    value: '+255',
    flag: 'https://flagcdn.com/w320/tz.png',
  },
  {
    label: '+380',
    value: '+380',
    flag: 'https://flagcdn.com/w320/ua.png',
  },
  {
    label: 'U+971',
    value: '+971',
    flag: 'https://flagcdn.com/w320/ae.png',
  },
  {
    label: '+256',
    value: '+256',
    flag: 'https://flagcdn.com/w320/ug.png',
  },
  {
    label: '+598',
    value: '+598',
    flag: 'https://flagcdn.com/w320/uy.png',
  },
  {
    label: '+998',
    value: '+998',
    flag: 'https://flagcdn.com/w320/uz.png',
  },
  {
    label: '+379',
    value: '+379',
    flag: 'https://flagcdn.com/w320/va.png',
  },
  {
    label: '+1-784',
    value: '+1-784',
    flag: 'https://flagcdn.com/w320/vc.png',
  },
  {
    label: '+58',
    value: '+58',
    flag: 'https://flagcdn.com/w320/ve.png',
  },
  {
    label: '+1-284',
    value: '+1-284',
    flag: 'https://flagcdn.com/w320/vg.png',
  },
  {
    label: '+1-340',
    value: '+1-340',
    flag: 'https://flagcdn.com/w320/vi.png',
  },
  {
    label: '+84',
    value: '+84',
    flag: 'https://flagcdn.com/w320/vn.png',
  },
  {
    label: '+678',
    value: '+678',
    flag: 'https://flagcdn.com/w320/vu.png',
  },
  {
    label: '+681',
    value: '+681',
    flag: 'https://flagcdn.com/w320/wf.png',
  },
  {
    label: '+685',
    value: '+685',
    flag: 'https://flagcdn.com/w320/ws.png',
  },
  {
    label: '+967',
    value: '+967',
    flag: 'https://flagcdn.com/w320/ye.png',
  },
  {
    label: '+262',
    value: '+262',
    flag: 'https://flagcdn.com/w320/yt.png',
  },
  {
    label: '+27',
    value: '+27',
    flag: 'https://flagcdn.com/w320/za.png',
  },
  {
    label: '+260',
    value: '+260',
    flag: 'https://flagcdn.com/w320/zm.png',
  },
  {
    label: '+263',
    value: '+263',
    flag: 'https://flagcdn.com/w320/zw.png',
  },
];

export const DEFAULT_COUNTRY_CODE = {
  label: '+61',
  value: '+61',
  flag: 'https://flagcdn.com/w320/au.png',
};

export const DEFAULT_COUNTRY = {
  value: '1',
  label: 'Australia',
};
