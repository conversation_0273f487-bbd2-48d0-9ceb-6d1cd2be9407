import { createSlice } from '@reduxjs/toolkit';

const initState = {
  enable: false,
  manualEnable: false,
  message: "You don't have access",
};

export const slice = createSlice({
  name: 'Toast',
  initialState: { ...initState },
  reducers: {
    showToast: (state, action) => {
      state.enable = true;
      state.message = action.payload;
    },
    hideToast: (state) => {
      state.enable = false;
      state.message = '';
    },
    showManualToast: (state, action) => {
      state.manualEnable = true;
      state.message = action.payload;
    },
    hideManualToast: (state) => {
      state.manualEnable = false;
      state.message = '';
    },
  },
});

export const { showToast, hideToast, showManualToast, hideManualToast } =
  slice.actions;

export default slice.reducer;
