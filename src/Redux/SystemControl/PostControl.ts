import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { getAllPostViaRedux, PostsResponseData } from '.';

// Define proper types for the state
interface InitialStateData {
  isLoadingPostData: boolean;
  postData: PostsResponseData[];
  [key: string]: boolean | PostsResponseData[] | undefined; // Allow dynamic keys
}

// Define type for reset action payload
interface ResetPayload {
  reduxKey: keyof InitialStateData;
  needToFetch?: boolean;
}

const initialState: InitialStateData = {
  isLoadingPostData: false,
  postData: [],
};

const slice = createSlice({
  name: 'PostControl',
  initialState,
  reducers: {
    resetPostData: (state, action: PayloadAction<ResetPayload>) => {
      const key = action.payload.reduxKey;
      if (key in state && typeof state[key] === 'object') {
        // Type assertion since we know this property exists
        (state[key] as { needToFetch?: boolean }).needToFetch = true;
      }
    },
  },
  extraReducers(builder) {
    builder
      .addCase(getAllPostViaRedux.pending, (state) => {
        state.isLoadingPostData = true;
      })
      .addCase(
        getAllPostViaRedux.fulfilled,
        (state, action: PayloadAction<PostsResponseData[]>) => {
          state.isLoadingPostData = false;
          state.postData = action.payload; // Set the postData directly with the array of posts
        }
      )
      .addCase(getAllPostViaRedux.rejected, (state) => {
        state.isLoadingPostData = false;
      });
  },
});

export const { resetPostData } = slice.actions;
export default slice.reducer;
