import CacheKeys from '@Helpers/CacheKeys';
import {
  viewUserBusinessData,
  updateBusinessData
} from '@Query/Services/enterprise.service';
import { useMutation, UseMutationOptions, useQuery } from 'react-query';

interface UseGetUserBusinessDataProps {
  orgId: number;
  enabled?: boolean;
  isOrgAdmin?: boolean;
}

type UpdateBusinessPayload = {
  formData: FormData;
  orgId: string;
  isOrgAdmin?: boolean;
};



export const useGetUserBusinessData = () => useQuery(CacheKeys.businessData, viewUserBusinessData);


export const useUpdateBusinessData = (
  options?: UseMutationOptions<any, any, UpdateBusinessPayload>
) => {
  return useMutation(
    ({ formData}: UpdateBusinessPayload) => {
      return updateBusinessData(formData);
    },
    {
      ...options,
    }
  );
};