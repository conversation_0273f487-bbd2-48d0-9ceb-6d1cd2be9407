import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const getUserAnalytics = async (data: any): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.GET_UPLOAD_COUNT + data);
};

export const getCategoryWiseAnalytics = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.GET_CATEGORY_WISE_QUESTION_COUNT + data);
};

export const getCategoryWiseIssueAnalytics = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.GET_CATEGORY_WISE_ISSUES_COUNT + data);
};

export const getRectificationAnalytics = async (
  data: any
): Promise<AxiosResponse> => {
  //Data => year,month,week, custom->start_date, end_date
  return api.get(API_PATHS.GET_RF_WISE_ISSUES_COUNT + data);
};
