import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const viewUserBusinessData = async ({
}: {
}): Promise<AxiosResponse> => {
  return api.get(API_PATHS.BUSINESS_DETAILS);
};

export const updateBusinessData = async (
  data: FormData,
): Promise<AxiosResponse> => {

  return api.patch(API_PATHS.BUSINESS_EDIT, {
    data: data as any,
    isMultipart: true,
  });
};
