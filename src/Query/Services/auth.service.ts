import Api from '@Helpers/Api';
import { API_PATHS } from '@Helpers/Constants';
import { AxiosResponse } from 'axios';

const api = new Api();

export const userLogin = async (data: {
  mobile_no: string;
  country_code: string;
  device_id: string;
  device_token: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.LOGIN, { data });
};

export const userLogout = async (): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.LOGOUT);
};

export const userDeleteAccount = async (): Promise<AxiosResponse> => {
  return api.delete(API_PATHS.DELETE_ACCOUNT);
};

export const userVerifyOtp = async (data: {
  verify_token: string;
  otp: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.VERIFY_OTP, { data });
};

export const userResendOtp = async (data: {
  verify_token: string;
  user_id: number;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.RESEND_OTP, { data });
};

export const userRegister = async (data: {
  first_name: string;
  last_name: string;
  email: string;
  mobile: string;
  country_code: string;
  device_id: string;
  device_token: string;
  country: string;
  state: string;
  license: string;
  format_id: string;
}): Promise<AxiosResponse> => {
  return api.post(API_PATHS.REGISTER, { data });
};

export const getCountryList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.COUNTRIES_LIST);
};

export const getStateList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.STATES_LIST + `1/states/`);
};

export const getTradeList = async (): Promise<AxiosResponse> => {
  return api.get(API_PATHS.TRADTE_LIST);
};
