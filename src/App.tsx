import { BrowserRouter, Route, Routes, useLocation } from 'react-router-dom';
import { Suspense, lazy, useEffect, useState } from 'react';
import { MainLayout, RegisterLayout } from '@Components/Layout';
import { MainLoader } from '@Components/Common';
import { PageNotFound } from '@Pages';
import { RoleGuard } from '@Helpers/Roleguard';
import { publicRoutes, privateRoutes } from './Config/Routes.Config';
import { PATHS } from './Config/Path.Config';
import AdobePdfViewer from './Pages/Forms&Documents/PDFViewer';
import Redirecting from './Pages/Redirecting';

const Subscribe = lazy(() => import('./Pages/Subscribe'));
import InternetConnectionMonitor from './Pages/InternetConnectionMonitor';
const TermsConditionsPublic = lazy(
  () => import('./Pages/TermsConditionsPublic')
);
const PrivacyPolicyPublic = lazy(() => import('./Pages/PrivacyPolicyPublic'));
const AboutUsPublic = lazy(() => import('./Pages/AboutUsPublic'));

interface LayoutDescription {
  title: string;
  subTitle: string;
  showBack: boolean;
}

const RegLayoutDesc: Record<string, LayoutDescription> = {
  '/login': {
    title: 'Welcome to REX',
    subTitle: 'Please enter your details',
    showBack: false,
  },
  '/sign-up': { title: 'Create your account', subTitle: '', showBack: false },
  '/verify-otp': {
    title: 'Verification',
    subTitle:
      'Please enter the OTP sent to the registered mobile number to verify your mobile number.',
    showBack: true,
  },
};

function App() {
  return (
    <BrowserRouter>
      <AppContent />
    </BrowserRouter>
  );
}

function AppContent() {

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const [isOnline, setIsOnline] = useState(navigator.onLine);

  useEffect(() => {
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  if (!isOnline) {
    return <InternetConnectionMonitor />;
  }

  return (
    <>
      <Routes>
        <Route element={<RoleGuard isPublicRoute={true} />}>
          {publicRoutes.map((route) => {
            const Component = route.element;
            return (
              <Route
                key={route.path}
                path={route.path}
                element={
                  <Suspense fallback={<MainLoader />}>
                    <RegisterLayout
                      title={RegLayoutDesc[route.path]?.title ?? ''}
                      subTitle={RegLayoutDesc[route.path]?.subTitle ?? ''}
                      showBack={RegLayoutDesc[route.path]?.showBack ?? false}
                    >
                      <Component />
                    </RegisterLayout>
                  </Suspense>
                }
              />
            );
          })}
        </Route>

        <Route element={<RoleGuard isPublicRoute={false} />}>
          {privateRoutes.map((route) => {
            const Component = route.element;
            return (
              <Route
                key={route.path}
                path={route.path}
                element={
                  <Suspense fallback={<MainLoader />}>
                    <MainLayout>
                      <Component />
                    </MainLayout>
                  </Suspense>
                }
              />
            );
          })}
          <Route
            path={PATHS.SUBSCRIBE}
            element={
              <Suspense fallback={<MainLoader />}>
                <Subscribe />
              </Suspense>
            }
          />
        </Route>

        <Route
          path={PATHS.TERMS_CONDITIONS_PUBLIC}
          element={
            <Suspense fallback={<MainLoader />}>
              <TermsConditionsPublic />
            </Suspense>
          }
        />
        <Route
          path={PATHS.PRIVACY_POLICY_PUBLIC}
          element={
            <Suspense fallback={<MainLoader />}>
              <PrivacyPolicyPublic />
            </Suspense>
          }
        />
        <Route
          path={PATHS.ABOUT_US_PUBLIC}
          element={
            <Suspense fallback={<MainLoader />}>
              <AboutUsPublic />
            </Suspense>
          }
        />

        <Route
          path={'/show-pdf'}
          element={
            // <Suspense fallback={<MainLoader />}>
            <AdobePdfViewer />
            // </Suspense>
          }
        />

        <Route path={'/redirection/:id/:token'} element={<Redirecting />} />

        {/* 404 Page */}
        <Route path="*" element={<PageNotFound />} />
      </Routes>
    </>
  );
}

export default App;
