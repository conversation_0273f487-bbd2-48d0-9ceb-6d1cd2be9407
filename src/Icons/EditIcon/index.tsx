import React from 'react';

const EditIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 25 25" fill="none">
      <path
        d="M24.4138 6.17122L18.8288 0.584971C18.643 0.399206 18.4225 0.251846 18.1799 0.151308C17.9372 0.0507701 17.6771 -0.000976562 17.4144 -0.000976562C17.1517 -0.000976562 16.8916 0.0507701 16.6489 0.151308C16.4062 0.251846 16.1857 0.399206 16 0.584971L0.58626 16C0.399734 16.185 0.251852 16.4053 0.151209 16.648C0.0505661 16.8907 -0.000829299 17.151 1.01187e-05 17.4137V23C1.01187e-05 23.5304 0.210724 24.0391 0.585796 24.4142C0.960869 24.7893 1.46958 25 2.00001 25H7.58626C7.849 25.0008 8.10929 24.9494 8.35199 24.8488C8.59469 24.7481 8.81497 24.6002 9.00001 24.4137L24.4138 8.99997C24.5995 8.81425 24.7469 8.59375 24.8474 8.35107C24.948 8.10839 24.9997 7.84828 24.9997 7.5856C24.9997 7.32291 24.948 7.0628 24.8474 6.82012C24.7469 6.57744 24.5995 6.35695 24.4138 6.17122ZM7.58626 23H2.00001V17.4137L13 6.41372L18.5863 12L7.58626 23ZM20 10.585L14.4138 4.99997L17.4138 1.99997L23 7.58497L20 10.585Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default EditIcon;
