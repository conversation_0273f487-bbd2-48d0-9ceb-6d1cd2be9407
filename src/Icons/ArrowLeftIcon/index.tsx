import React from 'react';

// Define the props interface for ArrowLeft component
interface ArrowLeftProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number;
  height?: string | number;
  stroke?: string;
}

const ArrowLeft: React.FC<ArrowLeftProps> = (props) => {
  return (
    <svg
      width={props?.width ?? '24'}
      height={props?.height ?? '24'}
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M19 12H5M5 12L12 19M5 12L12 5"
        stroke={props?.stroke ?? '#667085'}
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default ArrowLeft;
