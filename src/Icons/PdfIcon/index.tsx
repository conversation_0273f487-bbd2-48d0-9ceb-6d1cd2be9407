import React from 'react';

const SearchIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 18 19" fill="none">
      <path
        d="M17.5 12.75C17.5 12.9489 17.421 13.1397 17.2803 13.2803C17.1397 13.421 16.9489 13.5 16.75 13.5H14.5V15H16C16.1989 15 16.3897 15.079 16.5303 15.2197C16.671 15.3603 16.75 15.5511 16.75 15.75C16.75 15.9489 16.671 16.1397 16.5303 16.2803C16.3897 16.421 16.1989 16.5 16 16.5H14.5V18C14.5 18.1989 14.421 18.3897 14.2803 18.5303C14.1397 18.671 13.9489 18.75 13.75 18.75C13.5511 18.75 13.3603 18.671 13.2197 18.5303C13.079 18.3897 13 18.1989 13 18V12.75C13 12.5511 13.079 12.3603 13.2197 12.2197C13.3603 12.079 13.5511 12 13.75 12H16.75C16.9489 12 17.1397 12.079 17.2803 12.2197C17.421 12.3603 17.5 12.5511 17.5 12.75ZM5.125 14.625C5.125 15.3212 4.84844 15.9889 4.35616 16.4812C3.86387 16.9734 3.19619 17.25 2.5 17.25H1.75V18C1.75 18.1989 1.67098 18.3897 1.53033 18.5303C1.38968 18.671 1.19891 18.75 1 18.75C0.801088 18.75 0.610322 18.671 0.46967 18.5303C0.329018 18.3897 0.25 18.1989 0.25 18V12.75C0.25 12.5511 0.329018 12.3603 0.46967 12.2197C0.610322 12.079 0.801088 12 1 12H2.5C3.19619 12 3.86387 12.2766 4.35616 12.7688C4.84844 13.2611 5.125 13.9288 5.125 14.625ZM3.625 14.625C3.625 14.3266 3.50647 14.0405 3.2955 13.8295C3.08452 13.6185 2.79837 13.5 2.5 13.5H1.75V15.75H2.5C2.79837 15.75 3.08452 15.6315 3.2955 15.4205C3.50647 15.2095 3.625 14.9234 3.625 14.625ZM11.875 15.375C11.875 16.2701 11.5194 17.1285 10.8865 17.7615C10.2535 18.3944 9.39511 18.75 8.5 18.75H7C6.80109 18.75 6.61032 18.671 6.46967 18.5303C6.32902 18.3897 6.25 18.1989 6.25 18V12.75C6.25 12.5511 6.32902 12.3603 6.46967 12.2197C6.61032 12.079 6.80109 12 7 12H8.5C9.39511 12 10.2535 12.3556 10.8865 12.9885C11.5194 13.6215 11.875 14.4799 11.875 15.375ZM10.375 15.375C10.375 14.8777 10.1775 14.4008 9.82583 14.0492C9.47419 13.6975 8.99728 13.5 8.5 13.5H7.75V17.25H8.5C8.99728 17.25 9.47419 17.0525 9.82583 16.7008C10.1775 16.3492 10.375 15.8723 10.375 15.375ZM0.25 9V2.25C0.25 1.85218 0.408035 1.47064 0.68934 1.18934C0.970644 0.908035 1.35218 0.75 1.75 0.75H10.75C10.8485 0.749923 10.9461 0.769257 11.0371 0.806898C11.1282 0.844539 11.2109 0.899749 11.2806 0.969375L16.5306 6.21938C16.6003 6.28908 16.6555 6.37182 16.6931 6.46286C16.7307 6.55391 16.7501 6.65148 16.75 6.75V9C16.75 9.19891 16.671 9.38968 16.5303 9.53033C16.3897 9.67098 16.1989 9.75 16 9.75C15.8011 9.75 15.6103 9.67098 15.4697 9.53033C15.329 9.38968 15.25 9.19891 15.25 9V7.5H10.75C10.5511 7.5 10.3603 7.42098 10.2197 7.28033C10.079 7.13968 10 6.94891 10 6.75V2.25H1.75V9C1.75 9.19891 1.67098 9.38968 1.53033 9.53033C1.38968 9.67098 1.19891 9.75 1 9.75C0.801088 9.75 0.610322 9.67098 0.46967 9.53033C0.329018 9.38968 0.25 9.19891 0.25 9ZM11.5 6H14.1897L11.5 3.31031V6Z"
        fill={props?.fill ?? '#FFA033'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1563_436"
          x1="8.875"
          y1="0.75"
          x2="8.875"
          y2="18.75"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color={props?.fill ?? '#FFA033'} />
          <stop offset="1" stop-color={props?.fill ?? '#FF8800'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default SearchIcon;
