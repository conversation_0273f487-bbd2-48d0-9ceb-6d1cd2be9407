import React from 'react';

const MenuIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={props?.width ?? '16'}
      height={props?.height ?? '4'}
      viewBox="0 0 16 4"
      fill="none"
    >
      <path
        d="M9.5 2C9.5 2.29667 9.41203 2.58668 9.24721 2.83336C9.08238 3.08003 8.84812 3.27229 8.57403 3.38582C8.29994 3.49935 7.99834 3.52906 7.70737 3.47118C7.41639 3.4133 7.14912 3.27044 6.93934 3.06066C6.72956 2.85088 6.5867 2.58361 6.52882 2.29264C6.47094 2.00166 6.50065 1.70006 6.61418 1.42598C6.72771 1.15189 6.91997 0.917618 7.16665 0.752796C7.41332 0.587974 7.70333 0.5 8 0.5C8.39783 0.5 8.77936 0.658036 9.06066 0.93934C9.34197 1.22064 9.5 1.60218 9.5 2ZM1.625 0.5C1.32833 0.5 1.03832 0.587974 0.791646 0.752796C0.544972 0.917618 0.352713 1.15189 0.239181 1.42598C0.12565 1.70006 0.0959449 2.00166 0.153823 2.29264C0.211701 2.58361 0.354562 2.85088 0.564341 3.06066C0.774119 3.27044 1.04139 3.4133 1.33237 3.47118C1.62334 3.52906 1.92494 3.49935 2.19903 3.38582C2.47311 3.27229 2.70738 3.08003 2.87221 2.83336C3.03703 2.58668 3.125 2.29667 3.125 2C3.125 1.60218 2.96697 1.22064 2.68566 0.93934C2.40436 0.658036 2.02283 0.5 1.625 0.5ZM14.375 0.5C14.0783 0.5 13.7883 0.587974 13.5416 0.752796C13.295 0.917618 13.1027 1.15189 12.9892 1.42598C12.8757 1.70006 12.8459 2.00166 12.9038 2.29264C12.9617 2.58361 13.1046 2.85088 13.3143 3.06066C13.5241 3.27044 13.7914 3.4133 14.0824 3.47118C14.3733 3.52906 14.6749 3.49935 14.949 3.38582C15.2231 3.27229 15.4574 3.08003 15.6222 2.83336C15.787 2.58668 15.875 2.29667 15.875 2C15.875 1.60218 15.717 1.22064 15.4357 0.93934C15.1544 0.658036 14.7728 0.5 14.375 0.5Z"
        fill={props?.fill ?? '#333333'}
      />
    </svg>
  );
};

export default MenuIcon;
