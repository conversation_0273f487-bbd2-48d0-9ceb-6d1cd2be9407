import React from 'react';

const DatabaseIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 24 27" fill="none">
      <path
        d="M12 0.5C5.27125 0.5 0 3.575 0 7.5V19.5C0 23.425 5.27125 26.5 12 26.5C18.7288 26.5 24 23.425 24 19.5V7.5C24 3.575 18.7288 0.5 12 0.5ZM22 13.5C22 14.7025 21.015 15.9288 19.2987 16.865C17.3662 17.9188 14.7738 18.5 12 18.5C9.22625 18.5 6.63375 17.9188 4.70125 16.865C2.985 15.9288 2 14.7025 2 13.5V11.42C4.1325 13.295 7.77875 14.5 12 14.5C16.2213 14.5 19.8675 13.29 22 11.42V13.5ZM4.70125 4.135C6.63375 3.08125 9.22625 2.5 12 2.5C14.7738 2.5 17.3662 3.08125 19.2987 4.135C21.015 5.07125 22 6.2975 22 7.5C22 8.7025 21.015 9.92875 19.2987 10.865C17.3662 11.9187 14.7738 12.5 12 12.5C9.22625 12.5 6.63375 11.9187 4.70125 10.865C2.985 9.92875 2 8.7025 2 7.5C2 6.2975 2.985 5.07125 4.70125 4.135ZM19.2987 22.865C17.3662 23.9188 14.7738 24.5 12 24.5C9.22625 24.5 6.63375 23.9188 4.70125 22.865C2.985 21.9287 2 20.7025 2 19.5V17.42C4.1325 19.295 7.77875 20.5 12 20.5C16.2213 20.5 19.8675 19.29 22 17.42V19.5C22 20.7025 21.015 21.9287 19.2987 22.865Z"
        fill={props?.fill ?? 'url(#paint0_linear_1462_513)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_513"
          x1="12"
          y1="0.5"
          x2="12"
          y2="26.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#FFA033'} />
          <stop offset="1" stopColor={props?.fill ?? '#FF8800'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default DatabaseIcon;
