import React from 'react';

const VideoIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 23" fill="none">
      <path
        d="M17.555 8.6675L11.555 4.6675C11.4044 4.567 11.2293 4.50928 11.0484 4.50052C10.8675 4.49176 10.6877 4.53227 10.528 4.61774C10.3684 4.70321 10.2349 4.83043 10.1419 4.98581C10.0489 5.1412 9.99988 5.31891 10 5.5V13.5C9.99988 13.6811 10.0489 13.8588 10.1419 14.0142C10.2349 14.1696 10.3684 14.2968 10.528 14.3823C10.6877 14.4677 10.8675 14.5082 11.0484 14.4995C11.2293 14.4907 11.4044 14.433 11.555 14.3325L17.555 10.3325C17.6922 10.2412 17.8047 10.1175 17.8825 9.97222C17.9603 9.82698 18.001 9.66477 18.001 9.5C18.001 9.33523 17.9603 9.17302 17.8825 9.02778C17.8047 8.88255 17.6922 8.75878 17.555 8.6675ZM12 11.6313V7.375L15.1975 9.5L12 11.6313ZM24 0.5H2C1.46957 0.5 0.960859 0.710714 0.585786 1.08579C0.210714 1.46086 0 1.96957 0 2.5V16.5C0 17.0304 0.210714 17.5391 0.585786 17.9142C0.960859 18.2893 1.46957 18.5 2 18.5H24C24.5304 18.5 25.0391 18.2893 25.4142 17.9142C25.7893 17.5391 26 17.0304 26 16.5V2.5C26 1.96957 25.7893 1.46086 25.4142 1.08579C25.0391 0.710714 24.5304 0.5 24 0.5ZM24 16.5H2V2.5H24V16.5ZM26 21.5C26 21.7652 25.8946 22.0196 25.7071 22.2071C25.5196 22.3946 25.2652 22.5 25 22.5H1C0.734784 22.5 0.48043 22.3946 0.292893 22.2071C0.105357 22.0196 0 21.7652 0 21.5C0 21.2348 0.105357 20.9804 0.292893 20.7929C0.48043 20.6054 0.734784 20.5 1 20.5H25C25.2652 20.5 25.5196 20.6054 25.7071 20.7929C25.8946 20.9804 26 21.2348 26 21.5Z"
        fill="url(#paint0_linear_1462_492)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_492"
          x1="13"
          y1="0.5"
          x2="13"
          y2="22.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#FFA033'} />
          <stop offset="1" stopColor={props?.fill ?? '#FF8800'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default VideoIcon;
