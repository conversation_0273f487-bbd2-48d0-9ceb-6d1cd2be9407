import React from 'react';

const SwmsIcon = (props: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg {...props}  viewBox="0 0 28 25" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M27.6 20.5113L16.6687 1.52758C16.3956 1.06249 16.0056 0.676856 15.5375 0.408908C15.0694 0.14096 14.5394 0 14 0C13.4606 0 12.9306 0.14096 12.4625 0.408908C11.9944 0.676856 11.6044 1.06249 11.3312 1.52758L0.399998 20.5113C0.137168 20.9612 -0.00134277 21.4728 -0.00134277 21.9938C-0.00134277 22.5148 0.137168 23.0265 0.399998 23.4763C0.669661 23.9442 1.05896 24.332 1.52795 24.5997C1.99694 24.8675 2.52873 25.0057 3.06875 25.0001H24.9312C25.4708 25.0052 26.0021 24.8669 26.4706 24.5991C26.9391 24.3314 27.328 23.9439 27.5975 23.4763C27.8607 23.0267 27.9997 22.5152 28.0001 21.9942C28.0005 21.4732 27.8624 20.9614 27.6 20.5113ZM25.8662 22.4751C25.771 22.6376 25.6341 22.7719 25.4698 22.8642C25.3055 22.9564 25.1197 23.0033 24.9312 23.0001H3.06875C2.88034 23.0033 2.69446 22.9564 2.53016 22.8642C2.36586 22.7719 2.22904 22.6376 2.13375 22.4751C2.04743 22.3289 2.00189 22.1623 2.00189 21.9926C2.00189 21.8228 2.04743 21.6562 2.13375 21.5101L13.065 2.52633C13.1622 2.36455 13.2997 2.23068 13.4639 2.13774C13.6282 2.04479 13.8138 1.99595 14.0025 1.99595C14.1912 1.99595 14.3768 2.04479 14.5411 2.13774C14.7053 2.23068 14.8428 2.36455 14.94 2.52633L25.8712 21.5101C25.9568 21.6567 26.0015 21.8235 26.0006 21.9933C25.9997 22.163 25.9533 22.3294 25.8662 22.4751ZM13 15.0001V10.0001C13 9.73487 13.1054 9.48051 13.2929 9.29298C13.4804 9.10544 13.7348 9.00008 14 9.00008C14.2652 9.00008 14.5196 9.10544 14.7071 9.29298C14.8946 9.48051 15 9.73487 15 10.0001V15.0001C15 15.2653 14.8946 15.5197 14.7071 15.7072C14.5196 15.8947 14.2652 16.0001 14 16.0001C13.7348 16.0001 13.4804 15.8947 13.2929 15.7072C13.1054 15.5197 13 15.2653 13 15.0001ZM15.5 19.5001C15.5 19.7968 15.412 20.0868 15.2472 20.3334C15.0824 20.5801 14.8481 20.7724 14.574 20.8859C14.2999 20.9994 13.9983 21.0291 13.7074 20.9713C13.4164 20.9134 13.1491 20.7705 12.9393 20.5607C12.7296 20.351 12.5867 20.0837 12.5288 19.7927C12.4709 19.5017 12.5006 19.2001 12.6142 18.9261C12.7277 18.652 12.92 18.4177 13.1666 18.2529C13.4133 18.0881 13.7033 18.0001 14 18.0001C14.3978 18.0001 14.7794 18.1581 15.0607 18.4394C15.342 18.7207 15.5 19.1023 15.5 19.5001Z"
                fill={props?.fill ?? `url(#paint0_linear_24_2358)`} />
            <defs>
                <linearGradient
                    id="paint0_linear_24_2358"
                    x1="12.7317"
                    y1="0"
                    x2="12.7317"
                    y2="25.0002"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color={props?.fill ?? "#FFA033"} />
                    <stop offset="1" stop-color={props?.fill ?? "#FF8800"} />
                </linearGradient>
            </defs>
        </svg>
    );
};

export default SwmsIcon;
