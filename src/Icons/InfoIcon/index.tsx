import React from 'react';

// Define the type for the component props
interface InfoIconProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number;
  height?: string | number;
  fill?: string;
}

const InfoIcon: React.FC<InfoIconProps> = (props) => {
  return (
    <svg {...props} viewBox="0 0 16 16" fill="none">
      <g clipPath="url(#clip0_231_61464)">
        <path
          d="M8.00004 10.6667V8.00004M8.00004 5.33337H8.00671M14.6667 8.00004C14.6667 11.6819 11.6819 14.6667 8.00004 14.6667C4.31814 14.6667 1.33337 11.6819 1.33337 8.00004C1.33337 4.31814 4.31814 1.33337 8.00004 1.33337C11.6819 1.33337 14.6667 4.31814 14.6667 8.00004Z"
          stroke={props?.stroke ?? '#667085'}
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_231_61464">
          <rect
            width={props.width ?? '16'}
            height={props.height ?? '16'}
            fill={props.fill ?? 'white'}
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default InfoIcon;
