import React from 'react';

const VideoFinishIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="86"
      height="86"
      viewBox="0 0 86 86"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <g filter="url(#filter0_i_349_5060)" data-figma-bg-blur-radius="17.9167">
        <circle cx="43" cy="43" r="40" fill="url(#paint0_linear_349_5060)" />
        <circle
          cx="43"
          cy="43"
          r="40.4479"
          stroke="url(#paint1_linear_349_5060)"
          stroke-opacity="0.2"
          stroke-width="0.895833"
        />
      </g>
      <path
        d="M59.3107 34.225L39.87 53.6656C39.7007 53.8355 39.4995 53.9704 39.278 54.0623C39.0564 54.1543 38.8189 54.2017 38.579 54.2017C38.3392 54.2017 38.1017 54.1543 37.8801 54.0623C37.6586 53.9704 37.4574 53.8355 37.2881 53.6656L28.7828 45.1603C28.6133 44.9908 28.4788 44.7895 28.387 44.568C28.2953 44.3465 28.248 44.1091 28.248 43.8694C28.248 43.6296 28.2953 43.3922 28.387 43.1707C28.4788 42.9492 28.6133 42.7479 28.7828 42.5784C28.9523 42.4089 29.1536 42.2744 29.3751 42.1826C29.5966 42.0909 29.834 42.0436 30.0738 42.0436C30.3135 42.0436 30.5509 42.0909 30.7724 42.1826C30.9939 42.2744 31.1952 42.4089 31.3647 42.5784L38.5806 49.7942L56.7317 31.6461C57.0741 31.3037 57.5385 31.1113 58.0227 31.1113C58.5069 31.1113 58.9713 31.3037 59.3137 31.6461C59.6561 31.9885 59.8484 32.4528 59.8484 32.937C59.8484 33.4213 59.6561 33.8856 59.3137 34.228L59.3107 34.225Z"
        fill="white"
      />
      <defs>
        <filter
          id="filter0_i_349_5060"
          x="-15.8122"
          y="-15.8122"
          width="117.624"
          height="117.624"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1.30832"
            operator="dilate"
            in="SourceAlpha"
            result="effect1_innerShadow_349_5060"
          />
          <feOffset dx="-0.654162" dy="0.654162" />
          <feGaussianBlur stdDeviation="0.327081" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_349_5060"
          />
        </filter>
        <clipPath
          id="bgblur_0_349_5060_clip_path"
          transform="translate(15.8122 15.8122)"
        >
          <circle cx="43" cy="43" r="40" />
        </clipPath>
        <linearGradient
          id="paint0_linear_349_5060"
          x1="43"
          y1="3"
          x2="43"
          y2="83"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFA033" />
          <stop offset="1" stop-color="#FF8800" />
        </linearGradient>
        <linearGradient
          id="paint1_linear_349_5060"
          x1="73.6452"
          y1="8.48387"
          x2="43"
          y2="83"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default VideoFinishIcon;
