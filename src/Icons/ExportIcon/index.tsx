import React from 'react';

const ExportIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 22 26" fill="none">
      <path
        d="M22 12.0001V24.0001C22 24.5305 21.7893 25.0392 21.4142 25.4143C21.0391 25.7893 20.5304 26.0001 20 26.0001H2C1.46957 26.0001 0.960859 25.7893 0.585786 25.4143C0.210714 25.0392 0 24.5305 0 24.0001V12.0001C0 11.4696 0.210714 10.9609 0.585786 10.5858C0.960859 10.2108 1.46957 10.0001 2 10.0001H5C5.26522 10.0001 5.51957 10.1054 5.70711 10.2929C5.89464 10.4805 6 10.7348 6 11.0001C6 11.2653 5.89464 11.5196 5.70711 11.7072C5.51957 11.8947 5.26522 12.0001 5 12.0001H2V24.0001H20V12.0001H17C16.7348 12.0001 16.4804 11.8947 16.2929 11.7072C16.1054 11.5196 16 11.2653 16 11.0001C16 10.7348 16.1054 10.4805 16.2929 10.2929C16.4804 10.1054 16.7348 10.0001 17 10.0001H20C20.5304 10.0001 21.0391 10.2108 21.4142 10.5858C21.7893 10.9609 22 11.4696 22 12.0001ZM6.7075 6.70755L10 3.4138V15.0001C10 15.2653 10.1054 15.5196 10.2929 15.7072C10.4804 15.8947 10.7348 16.0001 11 16.0001C11.2652 16.0001 11.5196 15.8947 11.7071 15.7072C11.8946 15.5196 12 15.2653 12 15.0001V3.4138L15.2925 6.70755C15.4801 6.8952 15.7346 7.00061 16 7.00061C16.2654 7.00061 16.5199 6.8952 16.7075 6.70755C16.8951 6.51991 17.0006 6.26542 17.0006 6.00005C17.0006 5.73469 16.8951 5.4802 16.7075 5.29255L11.7075 0.292554C11.6146 0.199578 11.5043 0.125819 11.3829 0.0754944C11.2615 0.0251701 11.1314 -0.000732422 11 -0.000732422C10.8686 -0.000732422 10.7385 0.0251701 10.6171 0.0754944C10.4957 0.125819 10.3854 0.199578 10.2925 0.292554L5.2925 5.29255C5.10486 5.4802 4.99944 5.73469 4.99944 6.00005C4.99944 6.26542 5.10486 6.51991 5.2925 6.70755C5.48014 6.89519 5.73464 7.00061 6 7.00061C6.26536 7.00061 6.51986 6.8952 6.7075 6.70755Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default ExportIcon;
