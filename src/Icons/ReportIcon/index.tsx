import React from 'react';

const ReportIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={props?.width ?? '18'}
      height={props?.height ?? '20'}
      viewBox="0 0 18 20"
      fill="none"
    >
      <path
        d="M0.25875 2.4386C0.178065 2.50854 0.113243 2.5949 0.0686202 2.69191C0.0239975 2.78891 0.000602508 2.89433 0 3.0011V18.7511C0 18.95 0.0790178 19.1408 0.21967 19.2814C0.360322 19.4221 0.551088 19.5011 0.75 19.5011C0.948912 19.5011 1.13968 19.4221 1.28033 19.2814C1.42098 19.1408 1.5 18.95 1.5 18.7511V14.6045C4.01156 12.6208 6.17531 13.6905 8.66719 14.9242C10.2047 15.6845 11.8603 16.5039 13.6359 16.5039C14.9419 16.5039 16.3116 16.0586 17.7441 14.8164C17.8247 14.7465 17.8896 14.6601 17.9342 14.5631C17.9788 14.4661 18.0022 14.3607 18.0028 14.2539V3.0011C18.0025 2.85715 17.9607 2.71633 17.8825 2.59546C17.8043 2.4746 17.693 2.37879 17.5618 2.31948C17.4306 2.26017 17.2852 2.23987 17.1428 2.26099C17.0004 2.28211 16.8671 2.34377 16.7587 2.4386C14.1337 4.71017 11.91 3.60954 9.33281 2.3336C6.66281 1.00985 3.63562 -0.487333 0.25875 2.4386ZM16.5 13.8995C13.9884 15.8833 11.8247 14.8127 9.33281 13.5799C6.98906 12.422 4.38187 11.1302 1.5 12.7924V3.35642C4.01156 1.37267 6.17531 2.44235 8.66719 3.67517C11.0109 4.83298 13.6191 6.12485 16.5 4.46267V13.8995Z"
        fill={props?.fill ?? '#FF1B1F'}
      />
    </svg>
  );
};

export default ReportIcon;
