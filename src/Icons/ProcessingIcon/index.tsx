import React from 'react';

const ProcessingIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 26 26" fill="none">
      <path d="M14 1V5C14 5.26522 13.8946 5.51957 13.7071 5.70711C13.5196 5.89464 13.2652 6 13 6C12.7348 6 12.4804 5.89464 12.2929 5.70711C12.1054 5.51957 12 5.26522 12 5V1C12 0.734784 12.1054 0.48043 12.2929 0.292893C12.4804 0.105357 12.7348 0 13 0C13.2652 0 13.5196 0.105357 13.7071 0.292893C13.8946 0.48043 14 0.734784 14 1ZM18.6562 8.34375C18.7877 8.34369 18.9178 8.31771 19.0392 8.26731C19.1606 8.2169 19.2709 8.14306 19.3638 8.05L22.1925 5.2225C22.3801 5.03486 22.4856 4.78036 22.4856 4.515C22.4856 4.24964 22.3801 3.99514 22.1925 3.8075C22.0049 3.61986 21.7504 3.51444 21.485 3.51444C21.2196 3.51444 20.9651 3.61986 20.7775 3.8075L17.95 6.63625C17.8101 6.77603 17.7147 6.95417 17.676 7.14814C17.6373 7.34211 17.657 7.54319 17.7326 7.72596C17.8082 7.90873 17.9363 8.06498 18.1007 8.17495C18.2651 8.28492 18.4585 8.34366 18.6562 8.34375ZM25 12H21C20.7348 12 20.4804 12.1054 20.2929 12.2929C20.1054 12.4804 20 12.7348 20 13C20 13.2652 20.1054 13.5196 20.2929 13.7071C20.4804 13.8946 20.7348 14 21 14H25C25.2652 14 25.5196 13.8946 25.7071 13.7071C25.8946 13.5196 26 13.2652 26 13C26 12.7348 25.8946 12.4804 25.7071 12.2929C25.5196 12.1054 25.2652 12 25 12ZM19.3638 17.95C19.1747 17.7704 18.9229 17.6717 18.6622 17.6751C18.4014 17.6784 18.1523 17.7835 17.9679 17.9679C17.7835 18.1523 17.6784 18.4014 17.6751 18.6622C17.6717 18.9229 17.7704 19.1747 17.95 19.3638L20.7775 22.1925C20.9651 22.3801 21.2196 22.4856 21.485 22.4856C21.7504 22.4856 22.0049 22.3801 22.1925 22.1925C22.3801 22.0049 22.4856 21.7504 22.4856 21.485C22.4856 21.2196 22.3801 20.9651 22.1925 20.7775L19.3638 17.95ZM13 20C12.7348 20 12.4804 20.1054 12.2929 20.2929C12.1054 20.4804 12 20.7348 12 21V25C12 25.2652 12.1054 25.5196 12.2929 25.7071C12.4804 25.8946 12.7348 26 13 26C13.2652 26 13.5196 25.8946 13.7071 25.7071C13.8946 25.5196 14 25.2652 14 25V21C14 20.7348 13.8946 20.4804 13.7071 20.2929C13.5196 20.1054 13.2652 20 13 20ZM6.63625 17.95L3.8075 20.7775C3.61986 20.9651 3.51444 21.2196 3.51444 21.485C3.51444 21.7504 3.61986 22.0049 3.8075 22.1925C3.99514 22.3801 4.24964 22.4856 4.515 22.4856C4.78036 22.4856 5.03486 22.3801 5.2225 22.1925L8.05 19.3638C8.22962 19.1747 8.32828 18.9229 8.32494 18.6622C8.3216 18.4014 8.21653 18.1523 8.03213 17.9679C7.84773 17.7835 7.59859 17.6784 7.33782 17.6751C7.07706 17.6717 6.82531 17.7704 6.63625 17.95ZM6 13C6 12.7348 5.89464 12.4804 5.70711 12.2929C5.51957 12.1054 5.26522 12 5 12H1C0.734784 12 0.48043 12.1054 0.292893 12.2929C0.105357 12.4804 0 12.7348 0 13C0 13.2652 0.105357 13.5196 0.292893 13.7071C0.48043 13.8946 0.734784 14 1 14H5C5.26522 14 5.51957 13.8946 5.70711 13.7071C5.89464 13.5196 6 13.2652 6 13ZM5.2225 3.8075C5.03486 3.61986 4.78036 3.51444 4.515 3.51444C4.24964 3.51444 3.99514 3.61986 3.8075 3.8075C3.61986 3.99514 3.51444 4.24964 3.51444 4.515C3.51444 4.78036 3.61986 5.03486 3.8075 5.2225L6.63625 8.05C6.82531 8.22962 7.07706 8.32828 7.33782 8.32494C7.59859 8.3216 7.84773 8.21653 8.03213 8.03213C8.21653 7.84773 8.3216 7.59859 8.32494 7.33782C8.32828 7.07706 8.22962 6.82531 8.05 6.63625L5.2225 3.8075Z" />
    </svg>
  );
};

export default ProcessingIcon;
