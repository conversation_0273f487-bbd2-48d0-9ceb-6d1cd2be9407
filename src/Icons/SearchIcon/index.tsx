import React from 'react';

const SearchIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 27 27" fill="none">
      <path
        d="M26.7075 25.2928L20.4487 19.0353C22.2628 16.8574 23.1673 14.064 22.9743 11.2362C22.7812 8.40838 21.5054 5.76385 19.4122 3.85275C17.319 1.94164 14.5695 0.911103 11.7359 0.975503C8.90219 1.0399 6.20243 2.19429 4.19821 4.19851C2.19398 6.20274 1.0396 8.9025 0.975197 11.7362C0.910797 14.5698 1.94134 17.3193 3.85244 19.4125C5.76355 21.5057 8.40808 22.7815 11.2359 22.9746C14.0637 23.1676 16.8571 22.2631 19.035 20.449L25.2925 26.7078C25.3854 26.8007 25.4957 26.8744 25.6171 26.9247C25.7385 26.975 25.8686 27.0008 26 27.0008C26.1314 27.0008 26.2615 26.975 26.3829 26.9247C26.5043 26.8744 26.6146 26.8007 26.7075 26.7078C26.8004 26.6149 26.8741 26.5046 26.9244 26.3832C26.9747 26.2618 27.0005 26.1317 27.0005 26.0003C27.0005 25.8689 26.9747 25.7388 26.9244 25.6174C26.8741 25.496 26.8004 25.3857 26.7075 25.2928ZM2.99998 12.0003C2.99998 10.2203 3.52782 8.4802 4.51675 7.00015C5.50569 5.52011 6.91129 4.36656 8.55583 3.68537C10.2004 3.00418 12.01 2.82595 13.7558 3.17322C15.5016 3.52048 17.1053 4.37765 18.3639 5.63632C19.6226 6.895 20.4798 8.49864 20.827 10.2445C21.1743 11.9903 20.9961 13.7999 20.3149 15.4444C19.6337 17.089 18.4802 18.4946 17.0001 19.4835C15.5201 20.4724 13.78 21.0003 12 21.0003C9.61384 20.9976 7.3262 20.0486 5.63894 18.3613C3.95169 16.6741 3.00263 14.3864 2.99998 12.0003Z"
        fill={props.fill ? props.fill : '#FFA033'}
      />
    </svg>
  );
};

export default SearchIcon;
