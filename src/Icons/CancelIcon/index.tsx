import React from 'react';

const CancelIcon = ({
  width = 50,
  height = 50,
  color = '#FF0000',
  ...props
}: {
  width?: number;
  height?: number;
  color?: string;
} & React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 24 24"
      width={width}
      height={height}
      fill="none"
      stroke={color}
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    >
      <path d="M6 6L18 18M18 6L6 18" />
    </svg>
  );
};

export default CancelIcon;
