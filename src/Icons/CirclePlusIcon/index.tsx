import React from 'react';

const CirclePlusIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={props?.width ?? '26'}
      height={props?.height ?? '26'}
      viewBox="0 0 26 26"
      fill="none"
    >
      <path
        d="M13 0C10.4288 0 7.91542 0.762437 5.77759 2.1909C3.63975 3.61935 1.9735 5.64968 0.989564 8.02512C0.00562507 10.4006 -0.251818 13.0144 0.24979 15.5362C0.751397 18.0579 1.98953 20.3743 3.80761 22.1924C5.62569 24.0105 7.94207 25.2486 10.4638 25.7502C12.9856 26.2518 15.5994 25.9944 17.9749 25.0104C20.3503 24.0265 22.3806 22.3603 23.8091 20.2224C25.2376 18.0846 26 15.5712 26 13C25.9957 9.55351 24.6247 6.2494 22.1876 3.81236C19.7506 1.37532 16.4465 0.00430006 13 0ZM18 14H14V18C14 18.2652 13.8946 18.5196 13.7071 18.7071C13.5196 18.8946 13.2652 19 13 19C12.7348 19 12.4804 18.8946 12.2929 18.7071C12.1054 18.5196 12 18.2652 12 18V14H8C7.73478 14 7.48043 13.8946 7.29289 13.7071C7.10536 13.5196 7 13.2652 7 13C7 12.7348 7.10536 12.4804 7.29289 12.2929C7.48043 12.1054 7.73478 12 8 12H12V8C12 7.73478 12.1054 7.48043 12.2929 7.29289C12.4804 7.10536 12.7348 7 13 7C13.2652 7 13.5196 7.10536 13.7071 7.29289C13.8946 7.48043 14 7.73478 14 8V12H18C18.2652 12 18.5196 12.1054 18.7071 12.2929C18.8946 12.4804 19 12.7348 19 13C19 13.2652 18.8946 13.5196 18.7071 13.7071C18.5196 13.8946 18.2652 14 18 14Z"
        fill="url(#paint0_linear_4141_1090)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4141_1090"
          x1="13"
          y1="0"
          x2="13"
          y2="26"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor="#FFA033" />
          <stop offset="1" stopColor="#FF8800" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default CirclePlusIcon;
