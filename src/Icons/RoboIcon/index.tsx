import React from 'react';

const RoboIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width={props?.width ?? '112'}
      height={props?.height ?? '100'}
      viewBox="0 0 112 100"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g clip-path="url(#clip0_1_4230)">
        <path d="M96.6685 41.008C96.7306 41.4343 96.7928 41.9228 96.8505 42.4556L102.881 40.1821C103.267 40.1732 103.48 40.8082 103.103 40.937L96.9304 43.2594C96.9882 43.881 97.0326 44.5471 97.0548 45.2354L103.84 42.6776C104.213 42.6688 104.404 43.2283 104.089 43.4192C102.481 44.0675 100.825 44.6093 99.204 45.222C98.4935 45.4929 97.783 45.7638 97.0725 46.0391C97.077 46.5808 97.0725 47.1315 97.0459 47.6821L107.041 43.8766L104.781 37.9929L96.6685 41.0125V41.008Z" fill="#FF8800" />
        <path d="M105.327 37.7886L107.53 43.5967C107.619 43.65 108.627 42.3534 108.76 42.1891C109.479 41.2966 110.536 40.1065 111.131 39.134C111.184 39.0497 111.229 38.9653 111.247 38.8543L105.327 37.7886Z" fill="#FF8800" />
        <path d="M90.5849 49.9423C90.5849 49.8268 90.5849 49.7114 90.5805 49.5959C89.8345 49.5648 89.0751 49.5382 88.3114 49.5071C88.0982 49.4982 87.8806 49.4938 87.6675 49.4849C85.989 49.4272 84.2661 49.3739 82.5121 49.3295C81.624 49.3073 80.727 49.2851 79.8256 49.2673C79.6657 49.2673 79.5103 49.2584 79.3504 49.2584C79.5458 58.6368 79.6169 80.555 79.4703 84.0497C79.4437 84.667 79.0973 85.222 78.5556 85.524C76.1178 86.8961 69.8522 88.4014 68.0982 88.7034C55.989 90.8082 39.0796 91.6119 27.6808 86.9582C26.8416 86.6163 25.092 85.7238 24.0085 85.1643C23.4313 84.8668 23.0671 84.2806 23.0494 83.6323C22.9739 80.7549 22.2501 72.6376 23.0494 49.476C18.8709 49.5648 15.1186 49.6536 11.9792 49.7336C11.9037 60.8348 12.5964 75.8082 12.0725 86.2034C12.179 90.0755 15.1364 92.4955 18.3158 94.3295C24.7057 98.0151 71.3442 106.55 88.6844 90.9503C89.5325 89.6137 90.4339 88.2016 90.4694 86.5542C90.5893 79.6936 90.7714 63.1971 90.616 51.4609C90.6071 50.9458 90.6027 50.4352 90.5938 49.9423H90.5849Z" fill="#FF8800" />
        <path d="M94.5324 51.7141V49.778C93.7775 49.738 93.0093 49.7025 92.2278 49.667V78.1439C92.2278 79.4938 98.4622 77.7887 99.2438 77.2469C100.119 76.643 101.389 73.7967 101.508 72.7221C101.793 70.1643 101.926 58.2594 101.322 56.2123C100.598 53.7522 97.4676 52.0649 94.528 51.7185L94.5324 51.7141Z" fill="#FF8800" />
        <path d="M32.3921 66.7229C32.3921 70.6883 35.1363 73.9077 38.5244 73.9077C39.7589 73.9077 40.9045 73.4814 41.8681 72.7443C43.5466 71.4609 44.6567 69.2451 44.6567 66.7229C44.6567 64.5604 43.8397 62.6244 42.552 61.3055C41.4729 60.2043 40.0653 59.5382 38.5244 59.5382C35.1363 59.5382 32.3921 62.7531 32.3921 66.7229Z" fill="#FF8800" />
        <path d="M63.9507 73.9077C67.3375 73.9077 70.083 70.6909 70.083 66.7229C70.083 62.7549 67.3375 59.5382 63.9507 59.5382C60.5639 59.5382 57.8184 62.7549 57.8184 66.7229C57.8184 70.6909 60.5639 73.9077 63.9507 73.9077Z" fill="#FF8800" />
        <path d="M7.99173 51.7584C4.87894 52.0648 1.96597 53.5435 1.20665 56.5364C0.589418 58.9787 0.69599 69.9068 1.02903 72.7043C1.30434 74.9911 2.5832 76.9183 4.95443 77.8197C5.73151 78.1172 10.2919 79.3117 10.2919 78.1439V49.7869C9.47041 49.8091 8.70221 49.8313 7.98729 49.849V51.7584H7.99173Z" fill="#FF8800" />
        <path d="M17.1968 48.1972C19.164 48.1706 21.1311 48.1395 23.0983 48.0995C24.3638 48.0729 25.6338 48.0462 26.8993 48.0152C40.3851 47.6688 53.6134 47.6066 66.9926 47.9219C67.3478 47.913 68.9642 47.8153 70.434 47.0027C70.8469 46.7718 71.251 46.4876 71.6107 46.1279C72.5743 45.1688 73.0627 43.8544 73.0627 42.2247C73.0627 39.858 72.0459 38.175 70.0388 37.2292C68.5023 36.5009 66.9304 36.4921 66.9126 36.4921L34.4615 36.3944C36.9881 19.8668 64.124 19.4672 66.9704 35.7194C67.2945 35.7283 68.8087 35.7993 70.3407 36.5187C71.9304 37.2647 73.8309 38.8588 73.8309 42.2247C73.8309 44.0676 73.2626 45.5684 72.148 46.6786C72.0281 46.7985 71.9038 46.9095 71.7794 47.0116C71.2643 47.4423 70.7004 47.762 70.1498 47.9974C72.2235 48.0551 74.3016 48.1217 76.3887 48.2017C77.3611 48.2372 78.3381 48.2638 79.315 48.2816C79.4926 48.2816 79.6702 48.2905 79.8478 48.2949C80.5938 48.3082 81.3398 48.3215 82.0903 48.3304C83.2315 48.3482 84.3771 48.3615 85.5183 48.3837L87.188 48.4281L87.8274 48.4459L90.5583 48.5169L92.2279 48.5613L93.0006 48.5835C93.4402 48.5747 94.0263 48.5303 94.5325 48.366C94.9322 48.2372 95.283 48.0285 95.4695 47.6999C95.5983 47.469 95.6604 47.0649 95.6826 46.5764C95.696 46.3189 95.696 46.0391 95.6826 45.7549C95.6649 45.0755 95.6027 44.3517 95.5494 43.7789C95.5272 43.5214 95.505 43.2949 95.4873 43.1173C95.4873 43.0684 95.4784 43.024 95.4739 42.9752C95.4295 42.4867 95.3807 41.9983 95.323 41.5098C95.2475 40.826 95.1587 40.1466 95.0565 39.4716C92.3167 20.8526 81.3576 6.2656 61.5618 3.16169C60.2386 2.95299 57.6942 2.99739 56.664 2.68656C56.2555 2.56223 56.1622 2.00716 55.8602 1.76294C53.5601 -0.110953 49.8967 -0.412907 47.1214 0.510716C45.7226 0.976968 45.4251 2.5267 44.8833 2.68656C44.2617 2.87306 42.5343 2.81533 41.6862 2.94411C19.9944 6.19455 8.68884 21.7763 6.83716 42.9219C6.75279 43.8944 6.41975 46.7407 6.80607 47.7576C6.8416 47.8553 6.886 47.9397 6.93929 48.0018C7.40998 48.548 8.35137 48.4548 9.13289 48.4104C9.26611 48.4015 9.39932 48.397 9.52366 48.3926C9.7368 48.3926 9.99879 48.3837 10.2963 48.3793C10.7848 48.3704 11.3709 48.3571 11.9881 48.3393C14.346 48.2816 17.2013 48.2017 17.2013 48.2017L17.1968 48.1972ZM65.5672 41.3588C66.0423 41.3588 66.4331 41.7452 66.4331 42.2247C66.4331 42.7043 66.0467 43.0906 65.5672 43.0906C65.0876 43.0906 64.7013 42.7043 64.7013 42.2247C64.7013 41.7452 65.0876 41.3588 65.5672 41.3588ZM58.5645 41.3588C59.0396 41.3588 59.4304 41.7452 59.4304 42.2247C59.4304 42.7043 59.0441 43.0906 58.5645 43.0906C58.0849 43.0906 57.6986 42.7043 57.6986 42.2247C57.6986 41.7452 58.0849 41.3588 58.5645 41.3588ZM51.5618 41.3588C52.037 41.3588 52.4277 41.7452 52.4277 42.2247C52.4277 42.7043 52.0414 43.0906 51.5618 43.0906C51.0823 43.0906 50.6959 42.7043 50.6959 42.2247C50.6959 41.7452 51.0823 41.3588 51.5618 41.3588ZM44.5592 41.3588C45.0343 41.3588 45.4251 41.7452 45.4251 42.2247C45.4251 42.7043 45.0388 43.0906 44.5592 43.0906C44.0796 43.0906 43.6933 42.7043 43.6933 42.2247C43.6933 41.7452 44.0796 41.3588 44.5592 41.3588ZM37.5565 41.3588C38.0317 41.3588 38.4224 41.7452 38.4224 42.2247C38.4224 42.7043 38.0361 43.0906 37.5565 43.0906C37.0769 43.0906 36.6906 42.7043 36.6906 42.2247C36.6906 41.7452 37.0769 41.3588 37.5565 41.3588Z" fill="#FF8800" />
        <g clip-path="url(#clip1_1_4230)">
          <path d="M96.6685 41.008C96.7306 41.4343 96.7928 41.9228 96.8505 42.4556L102.881 40.1821C103.267 40.1732 103.48 40.8082 103.103 40.937L96.9304 43.2594C96.9882 43.881 97.0326 44.5471 97.0548 45.2354L103.84 42.6776C104.213 42.6688 104.404 43.2283 104.089 43.4192C102.481 44.0675 100.825 44.6093 99.204 45.222C98.4935 45.4929 97.783 45.7638 97.0725 46.0391C97.077 46.5808 97.0725 47.1315 97.0459 47.6821L107.041 43.8766L104.781 37.9929L96.6685 41.0125V41.008Z" fill="#FF8800" />
          <path d="M105.327 37.7886L107.53 43.5967C107.619 43.65 108.627 42.3534 108.76 42.1891C109.479 41.2966 110.536 40.1065 111.131 39.134C111.184 39.0497 111.229 38.9653 111.247 38.8543L105.327 37.7886Z" fill="#FF8800" />
          <path d="M90.5849 49.9423C90.5849 49.8268 90.5849 49.7114 90.5805 49.5959C89.8345 49.5648 89.0751 49.5382 88.3114 49.5071C88.0982 49.4982 87.8806 49.4938 87.6675 49.4849C85.989 49.4272 84.2661 49.3739 82.5121 49.3295C81.624 49.3073 80.727 49.2851 79.8256 49.2673C79.6657 49.2673 79.5103 49.2584 79.3504 49.2584C79.5458 58.6368 79.6169 80.555 79.4703 84.0497C79.4437 84.667 79.0973 85.222 78.5556 85.524C76.1178 86.8961 69.8522 88.4014 68.0982 88.7034C55.989 90.8082 39.0796 91.6119 27.6808 86.9582C26.8416 86.6163 25.092 85.7238 24.0085 85.1643C23.4313 84.8668 23.0671 84.2806 23.0494 83.6323C22.9739 80.7549 22.2501 72.6376 23.0494 49.476C18.8709 49.5648 15.1186 49.6536 11.9792 49.7336C11.9037 60.8348 12.5964 75.8082 12.0725 86.2034C12.179 90.0755 15.1364 92.4955 18.3158 94.3295C24.7057 98.0151 71.3442 106.55 88.6844 90.9503C89.5325 89.6137 90.4339 88.2016 90.4694 86.5542C90.5893 79.6936 90.7714 63.1971 90.616 51.4609C90.6071 50.9458 90.6027 50.4352 90.5938 49.9423H90.5849Z" fill="#FF8800" />
          <path d="M94.5324 51.7141V49.778C93.7775 49.738 93.0093 49.7025 92.2278 49.667V78.1439C92.2278 79.4938 98.4622 77.7887 99.2438 77.2469C100.119 76.643 101.389 73.7967 101.508 72.7221C101.793 70.1643 101.926 58.2594 101.322 56.2123C100.598 53.7522 97.4676 52.0649 94.528 51.7185L94.5324 51.7141Z" fill="#FF8800" />
          <path d="M32.3921 66.7229C32.3921 70.6883 35.1363 73.9077 38.5244 73.9077C39.7589 73.9077 40.9045 73.4814 41.8681 72.7443C43.5466 71.4609 44.6567 69.2451 44.6567 66.7229C44.6567 64.5604 43.8397 62.6244 42.552 61.3055C41.4729 60.2043 40.0653 59.5382 38.5244 59.5382C35.1363 59.5382 32.3921 62.7531 32.3921 66.7229Z" fill="#FF8800" />
          <path d="M63.9507 73.9077C67.3375 73.9077 70.083 70.6909 70.083 66.7229C70.083 62.7549 67.3375 59.5382 63.9507 59.5382C60.5639 59.5382 57.8184 62.7549 57.8184 66.7229C57.8184 70.6909 60.5639 73.9077 63.9507 73.9077Z" fill="#FF8800" />
          <path d="M7.99173 51.7584C4.87894 52.0648 1.96597 53.5435 1.20665 56.5364C0.589418 58.9787 0.69599 69.9068 1.02903 72.7043C1.30434 74.9911 2.5832 76.9183 4.95443 77.8197C5.73151 78.1172 10.2919 79.3117 10.2919 78.1439V49.7869C9.47041 49.8091 8.70221 49.8313 7.98729 49.849V51.7584H7.99173Z" fill="#FF8800" />
          <path d="M17.1968 48.1972C19.164 48.1706 21.1311 48.1395 23.0983 48.0995C24.3638 48.0729 25.6338 48.0462 26.8993 48.0152C40.3851 47.6688 53.6134 47.6066 66.9926 47.9219C67.3478 47.913 68.9642 47.8153 70.434 47.0027C70.8469 46.7718 71.251 46.4876 71.6107 46.1279C72.5743 45.1688 73.0627 43.8544 73.0627 42.2247C73.0627 39.858 72.0459 38.175 70.0388 37.2292C68.5023 36.5009 66.9304 36.4921 66.9126 36.4921L34.4615 36.3944C36.9881 19.8668 64.124 19.4672 66.9704 35.7194C67.2945 35.7283 68.8087 35.7993 70.3407 36.5187C71.9304 37.2647 73.8309 38.8588 73.8309 42.2247C73.8309 44.0676 73.2626 45.5684 72.148 46.6786C72.0281 46.7985 71.9038 46.9095 71.7794 47.0116C71.2643 47.4423 70.7004 47.762 70.1498 47.9974C72.2235 48.0551 74.3016 48.1217 76.3887 48.2017C77.3611 48.2372 78.3381 48.2638 79.315 48.2816C79.4926 48.2816 79.6702 48.2905 79.8478 48.2949C80.5938 48.3082 81.3398 48.3215 82.0903 48.3304C83.2315 48.3482 84.3771 48.3615 85.5183 48.3837L87.188 48.4281L87.8274 48.4459L90.5583 48.5169L92.2279 48.5613L93.0006 48.5835C93.4402 48.5747 94.0263 48.5303 94.5325 48.366C94.9322 48.2372 95.283 48.0285 95.4695 47.6999C95.5983 47.469 95.6604 47.0649 95.6826 46.5764C95.696 46.3189 95.696 46.0391 95.6826 45.7549C95.6649 45.0755 95.6027 44.3517 95.5494 43.7789C95.5272 43.5214 95.505 43.2949 95.4873 43.1173C95.4873 43.0684 95.4784 43.024 95.4739 42.9752C95.4295 42.4867 95.3807 41.9983 95.323 41.5098C95.2475 40.826 95.1587 40.1466 95.0565 39.4716C92.3167 20.8526 81.3576 6.2656 61.5618 3.16169C60.2386 2.95299 57.6942 2.99739 56.664 2.68656C56.2555 2.56223 56.1622 2.00716 55.8602 1.76294C53.5601 -0.110953 49.8967 -0.412907 47.1214 0.510716C45.7226 0.976968 45.4251 2.5267 44.8833 2.68656C44.2617 2.87306 42.5343 2.81533 41.6862 2.94411C19.9944 6.19455 8.68884 21.7763 6.83716 42.9219C6.75279 43.8944 6.41975 46.7407 6.80607 47.7576C6.8416 47.8553 6.886 47.9397 6.93929 48.0018C7.40998 48.548 8.35137 48.4548 9.13289 48.4104C9.26611 48.4015 9.39932 48.397 9.52366 48.3926C9.7368 48.3926 9.99879 48.3837 10.2963 48.3793C10.7848 48.3704 11.3709 48.3571 11.9881 48.3393C14.346 48.2816 17.2013 48.2017 17.2013 48.2017L17.1968 48.1972ZM65.5672 41.3588C66.0423 41.3588 66.4331 41.7452 66.4331 42.2247C66.4331 42.7043 66.0467 43.0906 65.5672 43.0906C65.0876 43.0906 64.7013 42.7043 64.7013 42.2247C64.7013 41.7452 65.0876 41.3588 65.5672 41.3588ZM58.5645 41.3588C59.0396 41.3588 59.4304 41.7452 59.4304 42.2247C59.4304 42.7043 59.0441 43.0906 58.5645 43.0906C58.0849 43.0906 57.6986 42.7043 57.6986 42.2247C57.6986 41.7452 58.0849 41.3588 58.5645 41.3588ZM51.5618 41.3588C52.037 41.3588 52.4277 41.7452 52.4277 42.2247C52.4277 42.7043 52.0414 43.0906 51.5618 43.0906C51.0823 43.0906 50.6959 42.7043 50.6959 42.2247C50.6959 41.7452 51.0823 41.3588 51.5618 41.3588ZM44.5592 41.3588C45.0343 41.3588 45.4251 41.7452 45.4251 42.2247C45.4251 42.7043 45.0388 43.0906 44.5592 43.0906C44.0796 43.0906 43.6933 42.7043 43.6933 42.2247C43.6933 41.7452 44.0796 41.3588 44.5592 41.3588ZM37.5565 41.3588C38.0317 41.3588 38.4224 41.7452 38.4224 42.2247C38.4224 42.7043 38.0361 43.0906 37.5565 43.0906C37.0769 43.0906 36.6906 42.7043 36.6906 42.2247C36.6906 41.7452 37.0769 41.3588 37.5565 41.3588Z" fill="#FF8800" />
        </g>
      </g>
      <defs>
        <clipPath id="clip0_1_4230">
          <rect
            width={props?.width ?? '110.484'}
            height={props?.height ?? '100'}
            fill="white"
            transform="translate(0.758057)" />
        </clipPath>
        <clipPath id="clip1_1_4230">
          <rect
            width={props?.width ?? '110.484'}
            height={props?.height ?? '100'}
            fill="white"
            transform="translate(0.758057)" />
        </clipPath>
      </defs>
    </svg>
  );
};

export default RoboIcon;
