import React from 'react';

const FilterIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 28 26" fill="none">
      <path
        d="M27.2863 1.98875C27.0903 1.54581 26.77 1.16931 26.3641 0.905017C25.9582 0.640725 25.4843 0.500018 25 0.5H3C2.51575 0.500047 2.04193 0.640735 1.63611 0.904971C1.2303 1.16921 0.909966 1.54561 0.714029 1.98846C0.518091 2.43131 0.454986 2.92153 0.532381 3.39956C0.609777 3.87759 0.824341 4.32285 1.15 4.68125L1.165 4.69875L9.5 13.5938V23C9.49994 23.4526 9.62274 23.8967 9.85532 24.285C10.0879 24.6733 10.4215 24.9912 10.8206 25.2047C11.2197 25.4182 11.6692 25.5194 12.1213 25.4974C12.5734 25.4755 13.011 25.3312 13.3875 25.08L17.3875 22.4137C17.73 22.1853 18.0107 21.8757 18.2048 21.5127C18.3989 21.1496 18.5003 20.7442 18.5 20.3325V13.5938L26.8338 4.69875L26.8488 4.68125C27.1746 4.32304 27.3894 3.87791 27.4671 3.39993C27.5448 2.92195 27.4819 2.4317 27.2863 1.98875ZM16.1763 11.6862C15.741 12.1492 15.4991 12.7609 15.5 13.3962V20.065L12.5 22.065V13.3962C12.5009 12.7609 12.259 12.1492 11.8238 11.6862L4.15375 3.5H23.8463L16.1763 11.6862Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default FilterIcon;
