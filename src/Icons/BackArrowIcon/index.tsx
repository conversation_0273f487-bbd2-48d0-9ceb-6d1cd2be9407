import React from 'react';

const BackArrowIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 24 20" fill="none">
      <path
        d="M24.0001 9.99958C24.0001 10.2648 23.8947 10.5192 23.7072 10.7067C23.5196 10.8942 23.2653 10.9996 23.0001 10.9996H3.4138L10.7076 18.2921C10.8005 18.385 10.8742 18.4953 10.9244 18.6167C10.9747 18.7381 11.0006 18.8682 11.0006 18.9996C11.0006 19.131 10.9747 19.2611 10.9244 19.3825C10.8742 19.5039 10.8005 19.6142 10.7076 19.7071C10.6146 19.8 10.5043 19.8737 10.383 19.924C10.2616 19.9743 10.1314 20.0001 10.0001 20.0001C9.86866 20.0001 9.73855 19.9743 9.61716 19.924C9.49576 19.8737 9.38546 19.8 9.29255 19.7071L0.292554 10.7071C0.199578 10.6142 0.125819 10.5039 0.0754945 10.3825C0.0251702 10.2611 -0.000732422 10.131 -0.000732422 9.99958C-0.000732422 9.86816 0.0251702 9.73804 0.0754945 9.61664C0.125819 9.49524 0.199578 9.38495 0.292554 9.29208L9.29255 0.292079C9.48019 0.104439 9.73469 -0.000976564 10.0001 -0.000976562C10.2654 -0.000976561 10.5199 0.104439 10.7076 0.292079C10.8952 0.47972 11.0006 0.734215 11.0006 0.999579C11.0006 1.26494 10.8952 1.51944 10.7076 1.70708L3.4138 8.99958H23.0001C23.2653 8.99958 23.5196 9.10494 23.7072 9.29247C23.8947 9.48001 24.0001 9.73436 24.0001 9.99958Z"
        fill={props?.fill ?? 'url(#paint0_linear_2547_1468)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_2547_1468"
          x1="11.9997"
          y1="-0.000976563"
          x2="11.9997"
          y2="20.0001"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#FFA033'} />
          <stop offset="1" stopColor={props?.fill ?? '#FF8800'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default BackArrowIcon;
