import React from 'react';

const LogoutIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 24 24" fill="none">
      <path
        d="M10 23C10 23.2652 9.89464 23.5196 9.70711 23.7071C9.51957 23.8946 9.26522 24 9 24H2C1.46957 24 0.960859 23.7893 0.585786 23.4142C0.210714 23.0391 0 22.5304 0 22V2C0 1.46957 0.210714 0.960859 0.585786 0.585786C0.960859 0.210714 1.46957 0 2 0H9C9.26522 0 9.51957 0.105357 9.70711 0.292893C9.89464 0.48043 10 0.734784 10 1C10 1.26522 9.89464 1.51957 9.70711 1.70711C9.51957 1.89464 9.26522 2 9 2H2V22H9C9.26522 22 9.51957 22.1054 9.70711 22.2929C9.89464 22.4804 10 22.7348 10 23ZM23.7075 11.2925L18.7075 6.2925C18.5199 6.10486 18.2654 5.99944 18 5.99944C17.7346 5.99944 17.4801 6.10486 17.2925 6.2925C17.1049 6.48014 16.9994 6.73464 16.9994 7C16.9994 7.26536 17.1049 7.51986 17.2925 7.7075L20.5863 11H9C8.73478 11 8.48043 11.1054 8.29289 11.2929C8.10536 11.4804 8 11.7348 8 12C8 12.2652 8.10536 12.5196 8.29289 12.7071C8.48043 12.8946 8.73478 13 9 13H20.5863L17.2925 16.2925C17.1049 16.4801 16.9994 16.7346 16.9994 17C16.9994 17.2654 17.1049 17.5199 17.2925 17.7075C17.4801 17.8951 17.7346 18.0006 18 18.0006C18.2654 18.0006 18.5199 17.8951 18.7075 17.7075L23.7075 12.7075C23.8005 12.6146 23.8742 12.5043 23.9246 12.3829C23.9749 12.2615 24.0008 12.1314 24.0008 12C24.0008 11.8686 23.9749 11.7385 23.9246 11.6171C23.8742 11.4957 23.8005 11.3854 23.7075 11.2925Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default LogoutIcon;
