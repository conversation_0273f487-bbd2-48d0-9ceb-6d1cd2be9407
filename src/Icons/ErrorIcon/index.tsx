import React from 'react';

// Define the type for the component props
interface ErrorIconProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number;
  height?: string | number;
  stroke?: string;
  fill?: string;
}

const ErrorIcon: React.FC<ErrorIconProps> = (props) => {
  return (
    <svg
      width={props.width ?? '16'}
      height={props.height ?? '16'}
      viewBox="0 0 16 16"
      fill="none"
      {...props} // Spread the rest of the props to the SVG element
    >
      <g clipPath="url(#clip0_840_9927)">
        <path
          d="M7.99998 5.33337V8.00004M7.99998 10.6667H8.00665M14.6666 8.00004C14.6666 11.6819 11.6819 14.6667 7.99998 14.6667C4.31808 14.6667 1.33331 11.6819 1.33331 8.00004C1.33331 4.31814 4.31808 1.33337 7.99998 1.33337C11.6819 1.33337 14.6666 4.31814 14.6666 8.00004Z"
          stroke={props.stroke ?? '#F04438'}
          strokeWidth="1.33333"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_840_9927">
          <rect
            width={props.width ?? '16'}
            height={props.height ?? '16'}
            fill={props.fill ?? 'white'}
          />
        </clipPath>
      </defs>
    </svg>
  );
};

export default ErrorIcon;
