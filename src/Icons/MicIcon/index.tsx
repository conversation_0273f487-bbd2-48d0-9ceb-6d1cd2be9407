import React from 'react';

// Define the type for the component props
interface MicIconProps extends React.SVGProps<SVGSVGElement> {
  width?: string | number;
  height?: string | number;
  fill?: string;
}

const MicIcon: React.FC<MicIconProps> = (props) => {
  return (
    <svg {...props} viewBox="0 0 20 28" fill="none">
      <path
        d="M10 20C11.5908 19.9983 13.116 19.3657 14.2408 18.2408C15.3657 17.116 15.9983 15.5908 16 14V6C16 4.4087 15.3679 2.88258 14.2426 1.75736C13.1174 0.632141 11.5913 0 10 0C8.4087 0 6.88258 0.632141 5.75736 1.75736C4.63214 2.88258 4 4.4087 4 6V14C4.00165 15.5908 4.63433 17.116 5.75919 18.2408C6.88405 19.3657 8.40921 19.9983 10 20ZM6 6C6 4.93913 6.42143 3.92172 7.17157 3.17157C7.92172 2.42143 8.93913 2 10 2C11.0609 2 12.0783 2.42143 12.8284 3.17157C13.5786 3.92172 14 4.93913 14 6V14C14 15.0609 13.5786 16.0783 12.8284 16.8284C12.0783 17.5786 11.0609 18 10 18C8.93913 18 7.92172 17.5786 7.17157 16.8284C6.42143 16.0783 6 15.0609 6 14V6ZM11 23.95V27C11 27.2652 10.8946 27.5196 10.7071 27.7071C10.5196 27.8946 10.2652 28 10 28C9.73478 28 9.48043 27.8946 9.29289 27.7071C9.10536 27.5196 9 27.2652 9 27V23.95C6.53455 23.6991 4.24971 22.543 2.5873 20.7051C0.924902 18.8672 0.00304514 16.4782 0 14C0 13.7348 0.105357 13.4804 0.292893 13.2929C0.48043 13.1054 0.734784 13 1 13C1.26522 13 1.51957 13.1054 1.70711 13.2929C1.89464 13.4804 2 13.7348 2 14C2 16.1217 2.84285 18.1566 4.34315 19.6569C5.84344 21.1571 7.87827 22 10 22C12.1217 22 14.1566 21.1571 15.6569 19.6569C17.1571 18.1566 18 16.1217 18 14C18 13.7348 18.1054 13.4804 18.2929 13.2929C18.4804 13.1054 18.7348 13 19 13C19.2652 13 19.5196 13.1054 19.7071 13.2929C19.8946 13.4804 20 13.7348 20 14C19.997 16.4782 19.0751 18.8672 17.4127 20.7051C15.7503 22.543 13.4654 23.6991 11 23.95Z"
        fill={props?.fill ?? 'url(#paint0_linear_1399_962)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1399_962"
          x1="10"
          y1="0"
          x2="10"
          y2="28"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#FFA033'} />
          <stop offset="1" stopColor={props?.fill ?? '#FF8800'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default MicIcon;
