import React from 'react';

const PurchaseHistoryIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      viewBox="0 0 28 26"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M6 8C6 7.73478 6.10536 7.48043 6.29289 7.29289C6.48043 7.10536 6.73478 7 7 7H19C19.2652 7 19.5196 7.10536 19.7071 7.29289C19.8946 7.48043 20 7.73478 20 8C20 8.26522 19.8946 8.51957 19.7071 8.70711C19.5196 8.89464 19.2652 9 19 9H7C6.73478 9 6.48043 8.89464 6.29289 8.70711C6.10536 8.51957 6 8.26522 6 8ZM7 13H19C19.2652 13 19.5196 12.8946 19.7071 12.7071C19.8946 12.5196 20 12.2652 20 12C20 11.7348 19.8946 11.4804 19.7071 11.2929C19.5196 11.1054 19.2652 11 19 11H7C6.73478 11 6.48043 11.1054 6.29289 11.2929C6.10536 11.4804 6 11.7348 6 12C6 12.2652 6.10536 12.5196 6.29289 12.7071C6.48043 12.8946 6.73478 13 7 13ZM26 2V21C25.9999 21.1704 25.9563 21.338 25.8732 21.4869C25.7901 21.6357 25.6704 21.7608 25.5254 21.8503C25.3803 21.9399 25.2148 21.9909 25.0446 21.9985C24.8743 22.0061 24.7049 21.97 24.5525 21.8937L21 20.1175L17.4475 21.8937C17.3086 21.9633 17.1554 21.9995 17 21.9995C16.8446 21.9995 16.6914 21.9633 16.5525 21.8937L13 20.1175L9.4475 21.8937C9.30857 21.9633 9.15535 21.9995 9 21.9995C8.84465 21.9995 8.69143 21.9633 8.5525 21.8937L5 20.1175L1.4475 21.8937C1.29508 21.97 1.12569 22.0061 0.955425 21.9985C0.785156 21.9909 0.619654 21.9399 0.474632 21.8503C0.32961 21.7608 0.209881 21.6357 0.126812 21.4869C0.0437432 21.338 9.09903e-05 21.1704 0 21V2C0 1.46957 0.210714 0.960859 0.585786 0.585786C0.960859 0.210714 1.46957 0 2 0H24C24.5304 0 25.0391 0.210714 25.4142 0.585786C25.7893 0.960859 26 1.46957 26 2ZM24 2H2V19.3825L4.5525 18.105C4.69143 18.0355 4.84465 17.9993 5 17.9993C5.15535 17.9993 5.30857 18.0355 5.4475 18.105L9 19.8825L12.5525 18.105C12.6914 18.0355 12.8446 17.9993 13 17.9993C13.1554 17.9993 13.3086 18.0355 13.4475 18.105L17 19.8825L20.5525 18.105C20.6914 18.0355 20.8446 17.9993 21 17.9993C21.1554 17.9993 21.3086 18.0355 21.4475 18.105L24 19.3825V2Z"
        fill="#343330"
      />
    </svg>
  );
};

export default PurchaseHistoryIcon;
