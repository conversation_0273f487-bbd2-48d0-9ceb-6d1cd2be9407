import React from 'react';

const TickIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 25 18" fill="none">
      <path
        d="M24.7076 1.70757L8.70757 17.7076C8.6147 17.8005 8.50441 17.8743 8.38301 17.9246C8.26161 17.975 8.13148 18.0009 8.00007 18.0009C7.86865 18.0009 7.73853 17.975 7.61713 17.9246C7.49573 17.8743 7.38544 17.8005 7.29257 17.7076L0.292568 10.7076C0.104927 10.5199 -0.000488281 10.2654 -0.000488281 10.0001C-0.000488281 9.7347 0.104927 9.48021 0.292568 9.29257C0.480208 9.10493 0.734704 8.99951 1.00007 8.99951C1.26543 8.99951 1.51993 9.10493 1.70757 9.29257L8.00007 15.5863L23.2926 0.292567C23.4802 0.104927 23.7347 -0.000488283 24.0001 -0.000488281C24.2654 -0.000488279 24.5199 0.104927 24.7076 0.292567C24.8952 0.480208 25.0006 0.734704 25.0006 1.00007C25.0006 1.26543 24.8952 1.51993 24.7076 1.70757Z"
        fill={props?.fill ?? '#343330'}
      />
    </svg>
  );
};

export default TickIcon;
