import React from 'react';

const CaretRightIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 32 32" fill="none">
      <path
        d="M11.7076 11.7076L1.70757 21.7076C1.61466 21.8005 1.50436 21.8742 1.38296 21.9245C1.26157 21.9747 1.13146 22.0006 1.00007 22.0006C0.868673 22.0006 0.738564 21.9747 0.617171 21.9245C0.495778 21.8742 0.385477 21.8005 0.292567 21.7076C0.199657 21.6147 0.125957 21.5044 0.0756741 21.383C0.0253914 21.2616 -0.000488281 21.1315 -0.000488281 21.0001C-0.000488281 20.8687 0.0253914 20.7386 0.0756741 20.6172C0.125957 20.4958 0.199657 20.3855 0.292567 20.2926L9.58632 11.0001L0.292567 1.70757C0.104927 1.51993 -0.000488283 1.26543 -0.000488281 1.00007C-0.000488279 0.734704 0.104927 0.480208 0.292567 0.292568C0.480208 0.104927 0.734704 -0.000488279 1.00007 -0.000488281C1.26543 -0.000488283 1.51993 0.104927 1.70757 0.292568L11.7076 10.2926C11.8005 10.3854 11.8743 10.4957 11.9246 10.6171C11.975 10.7385 12.0009 10.8687 12.0009 11.0001C12.0009 11.1315 11.975 11.2616 11.9246 11.383C11.8743 11.5044 11.8005 11.6147 11.7076 11.7076Z"
        fill={props.fill ?? '#343330'}
      />
    </svg>
  );
};

export default CaretRightIcon;
