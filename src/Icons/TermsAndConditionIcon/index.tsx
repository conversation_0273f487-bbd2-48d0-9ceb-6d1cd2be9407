import React from 'react';

const TermsAndConditionIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="18"
      height="21"
      viewBox="0 0 18 21"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M12.75 11C12.75 11.1989 12.671 11.3897 12.5303 11.5303C12.3897 11.671 12.1989 11.75 12 11.75H6C5.80109 11.75 5.61032 11.671 5.46967 11.5303C5.32902 11.3897 5.25 11.1989 5.25 11C5.25 10.8011 5.32902 10.6103 5.46967 10.4697C5.61032 10.329 5.80109 10.25 6 10.25H12C12.1989 10.25 12.3897 10.329 12.5303 10.4697C12.671 10.6103 12.75 10.8011 12.75 11ZM12 13.25H6C5.80109 13.25 5.61032 13.329 5.46967 13.4697C5.32902 13.6103 5.25 13.8011 5.25 14C5.25 14.1989 5.32902 14.3897 5.46967 14.5303C5.61032 14.671 5.80109 14.75 6 14.75H12C12.1989 14.75 12.3897 14.671 12.5303 14.5303C12.671 14.3897 12.75 14.1989 12.75 14C12.75 13.8011 12.671 13.6103 12.5303 13.4697C12.3897 13.329 12.1989 13.25 12 13.25ZM17.25 3.5V17.75C17.25 18.5456 16.9339 19.3087 16.3713 19.8713C15.8087 20.4339 15.0456 20.75 14.25 20.75H3.75C2.95435 20.75 2.19129 20.4339 1.62868 19.8713C1.06607 19.3087 0.75 18.5456 0.75 17.75V3.5C0.75 3.10218 0.908035 2.72064 1.18934 2.43934C1.47064 2.15804 1.85218 2 2.25 2H3.75V1.25C3.75 1.05109 3.82902 0.860322 3.96967 0.71967C4.11032 0.579018 4.30109 0.5 4.5 0.5C4.69891 0.5 4.88968 0.579018 5.03033 0.71967C5.17098 0.860322 5.25 1.05109 5.25 1.25V2H8.25V1.25C8.25 1.05109 8.32902 0.860322 8.46967 0.71967C8.61032 0.579018 8.80109 0.5 9 0.5C9.19891 0.5 9.38968 0.579018 9.53033 0.71967C9.67098 0.860322 9.75 1.05109 9.75 1.25V2H12.75V1.25C12.75 1.05109 12.829 0.860322 12.9697 0.71967C13.1103 0.579018 13.3011 0.5 13.5 0.5C13.6989 0.5 13.8897 0.579018 14.0303 0.71967C14.171 0.860322 14.25 1.05109 14.25 1.25V2H15.75C16.1478 2 16.5294 2.15804 16.8107 2.43934C17.092 2.72064 17.25 3.10218 17.25 3.5ZM15.75 3.5H14.25V4.25C14.25 4.44891 14.171 4.63968 14.0303 4.78033C13.8897 4.92098 13.6989 5 13.5 5C13.3011 5 13.1103 4.92098 12.9697 4.78033C12.829 4.63968 12.75 4.44891 12.75 4.25V3.5H9.75V4.25C9.75 4.44891 9.67098 4.63968 9.53033 4.78033C9.38968 4.92098 9.19891 5 9 5C8.80109 5 8.61032 4.92098 8.46967 4.78033C8.32902 4.63968 8.25 4.44891 8.25 4.25V3.5H5.25V4.25C5.25 4.44891 5.17098 4.63968 5.03033 4.78033C4.88968 4.92098 4.69891 5 4.5 5C4.30109 5 4.11032 4.92098 3.96967 4.78033C3.82902 4.63968 3.75 4.44891 3.75 4.25V3.5H2.25V17.75C2.25 18.1478 2.40804 18.5294 2.68934 18.8107C2.97064 19.092 3.35218 19.25 3.75 19.25H14.25C14.6478 19.25 15.0294 19.092 15.3107 18.8107C15.592 18.5294 15.75 18.1478 15.75 17.75V3.5Z"
        fill={props.fill ? props.fill : '#FFA033'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_3447_4407"
          x1="9"
          y1="0.5"
          x2="9"
          y2="20.75"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFA033" />
          <stop offset="1" stop-color="#FF8800" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default TermsAndConditionIcon;
