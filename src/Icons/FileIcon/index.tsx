import React from 'react';

const FileIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg {...props} viewBox="0 0 22 27" fill="none">
      <path
        d="M21.7075 7.7925L14.7075 0.7925C14.6146 0.699666 14.5042 0.626052 14.3829 0.575864C14.2615 0.525676 14.1314 0.499897 14 0.5H2C1.46957 0.5 0.960859 0.710714 0.585786 1.08579C0.210714 1.46086 0 1.96957 0 2.5V24.5C0 25.0304 0.210714 25.5391 0.585786 25.9142C0.960859 26.2893 1.46957 26.5 2 26.5H20C20.5304 26.5 21.0391 26.2893 21.4142 25.9142C21.7893 25.5391 22 25.0304 22 24.5V8.5C22.0001 8.36864 21.9743 8.23855 21.9241 8.11715C21.8739 7.99576 21.8003 7.88544 21.7075 7.7925ZM15 3.91375L18.5863 7.5H15V3.91375ZM20 24.5H2V2.5H13V8.5C13 8.76522 13.1054 9.01957 13.2929 9.20711C13.4804 9.39464 13.7348 9.5 14 9.5H20V24.5Z"
        fill={props?.fill ?? 'url(#paint0_linear_1462_499)'}
      />
      <defs>
        <linearGradient
          id="paint0_linear_1462_499"
          x1="11"
          y1="0.5"
          x2="11"
          y2="26.5"
          gradientUnits="userSpaceOnUse"
        >
          <stop stopColor={props?.fill ?? '#FFA033'} />
          <stop offset="1" stopColor={props?.fill ?? '#FF8800'} />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default FileIcon;
