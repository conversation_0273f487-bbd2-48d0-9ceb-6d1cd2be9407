import React from 'react';

const FailedIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="86"
      height="86"
      viewBox="0 0 86 86"
      {...props}
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <foreignObject
        x="-15.8132"
        y="-15.8132"
        width="117.626"
        height="117.626"
      ></foreignObject>
      <g filter="url(#filter0_i_349_5082)" data-figma-bg-blur-radius="17.9167">
        <circle cx="43" cy="43" r="40" fill="#FFD8E0" />
        <circle
          cx="43"
          cy="43"
          r="40.4479"
          stroke="url(#paint0_linear_349_5082)"
          stroke-opacity="0.2"
          stroke-width="0.895833"
        />
      </g>
      <path
        d="M55.6651 53.0858C56.0075 53.4282 56.1999 53.8926 56.1999 54.3768C56.1999 54.861 56.0075 55.3254 55.6651 55.6678C55.3228 56.0101 54.8584 56.2025 54.3742 56.2025C53.89 56.2025 53.4256 56.0101 53.0832 55.6678L43.4403 46.0219L33.7944 55.6647C33.452 56.0071 32.9877 56.1995 32.5035 56.1995C32.0192 56.1995 31.5549 56.0071 31.2125 55.6647C30.8701 55.3223 30.6777 54.8579 30.6777 54.3737C30.6777 53.8895 30.8701 53.4251 31.2125 53.0828L40.8584 43.4399L31.2155 33.794C30.8731 33.4516 30.6808 32.9872 30.6808 32.503C30.6808 32.0188 30.8731 31.5544 31.2155 31.212C31.5579 30.8697 32.0223 30.6773 32.5065 30.6773C32.9907 30.6773 33.4551 30.8697 33.7975 31.212L43.4403 40.8579L53.0862 31.2105C53.4286 30.8681 53.893 30.6758 54.3772 30.6758C54.8614 30.6758 55.3258 30.8681 55.6682 31.2105C56.0106 31.5529 56.2029 32.0173 56.2029 32.5015C56.2029 32.9857 56.0106 33.4501 55.6682 33.7925L46.0223 43.4399L55.6651 53.0858Z"
        fill="#FF3B30"
      />
      <defs>
        <filter
          id="filter0_i_349_5082"
          x="-15.8132"
          y="-15.8132"
          width="117.626"
          height="117.626"
          filterUnits="userSpaceOnUse"
          color-interpolation-filters="sRGB"
        >
          <feFlood flood-opacity="0" result="BackgroundImageFix" />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="BackgroundImageFix"
            result="shape"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feMorphology
            radius="1.30832"
            operator="dilate"
            in="SourceAlpha"
            result="effect1_innerShadow_349_5082"
          />
          <feOffset dx="-0.654162" dy="0.654162" />
          <feGaussianBlur stdDeviation="0.327081" />
          <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1" />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.35 0"
          />
          <feBlend
            mode="normal"
            in2="shape"
            result="effect1_innerShadow_349_5082"
          />
        </filter>
        <clipPath
          id="bgblur_0_349_5082_clip_path"
          transform="translate(15.8132 15.8132)"
        >
          <circle cx="43" cy="43" r="40" />
        </clipPath>
        <linearGradient
          id="paint0_linear_349_5082"
          x1="73.6452"
          y1="8.48387"
          x2="43"
          y2="83"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="white" />
          <stop offset="1" stop-color="white" stop-opacity="0" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default FailedIcon;
