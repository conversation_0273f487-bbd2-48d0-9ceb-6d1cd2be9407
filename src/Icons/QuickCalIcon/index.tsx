import React from 'react';

const QuickCalIcon = (props: React.SVGProps<SVGSVGElement>) => {
    return (
        <svg {...props} viewBox="0 0 22 26" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
                d="M5 12H17C17.2652 12 17.5196 11.8946 17.7071 11.7071C17.8946 11.5196 18 11.2652 18 11V5C18 4.73478 17.8946 4.48043 17.7071 4.29289C17.5196 4.10536 17.2652 4 17 4H5C4.73478 4 4.48043 4.10536 4.29289 4.29289C4.10536 4.48043 4 4.73478 4 5V11C4 11.2652 4.10536 11.5196 4.29289 11.7071C4.48043 11.8946 4.73478 12 5 12ZM6 6H16V10H6V6ZM20 0H2C1.46957 0 0.960859 0.210714 0.585786 0.585786C0.210714 0.960859 0 1.46957 0 2V24C0 24.5304 0.210714 25.0391 0.585786 25.4142C0.960859 25.7893 1.46957 26 2 26H20C20.5304 26 21.0391 25.7893 21.4142 25.4142C21.7893 25.0391 22 24.5304 22 24V2C22 1.46957 21.7893 0.960859 21.4142 0.585786C21.0391 0.210714 20.5304 0 20 0ZM20 24H2V2H20V24ZM7.5 15.5C7.5 15.7967 7.41203 16.0867 7.2472 16.3334C7.08238 16.58 6.84811 16.7723 6.57403 16.8858C6.29994 16.9993 5.99834 17.0291 5.70736 16.9712C5.41639 16.9133 5.14912 16.7704 4.93934 16.5607C4.72956 16.3509 4.5867 16.0836 4.52882 15.7926C4.47094 15.5017 4.50065 15.2001 4.61418 14.926C4.72771 14.6519 4.91997 14.4176 5.16665 14.2528C5.41332 14.088 5.70333 14 6 14C6.39782 14 6.77936 14.158 7.06066 14.4393C7.34196 14.7206 7.5 15.1022 7.5 15.5ZM12.5 15.5C12.5 15.7967 12.412 16.0867 12.2472 16.3334C12.0824 16.58 11.8481 16.7723 11.574 16.8858C11.2999 16.9993 10.9983 17.0291 10.7074 16.9712C10.4164 16.9133 10.1491 16.7704 9.93934 16.5607C9.72956 16.3509 9.5867 16.0836 9.52882 15.7926C9.47094 15.5017 9.50065 15.2001 9.61418 14.926C9.72771 14.6519 9.91997 14.4176 10.1666 14.2528C10.4133 14.088 10.7033 14 11 14C11.3978 14 11.7794 14.158 12.0607 14.4393C12.342 14.7206 12.5 15.1022 12.5 15.5ZM17.5 15.5C17.5 15.7967 17.412 16.0867 17.2472 16.3334C17.0824 16.58 16.8481 16.7723 16.574 16.8858C16.2999 16.9993 15.9983 17.0291 15.7074 16.9712C15.4164 16.9133 15.1491 16.7704 14.9393 16.5607C14.7296 16.3509 14.5867 16.0836 14.5288 15.7926C14.4709 15.5017 14.5007 15.2001 14.6142 14.926C14.7277 14.6519 14.92 14.4176 15.1666 14.2528C15.4133 14.088 15.7033 14 16 14C16.3978 14 16.7794 14.158 17.0607 14.4393C17.342 14.7206 17.5 15.1022 17.5 15.5ZM7.5 20.5C7.5 20.7967 7.41203 21.0867 7.2472 21.3334C7.08238 21.58 6.84811 21.7723 6.57403 21.8858C6.29994 21.9993 5.99834 22.0291 5.70736 21.9712C5.41639 21.9133 5.14912 21.7704 4.93934 21.5607C4.72956 21.3509 4.5867 21.0836 4.52882 20.7926C4.47094 20.5017 4.50065 20.2001 4.61418 19.926C4.72771 19.6519 4.91997 19.4176 5.16665 19.2528C5.41332 19.088 5.70333 19 6 19C6.39782 19 6.77936 19.158 7.06066 19.4393C7.34196 19.7206 7.5 20.1022 7.5 20.5ZM12.5 20.5C12.5 20.7967 12.412 21.0867 12.2472 21.3334C12.0824 21.58 11.8481 21.7723 11.574 21.8858C11.2999 21.9993 10.9983 22.0291 10.7074 21.9712C10.4164 21.9133 10.1491 21.7704 9.93934 21.5607C9.72956 21.3509 9.5867 21.0836 9.52882 20.7926C9.47094 20.5017 9.50065 20.2001 9.61418 19.926C9.72771 19.6519 9.91997 19.4176 10.1666 19.2528C10.4133 19.088 10.7033 19 11 19C11.3978 19 11.7794 19.158 12.0607 19.4393C12.342 19.7206 12.5 20.1022 12.5 20.5ZM17.5 20.5C17.5 20.7967 17.412 21.0867 17.2472 21.3334C17.0824 21.58 16.8481 21.7723 16.574 21.8858C16.2999 21.9993 15.9983 22.0291 15.7074 21.9712C15.4164 21.9133 15.1491 21.7704 14.9393 21.5607C14.7296 21.3509 14.5867 21.0836 14.5288 20.7926C14.4709 20.5017 14.5007 20.2001 14.6142 19.926C14.7277 19.6519 14.92 19.4176 15.1666 19.2528C15.4133 19.088 15.7033 19 16 19C16.3978 19 16.7794 19.158 17.0607 19.4393C17.342 19.7206 17.5 20.1022 17.5 20.5Z"
                fill="url(#paint0_linear_24_2365)" />
            <defs>
                <linearGradient
                    id="paint0_linear_24_2365"
                    x1="10.004"
                    y1="0"
                    x2="10.004" y2="26"
                    gradientUnits="userSpaceOnUse">
                    <stop stop-color={props?.fill ?? "#FFA033"} />
                    <stop offset="1" stop-color={props?.fill ?? "#FF8800"} />
                </linearGradient>
            </defs>
        </svg>
    );
};

export default QuickCalIcon;
