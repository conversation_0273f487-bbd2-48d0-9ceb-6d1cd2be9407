import React from 'react';

const BackIcon = (props: React.SVGProps<SVGSVGElement>) => {
  return (
    <svg
      {...props}
      width="24"
      height="20"
      viewBox="0 0 24 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M23.9998 9.99958C23.9998 10.2648 23.8945 10.5192 23.7069 10.7067C23.5194 10.8942 23.265 10.9996 22.9998 10.9996H3.41356L10.7073 18.2921C10.8002 18.385 10.8739 18.4953 10.9242 18.6167C10.9745 18.7381 11.0004 18.8682 11.0004 18.9996C11.0004 19.131 10.9745 19.2611 10.9242 19.3825C10.8739 19.5039 10.8002 19.6142 10.7073 19.7071C10.6144 19.8 10.5041 19.8737 10.3827 19.924C10.2613 19.9743 10.1312 20.0001 9.99981 20.0001C9.86841 20.0001 9.73831 19.9743 9.61691 19.924C9.49552 19.8737 9.38522 19.8 9.29231 19.7071L0.29231 10.7071C0.199334 10.6142 0.125575 10.5039 0.0752504 10.3825C0.0249261 10.2611 -0.000976562 10.131 -0.000976562 9.99958C-0.000976562 9.86816 0.0249261 9.73804 0.0752504 9.61664C0.125575 9.49524 0.199334 9.38495 0.29231 9.29208L9.29231 0.292079C9.47995 0.104439 9.73445 -0.000976564 9.99981 -0.000976562C10.2652 -0.000976561 10.5197 0.104439 10.7073 0.292079C10.895 0.47972 11.0004 0.734215 11.0004 0.999579C11.0004 1.26494 10.895 1.51944 10.7073 1.70708L3.41356 8.99958H22.9998C23.265 8.99958 23.5194 9.10494 23.7069 9.29247C23.8945 9.48001 23.9998 9.73436 23.9998 9.99958Z"
        fill="url(#paint0_linear_4270_2800)"
      />
      <defs>
        <linearGradient
          id="paint0_linear_4270_2800"
          x1="11.9994"
          y1="-0.000976563"
          x2="11.9994"
          y2="20.0001"
          gradientUnits="userSpaceOnUse"
        >
          <stop stop-color="#FFA033" />
          <stop offset="1" stop-color="#FF8800" />
        </linearGradient>
      </defs>
    </svg>
  );
};

export default BackIcon;
