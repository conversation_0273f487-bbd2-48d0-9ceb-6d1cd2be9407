console.log('Firebase service worker initializing');
importScripts(
  'https://www.gstatic.com/firebasejs/9.22.2/firebase-app-compat.js'
);
importScripts(
  'https://www.gstatic.com/firebasejs/9.22.2/firebase-messaging-compat.js'
);
console.log('Firebase scripts imported successfully');
const firebaseConfig = {
  apiKey: 'AIzaSyCjxer8-bL-kHzi6b12Oj3EpabcVqCCWxE',
  authDomain: 'rexconstruction-3cf18.firebaseapp.com',
  projectId: 'rexconstruction-3cf18',
  storageBucket: 'rexconstruction-3cf18.firebasestorage.app',
  messagingSenderId: '151808921700',
  appId: '1:151808921700:web:004fb8fe3426c96a790f71',
  measurementId: 'G-W3MQR4EZ5D',
};

firebase.initializeApp(firebaseConfig);

const messaging = firebase.messaging();
console.log('Firebase messaging initialized');
messaging.onBackgroundMessage((payload) => {
  console.log('Received background message:', payload);
  const { title, body } = payload.notification;

  self.registration.showNotification(title, {
    body,
    icon: '/firebase-logo.png',
  });
});
